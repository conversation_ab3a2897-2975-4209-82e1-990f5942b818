@mixin round-box($radius) {
    border-radius: $radius;
    box-shadow: 0px 0px 12px 0px rgba(30, 72, 182, 0.1);
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 24px;
}

.grid-container {
    display: grid;
    gap: 24px;
}

.grid-container-2 {
    display: grid;
    gap: 24px;
    grid-template-columns: repeat(2, 1fr);
}

.grid-container-3 {
    display: grid;
    gap: 24px;
    grid-template-columns: repeat(3, 1fr);
}

@mixin ellipsis($lines: 1) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    line-clamp: $lines;
}