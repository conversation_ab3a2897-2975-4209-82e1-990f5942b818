<template>
    <div class="card common-box">
        <div class="card-title">
            <div class="title-left">
                <img v-if="props.icon" :src="iconSrc" alt="" />
                <span>{{ props.title }}</span>
                <slot name="prefix"></slot>
            </div>
            <slot name="suffix"></slot>
        </div>
        <div class="card-content">
            <slot></slot>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

interface Props {
    icon?: string; // 图标
    title: string; // 标题
    width?: string; // 宽度
    height?: string; // 内容高度
}

const props = withDefaults(
    defineProps<Props>(),
    {
        icon: "",
        title: "暂无标题",
        width: "100%",
        height: "100%",
    }
);

const iconSrc = computed(
    () => new URL(`../assets/${props.icon}.png`, import.meta.url).href
);
</script>

<style lang="scss" scoped>
.card {
    @include round-box(10px);
    width: v-bind(width);
    background: linear-gradient(
        180deg,
        rgba(230, 242, 255, 0.7) 0%,
        rgb(255, 255, 255) 20%
    );
    background-color: #fff;
    .card-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        font-weight: bold;
        height: 64px;
        padding: 0 24px;
        img {
            width: 20px;
            height: 20px;
        }
        span {
            line-height: 1;
            margin: 0 8px;
        }
        .title-left {
            display: flex;
            align-items: center;
        }
    }
    .card-content {
        position: relative;
        height: v-bind(height);
    }
}
</style>
