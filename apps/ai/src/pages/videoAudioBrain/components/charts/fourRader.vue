<template>
    <div class="container">
        <div class="four-rader" id="fourRader"></div>
    </div>
</template>

<script setup lang="ts">
import { inject, Ref, ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";
import { emitter } from "@/utils/mitt";

const props = withDefaults(
    defineProps<{
        width?: string;
        height?: string;
    }>(),
    {
        width: "100%",
        height: "100%",
    }
);

const roleChats = inject<Ref<any>>("roleChats");
const raderData = ref<any[]>([]);
let myChart: any = null;

const initData = () => {
    if (roleChats?.value) {
        const roleChat3 = roleChats?.value[3] as any[];
        const data27 = roleChat3.find((item) => item.questionType === 27);
        if (data27) {
            let arr: any[] = [];
            const answer = (JSON.parse(data27.answer) as any[]) || [];
            const allTypes = ["若何", "为何", "是何", "如何"];
            answer.forEach((item) => {
                if (allTypes.includes(item["四何"])) {
                    arr.push(item["四何"]);
                }
            });
            const result = arr.reduce((acc, item) => {
                const existing = acc.find((entry: any) => entry.title === item);

                if (existing) {
                    existing.num += 1;
                } else {
                    acc.push({ title: item, num: 1 });
                }

                return acc;
            }, []);
            let arr2 = result.map((item: any) => {
                if (item.title == "若何") {
                    item.title = `若何 ${String(item.num).padStart(3, " ")}`;
                } else if (item.title == "为何") {
                    item.title = `为何 ${item.num}`;
                } else if (item.title == "是何") {
                    item.title = `是何 ${String(item.num).padStart(3, " ")}`;
                } else if (item.title == "如何") {
                    item.title = `如何 ${item.num}`;
                }
                return item;
            });
            // 确保所有四何类型都显示，即使num为0
            const completeArr = allTypes.map((type) => {
                const found = arr2.find((item: any) =>
                    item.title.startsWith(type)
                );
                if (found) return found;
                let title = "";
                if (type === "若何") title = "若何   0";
                else if (type === "为何") title = "为何 0";
                else if (type === "是何") title = "是何   0";
                else if (type === "如何") title = "如何 0";
                return { title, num: 0 };
            });
            raderData.value = completeArr;
            nextTick(() => {
                initEcharts();
            });
        }
    }
};

const initEcharts = () => {
    const numList = raderData.value.map((item: any) => item.num);
    const temp = numList[1];
    numList[1] = numList[3];
    numList[3] = temp;

    const max = numList.reduce((sum, num) => sum + num, 0);
    const nameList = raderData.value.map((item: any) => ({
        name: item.title,
        max,
    }));

    const chartDom = document.getElementById("fourRader")!;
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    let option: any;
    option = {
        radar: {
            name: {
                show: false, // 隐藏原始的指示器文字
            },
            indicator: nameList,
            axisLine: {
                lineStyle: {
                    color: "rgba(0,122,255,0.08)",
                },
            },
            splitLine: {
                show: true, // 隐藏分割线
            },
            splitArea: {
                areaStyle: {
                    color: [
                        "rgba(0,122,255, 0.5)",
                        "rgba(0,122,255, 0.4)",
                        "rgba(0,122,255,0.3)",
                        "rgba(0,122,255,0.2)",
                        "rgba(0,122,255,0.1)",
                    ],
                    shadowColor: "rgba(0,122,255,0.08);",
                    opacity: 1, //雷达图数据线段颜色
                },
            },
        },
        series: [
            {
                name: "Budget vs spending",
                type: "radar",
                data: [
                    {
                        value: numList,
                        name: "Allocated Budget",
                        itemStyle: {
                            opacity: 0,
                        },
                    },
                ],
                areaStyle: {
                    color: "#007AFF",
                    opacity: 1,
                },
            },
        ],
        graphic: [
            // 若何 - 上方
            {
                type: "text",
                left: "42%",
                top: "8%",
                style: {
                    text: nameList[0].name,
                    textAlign: "center",
                    fill: "#262626",
                    fontSize: 13,
                    cursor: "pointer",
                },
                onclick: function () {
                    const name = nameList[0].name.split(" ")[0];
                    // emitter.emit("four-rader-data", name);
                },
            },
            // 如何 - 左方
            {
                type: "text",
                right: "0%",
                top: "48%",
                style: {
                    text: nameList[1].name,
                    textAlign: "right",
                    fill: "#262626",
                    fontSize: 13,
                    cursor: "pointer",
                },
                onclick: function () {
                    const name = nameList[1].name.split(" ")[0];
                    // emitter.emit("four-rader-data", name);
                },
            },
            // 是何 - 下方
            {
                type: "text",
                left: "42%",
                bottom: "8%",
                style: {
                    text: nameList[2].name,
                    textAlign: "center",
                    fill: "#262626",
                    fontSize: 13,
                    cursor: "pointer",
                },
                onclick: function () {
                    const name = nameList[2].name.split(" ")[0];
                    // emitter.emit("four-rader-data", name);
                },
            },
            // 为何 - 右方
            {
                type: "text",
                left: "0%",
                top: "48%",
                style: {
                    text: nameList[3].name,
                    textAlign: "left",
                    fill: "#262626",
                    fontSize: 13,
                    cursor: "pointer",
                },
                onclick: function () {
                    const name = nameList[3].name.split(" ")[0];
                    // emitter.emit("four-rader-data", name);
                },
            },
        ],
    };

    option && myChart.setOption(option);
};

const handleResize = () => {
    if (myChart) {
        myChart.resize();
    }
};

onMounted(() => {
    initData();
    window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    if (myChart) {
        myChart.dispose();
        myChart = null;
    }
});
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.four-rader {
    width: v-bind(width);
    height: v-bind(height);
}
</style>
