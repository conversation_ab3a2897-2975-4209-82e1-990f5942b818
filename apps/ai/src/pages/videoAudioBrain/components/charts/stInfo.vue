<script lang="ts" setup>
import { ref, inject, Ref, watch, withDefaults } from "vue";
import { Static } from "@/pages/videoAudioBrain/entity";
import icon9 from "@/pages/videoAudioBrain/assets/icon9.svg";
import icon10 from "@/pages/videoAudioBrain/assets/icon10.png";

const props = withDefaults(
    defineProps<{
        stMode: number;
        width?: string;
        height?: string;
    }>(),
    {
        stMode: 1,
        width: "auto",
        height: "auto",
    }
);

const aiClassStData = inject<Ref<Static>>("aiClassStData");
const point = ref({
    x: "0",
    y: "0",
});
watch(
    () => aiClassStData?.value,
    (val) => {
        if (val) {
            if (Number(val.rtData) < 0 || Number(val.chData) < 0) {
                return;
            }
            point.value = {
                x: val.rtData,
                y: val.chData,
            };
        } else {
            point.value = {
                x: "0",
                y: "0",
            };
        }
    },
    {
        immediate: true,
    }
);
</script>

<template>
    <div class="st-content">
        <div class="st-info">
            <img v-if="props.stMode === 1" :src="icon9" alt="" />
            <img v-else :src="icon10" alt="" />
            <span class="dot dot-x-0">0</span>
            <span class="dot dot-x-4">0.3</span>
            <span class="dot dot-x-7">0.7</span>
            <span class="dot dot-x-10">1</span>

            <span class="dot dot-y-0">0</span>
            <span class="dot dot-y-4">0.4</span>
            <span class="dot dot-y-10">1</span>

            <div
                class="rt-dot"
                :style="{
                    left: `calc(${Number(point.x) * 100}% - 6px)`,
                    bottom: `calc(${Number(point.y) * 100}% - 6px)`,
                }"
            >
                <div class="rt-value">
                    <div>Ch={{ point.y }}</div>
                    <div>Rt={{ point.x }}</div>
                </div>
            </div>
        </div>
        <!-- <div class="title">Rt-Ch图</div> -->
        <div class="rt">Rt</div>
        <div class="ch">Ch</div>
    </div>
</template>

<style lang="scss" scoped>
.st-content {
    width: v-bind(width);
    height: 0;
    padding-top: v-bind(height);
    position: relative;
    left: 10px;
    .st-mode {
        position: absolute;
        top: -10px;
        right: 0;
    }
}
.st-info {
    position: absolute;
    left: 16px;
    top: 16px;
    right: 16px;
    bottom: 16px;
    border-left: 1px solid #bfbfbf;
    img {
        width: 100%;
        height: 100%;
    }
}
.dot {
    position: absolute;
    color: #8c8c8c;
    &-y-0 {
        left: -20px;
        bottom: -8px;
    }

    &-y-4 {
        left: -30px;
        bottom: 38%;
    }

    &-y-10 {
        left: -20px;
        top: 0px;
    }

    &-x-0 {
        left: 0;
        bottom: -26px;
    }

    &-x-4 {
        left: 28%;
        bottom: -26px;
    }

    &-x-7 {
        right: 28%;
        bottom: -26px;
    }

    &-x-10 {
        right: 0;
        bottom: -26px;
    }
}
@keyframes rippleAnimation {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 132, 255, 0.7);
    }
    100% {
        box-shadow: 0 0 0 15px rgba(76, 132, 255, 0);
    }
}
.rt-dot {
    position: absolute;
    width: 12px;
    height: 12px;
    background: rgba(76, 132, 255, 1);
    border-radius: 50%;
    text-align: center;
    animation: rippleAnimation 1s infinite;
    &:hover .rt-value {
        display: block;
    }

    .rt-value {
        display: none;
        position: absolute;
        right: 24px;
        top: 0;
        color: #fff;
        width: 80px;
        height: 56px;
        background: rgba(49, 53, 75, 0.9);
        border-radius: 4px;
        padding-top: 7px;
        z-index: 9;

        div {
            line-height: 20px;
        }
    }
}
/* ==== */
.title {
    text-align: center;
    position: relative;
    top: 30px;
}
.ch {
    position: absolute;
    top: 50%;
    left: -30px;
    transform: translateY(-50%) rotate(270deg);
}
.rt {
    position: absolute;
    left: 50%;
    bottom: -28px;
    transform: translate(-50%);
}
</style>
