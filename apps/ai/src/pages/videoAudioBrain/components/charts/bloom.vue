<template>
    <div class="container">
        <div class="bloom" id="bloom"></div>
    </div>
</template>

<script setup lang="ts">
import { inject, onMounted, onUnmounted, ref, Ref, nextTick } from "vue";
import * as echarts from "echarts";
import { emitter } from "@/utils/mitt";

const props = withDefaults(
    defineProps<{
        width?: string;
        height?: string;
    }>(),
    {
        width: "100%",
        height: "100%",
    }
);

const roleChats = inject<Ref<any>>("roleChats");
const bloomData = ref<any[]>([]);
let myChart: any = null;

const initData = () => {
    if (roleChats?.value) {
        const roleChats3 = roleChats?.value[3];
        const find27 = roleChats3.find(
            (item: { questionType: number }) => item.questionType == 27
        );
        if (find27) {
            const answer = (JSON.parse(find27.answer) as any[]) || [];

            let countMap: any = {
                记忆: 0,
                理解: 0,
                应用: 0,
                分析: 0,
                评价: 0,
                创造: 0,
            };
            answer.forEach((item) => {
                const text = item["布鲁姆"];
                countMap[text]++;
            });
            let result: any[] = Object.entries(countMap).map(
                ([text, num]) => num
            );
            bloomData.value = result;

            nextTick(() => {
                initEcharts();
            });
        }
    }
};

const initEcharts = () => {
    const chartDom = document.getElementById("bloom")!;
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    let option: any;
    option = {
        title: {
            textStyle: {
                fontSize: 14,
            },
        },
        xAxis: {
            type: "category",
            data: ["记忆", "理解", "应用", "分析", "评价", "创造"],
        },
        yAxis: {
            type: "value",
        },
        series: [
            {
                data: bloomData.value,
                type: "bar",
                label: {
                    show: true,
                    position: "top",
                    fontSize: 14,
                    color: "#262626",
                    formatter: function (params: any) {
                        return params.value > 0 ? params.value : "";
                    },
                },
                itemStyle: {
                    borderRadius: [4, 4, 0, 0], // 设置柱子顶部圆角
                    color: function (params: any) {
                        // 为每个柱子定义不同的渐变色
                        const colorList = [
                            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(143,123,211,0.6)" },
                                { offset: 1, color: "#8F7BD3" },
                            ]),
                            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(74,180,208,0.6)" },
                                { offset: 1, color: "#4AB4D0" },
                            ]),
                            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(89,190,127,0.6)" },
                                { offset: 1, color: "#59BE7F" },
                            ]),
                            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(250,204,11,0.6)" },
                                { offset: 1, color: "#FACC0B" },
                            ]),
                            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(244,138,46,0.6)" },
                                { offset: 1, color: "#F48A2E" },
                            ]),
                            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: "rgba(241,88,78,0.6)" },
                                { offset: 1, color: "#F1584E" },
                            ]),
                        ];
                        return colorList[params.dataIndex];
                    },
                },
            },
        ],
    };

    option && myChart.setOption(option);

    // myChart.on("click", function (params) {
    //     emitter.emit("bloom-data", params.name);
    // });
};

const handleResize = () => {
    if (myChart) {
        myChart.resize();
    }
};

onMounted(() => {
    initData();
    if (myChart) {
        myChart.dispose();
        myChart = null;
    }
});

onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    if (myChart) {
        myChart.dispose();
        myChart = null;
    }
});
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.bloom {
    width: v-bind(width);
    height: v-bind(height);
}
</style>
