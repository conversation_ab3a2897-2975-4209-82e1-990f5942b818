<template>
    <a-modal
        v-model:open="visible"
        :title="null"
        :footer="null"
        :width="600"
        :centered="true"
        :destroyOnClose="true"
        wrapClassName="more-modal-wrap"
        @cancel="handleClose"
    >
        <div class="more-modal-content">
            <div class="modal-title">{{ props.title }}</div>
            <div class="modal-body" v-html="props.content"></div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
    open: boolean; // 弹窗显示状态
    title: string; // 弹窗标题
    content: string; // 弹窗内容
}

const props = withDefaults(defineProps<Props>(), {
    open: false,
    title: '',
    content: '',
});

const emit = defineEmits<{
    'update:open': [value: boolean];
}>('update:open');

const visible = ref(props.open);

// 监听props.open变化
watch(
    () => props.open,
    (newVal) => {
        visible.value = newVal;
    },
    { immediate: true }
);

// 关闭弹窗
const handleClose = () => {
    visible.value = false;
    emit('update:open', false);
};
</script>

<style scoped lang="scss">

</style>