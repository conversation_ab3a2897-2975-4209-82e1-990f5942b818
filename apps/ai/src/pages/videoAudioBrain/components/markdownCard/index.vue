<template>
    <commonCard
        :icon="props.icon"
        :title="props.title"
        :width="props.width"
        :height="props.height"
    >
        <div
            class="markdown-body"
            v-html="markdownContent"
            :style="{ '--lines': props.lines }"
        ></div>
        <span class="more">更多内容 @click="showModal = true"></span>
    </commonCard>

    <moreModal v-model:open="showModal"
    :title="props.title"
    :content="markdownContent" />
</template>

<script lang="ts" setup>
import { computed } from "vue";
import commonCard from "@/pages/videoAudioBrain/components/commonCard.vue";
import moreModal from "./moreModal.vue";

interface Props {
    icon?: string; // 图标
    title: string; // 标题
    width?: string; // 宽度
    height?: string; // 内容高度
    content: string; // 内容
    lines?: number; // 展示总行数（超出总行数展示省略号）
}

const props = withDefaults(defineProps<Props>(), {
    icon: "",
    title: "",
    width: "100%",
    height: "100%",
    content: "",
    lines: 1,
});

const markdownContent = computed(() => {
    return props.content || "暂无内容";
});

const showModal = ref<boolean>(false);
</script>

<style lang="scss" scoped>
.markdown-body {
    display: -webkit-box;
    -webkit-line-clamp: var(--lines, 1);
    line-clamp: var(--lines, 1);
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 24px;
    line-height: 23px;
}
.more {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    color: #007aff;
    cursor: pointer;
}
</style>
