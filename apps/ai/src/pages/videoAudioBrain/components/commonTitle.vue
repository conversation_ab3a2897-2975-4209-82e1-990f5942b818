<template>
    <div class="common-title">
        <span>
            {{ title }}
        </span>
    </div>
</template>

<script lang="ts" setup>
interface Props {
    title: string; // 标题
}

const props = withDefaults(defineProps<Props>(), {
    title: "暂无标题",
});
</script>

<style lang="scss" scoped>
.common-title {
    text-align: center;
    margin: 24px 0;
    span {
        font-size: 18px;
        font-weight: bold;
        background: url("@/pages/videoAudioBrain/assets/common_title_bg.png")
            no-repeat center bottom;
        background-size: 55%;
    }
}
</style>
