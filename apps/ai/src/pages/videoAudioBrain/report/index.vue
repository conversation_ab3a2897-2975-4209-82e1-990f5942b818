<script lang="ts" setup>
import { ref, watch, computed, provide, onUnmounted, reactive, Ref, inject } from "vue";
import { ysIcon } from "@ys/ui";
import { common, CommonRes } from "@ys/tools";
import { useRoute, useRouter } from "vue-router";
import { selectResourceDetailInfo, selectVisitEventInfo, selectEventAiTaskGroups, exportAiReport, exportAiReportNew, queryAiTaskGroup, queryAiTaskGroupSmallClass } from "../api";
const { getUrlQuery } = common;
import qrcodeVue from "qrcode.vue";
import { useClipboard } from '@vueuse/core'
import { message } from "ant-design-vue";
import {
  AiCaptionTask,
  AiClassActionData,
  AiClassActionDataVo,
  AiTaskGroupVo,
  MergedSegmentProps,
  AiClassBehaviorData
} from "./../entity";
import { isJson } from "@/utils";
import html2canvas from "html2canvas";
const { isCdn, down } = common;

const route = useRoute();
const router = useRouter();

const isInIframe = window !== window.top;

const aiTaskGroupVo = ref<AiTaskGroupVo>();
const aiClassStData = ref<any>({});
const aiCaptionTask = ref<AiCaptionTask>();
const roleChats = ref<any>([]);
const listFormItem = ref<any[]>([]);
const aiClassActionData = ref<AiClassActionData>();
const aiClassActionDataVo = ref<AiClassActionDataVo>();
const mergedSegments = ref<MergedSegmentProps[]>();
const aiClassBlackboardData = ref<any>();
const aiClassSpeakData = ref<any>();
const aiClassBehaviorData = ref<AiClassBehaviorData>();

provide("aiTaskGroupVo", aiTaskGroupVo);
provide("aiClassStData", aiClassStData);
provide("aiCaptionTask", aiCaptionTask);
provide("roleChats", roleChats);
provide("listFormItem", listFormItem);
provide("aiClassActionData", aiClassActionData);
provide("aiClassActionDataVo", aiClassActionDataVo);
provide("mergedSegments", mergedSegments);
provide("aiClassBlackboardData", aiClassBlackboardData);
provide("aiClassSpeakData", aiClassSpeakData);
provide("aiClassBehaviorData", aiClassBehaviorData);

const title = ref("");
const captionOpenSpeaker = ref(1);
const objType = ref<number>(1); // 1 活动 2 资源
const aiAuth = ref<number>(1); // 1 教师 2 学生
const classroomType = ref(2); // 1 普通 2 微格 3 电影
const subjectName = ref("");

provide("classroomType", classroomType);
provide("aiAuth", aiAuth);
provide("objType", objType);
provide("captionOpenSpeaker", captionOpenSpeaker);
provide("subjectName", subjectName);

const navItemList = ref([
  {
    title: "概览",
    type: 1
  },
  {
    title: "课堂行为",
    type: 2
  },
  {
    title: "教学内容",
    type: 3
  },
  {
    title: "教学建议",
    type: 6
  },
  {
    title: "教学分析",
    type: 4
  },
  {
    title: "师训专家分析",
    type: 5
  },
  {
    title: "同课异构",
    type: 7
  },
]) // 导航菜单

const navList = computed(() => {
  if (classroomType.value != 1 && classroomType.value != 3) {
    navItemList.value = navItemList.value.filter((item) => item.type !== 2);
  }
  const roleChat3 = roleChats.value[3] as any[];
  const data18 = roleChat3.find((item) => item.questionType === 18);
  const data24 = roleChat3.find((item) => item.questionType === 24);
  const data32 = roleChat3.find((item) => item.questionType === 32);
  if (!isJson(data18?.answer) && !isJson(data24?.answer) && !isJson(data32?.answer)) {
    navItemList.value = navItemList.value.filter((item) => item.type !== 6);
  }
  return navItemList.value;
});

const currentMenu = ref(1);
const groupId = ref<string>(""); // 选中分析任务id
const taskGroupList = ref<aiTaskGroupItemType[]>([]); // 分析任务列表

async function getInfo2() {
  // if (!route.query.groupId) return;
  // resetLoading.value = false;
  let result = null;
  if (classroomType.value == 1 || classroomType.value == 3) {
    result = await queryAiTaskGroup<CommonRes<any>>({
      groupId: route.query.groupId,
    });
  } else {
    result = await queryAiTaskGroupSmallClass<CommonRes<any>>({
      groupId: route.query.groupId,
    });
  }
  if (result.data.code == 0) {
    groupId.value = route.query.groupId as string;
    // resetLoading.value = true;
    const data = result.data.data;
    // hasResult.value = true;
    aiClassStData.value = data.aiClassStData;
    aiCaptionTask.value = data.aiCaptionTask;
    roleChats.value = data.roleChats;
    listFormItem.value = data.listFormItem;
    aiTaskGroupVo.value = data.aiTaskGroupVo;
    aiClassActionData.value = data.aiClassActionData;
    aiClassActionDataVo.value = data.aiClassActionDataVo;
    mergedSegments.value = data.mergedSegments;
    document.title = aiTaskGroupVo.value?.groupName || "影像大脑";
    aiClassBlackboardData.value = data.aiClassBlackboardData;
    aiClassSpeakData.value = data.aiClassSpeakData;
    aiClassBehaviorData.value = data.aiClassBehaviorData;
  }
}

/**
 * 切换分析任务
 * @param val 任务id
 */
const onChangeGroupId = async (val: string) => {
  currentMenu.value = 1;
  const id = route.params.id;
  const objType = route.params.objType;
  const aiAuth = route.params.aiAuth;
  const from = route.query.from;
  const taskType = taskGroupList.value.find(
    (item) => item.id === val
  )!.taskType;
  await router.replace(
    `/active-ai/${id}/${objType}/${aiAuth}?groupId=${val}&from=${from}&taskType=${taskType}`
  );
  docUrl.value = "";
};
// 获取分析任务
const getTaskGroup = async () => {
  try {
    const response = await selectEventAiTaskGroups({
      eventId: route.params.id,
    });
    const res = response.data as any;
    if (res.code == 0) {
      taskGroupList.value = res.data.filter(
        (item: aiTaskGroupItemType) => {
          return item.taskGroupStatus == 1;
        }
      );
    }
  } catch (error) {
    console.error("selectEventAiTaskGroups", error);
  }
}

watch(
  () => route.query.groupId,
  (val) => {
    if (val) {
      getInfo2();
      groupId.value = val as string;
      const type = Number(route.params.objType);
      const id = route.params.id;
      // 资源中心传过来会为0
      if (type == 1 && id !== "0") {
        getTaskGroup();
      }
    }
  },
  { immediate: true }
);

// 二维码相关
const codeOption = reactive({
  value: "",
  size: 180,
  level: "H",
});
const docUrl = ref<string>("");
const { copy, isSupported, copied } = useClipboard({ source: codeOption.value, legacy: true });
const isExport = ref<boolean>(false); // 是否正在导出
interface ExportImg {
  type: string;
  img: Blob;
}

// 复制
const handle2Copy = () => {
  if (isSupported) {
    copy(codeOption.value);
    if (copied) {
      message.success('复制成功');
    } else {
      message.error('复制失败');
    }
  } else {
    message.error('复制失败');
  }
}

// 生成报告H5链接
const handleExportH5 = () => {
  const href = window.location.href;
  const h5Href = href.replace(/\/active-ai/, "/active-ai-h5");
  codeOption.value = h5Href + "&tenantId=" + (JSON.parse(localStorage.getItem("tempTenant") as any).id || 0) + "&url=" + docUrl.value;
};

// 导出
const handle2Export = async () => {
  if (aiTaskGroupVo.value?.reportFileCreated) {
    docUrl.value = aiTaskGroupVo.value?.reportFileUrl || "";
  }

  isExport.value = true;
  if (docUrl.value) {
    down(isCdn(docUrl.value), "分析报告.docx");
    isExport.value = false;
    return;
  }

  try {
      // 延迟执行以确保加载状态显示
      await new Promise(resolve => setTimeout(resolve, 100));

      const exportImg = Array.from(document.querySelectorAll<HTMLElement>(".doc .export-img"));
      // 批处理相关变量
      const batchSize = 4; // 每批处理4个元素
      const allResults: ExportImg[] = [];

      // 分批处理图片
      for (let i = 0; i < exportImg.length; i += batchSize) {
        const batch = exportImg.slice(i, i + batchSize);
        const batchPromises = batch.map((child) => {
          return new Promise<ExportImg>((resolve) => {
            html2canvas(child as HTMLElement, {
              useCORS: true,
              allowTaint: false,
              scale: 1,
            }).then(function (canvas) {
              canvas.toBlob(function(blob) {
                if (blob) {
                  resolve({
                    type: child.getAttribute('data-type') as string,
                    img: blob
                  });
                } else {
                  const emptyBlob = new Blob([''], { type: 'image/png' });
                  resolve({
                    type: child.getAttribute('data-type') as string,
                    img: emptyBlob
                  });
                }
              }, 'image/png');
            }).catch(error => {
                // 发生错误时也要 resolve，避免阻塞流程
                const emptyBlob = new Blob([''], { type: 'image/png' });
                resolve({
                  type: child.getAttribute('data-type') as string,
                  img: emptyBlob
                });
            });
          });
        });

        // 等待当前批次完成并合并结果
        const batchResults = await Promise.all(batchPromises);
        allResults.push(...batchResults);
      }
    try {
      // 创建 FormData 对象
      const formData = new FormData();
      formData.append('groupId', route.query.groupId as string);

      // 将每个 Blob 添加到 FormData 中
      allResults.forEach((item: any) => {
        // 将 Blob 转换为 File 对象以便设置文件名
        const file = new File([item.img], `${item.type}.png`, { type: 'image/png' });
        formData.append(item.type, file);
      });
      const exportApi = aiClassBehaviorData.value ? exportAiReportNew : exportAiReport;
      const res = await exportApi<CommonRes<any>>(formData);
      if (res.data.code == 0) {
        docUrl.value = res.data.data;
        down(isCdn(docUrl.value), "分析报告.docx");
      } else {
        message.error(res.data.msg);
      }
    }
    catch (error) {
      console.error('报告导出失败', error);
    }
  } catch (error) {
    console.error('图片处理出错', error);
  } finally {
    isExport.value = false;
  }
};

function handle2Back() {
  const bureauId = getUrlQuery("bureauId");
  const jd = getUrlQuery("jd");
  const id = route.params.id;
  if (objType.value == 2) {
    const suffixId = sessionStorage.getItem("suffixId");
    let url = `/yskt/a/#/resource/Layout/resource-list/${id}?suffixId=${suffixId}&bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  } else if (objType.value == 1) {
    let url = `/yskt/ys/active-player/#/${id}?bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  }
}

function handle3Back() {
  const bureauId = getUrlQuery("bureauId");
  const jd = getUrlQuery("jd");
  const from = route.query.from;
  if (from == "courseAnalyse1") {
    router.replace(`/ai-course/index`);
  } else if (from == "courseAnalyse2") {
    router.replace(`/analyse-task/course-analyse`);
  } else if (from == "researchPlan") {
    let url = `/yskt/ys/research-online/#/researchPlan?bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  } else if (from == "researchResources") {
    let url = `/yskt/ys/research-online/#/researchResources?bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  }
}

function handle2change(type: "1" | "2") {
  const groupId = route.query.groupId;
  const from = route.query.from;
  const id = route.params.id;
  const objType = route.params.objType;
  const aiAuth = route.params.aiAuth;
  const taskType = route.query.taskType;
  if (type === "1") {
    router.push(
      `/active-ai/${id}/${objType}/${aiAuth}?groupId=${groupId}&from=${from}&taskType=${taskType}`
    );
  } else {
    router.push(
      `/active-ai/class-diff/${id}/${objType}/${aiAuth}?groupId=${groupId}&from=${from}&taskType=${taskType}`
    );
  }
}

async function getEventDetailInfo() {
  const result = await selectVisitEventInfo<CommonRes<any>>({
    eventId: route.params.id,
  });
  if (result.data.code === 0) {
    title.value = result.data.data.eventInfo.eventName;
    captionOpenSpeaker.value =
      result.data.data.eventOtherInfo.captionOpenSpeaker;
    subjectName.value = result.data.data.eventOtherInfo.subjectName || "";

    // document.title = title.value; // 设置页面标题
  }
}

async function getResourceDetailInfo() {
  const result = await selectResourceDetailInfo<CommonRes<any>>(
    route.params.id
  );
  if (result.data.code === 0) {
    const data = result.data.data;
    title.value = data.resResource.resourceName;
    document.title = title.value; // 设置页面标题
  }
}

const currentPath = ref("");
const active = computed(() => {
  return currentPath.value.indexOf("class-diff") === -1;
});
watch(
  () => route.path,
  (val) => {
    currentPath.value = val;
    objType.value = Number(route.params.objType);
    aiAuth.value = Number(route.params.aiAuth);
    const suffixId = getUrlQuery("suffixId");
    if (suffixId) {
      sessionStorage.setItem("suffixId", suffixId);
    }
    if (objType.value == 2) {
      // 资源详情
      // getResourceDetailInfo();
    } else if (objType.value == 1) {
      // 活动详情
      const id = route.params.id;
      // 资源中心传过来会为0
      if (id !== "0") {
        getEventDetailInfo();
      }
    }
  },
  { immediate: true }
);
watch(
  () => route.query,
  (val) => {
    classroomType.value = Number(route.query.taskType);
  },
  { immediate: true }
);

onUnmounted(() => {
  document.title = "影像大脑";
});
</script>

<template>
  <div class="ai-brain-report">
    <div class="head" v-if="!isInIframe">
      <div class="head-container">
        <div class="head-title">
          <img
            src="@/pages/videoAudioBrain/assets/robot.svg"
            width="28"
            alt=""
          />
          <span>影像大脑</span>
        </div>
        <div class="nav" v-if="aiAuth == 1">
          <!-- <div @click="handle2change('1')" :class="['nav-item', { active }]">
            课程分析
          </div>
          <div
            @click="handle2change('2')"
            :class="['nav-item', { active: !active }]"
          >
            同课异构
          </div> -->
          <div
            v-for="(item, index) in navList"
            :key="index"
            :class="['nav-item', { active: !active }]"
          >
            {{ item.title }}
          </div>
        </div>

        <a-space :size="16">
          <a-select
            :value="groupId"
            style="width: 240px"
            class="group-select"
            placeholder="请选择分组"
            @change="onChangeGroupId"
          >
            <a-select-option
              :value="item.id"
              v-for="item in taskGroupList"
              :key="item.id"
            >
              <h3>{{ item.groupName }}</h3>
              <p
                class="text-overflow"
                :title="item.teacherViewName"
                v-if="item.teacherViewName"
              >
                教师画面：{{ item.teacherViewName }}
              </p>
              <p
                class="text-overflow"
                :title="item.studentViewName"
                v-if="item.studentViewName"
              >
                学生画面：{{ item.studentViewName }}
              </p>
            </a-select-option>
          </a-select>
          <a-popover trigger="click" placement="bottomRight">
            <template #content>
              <div class="qrcode-popover">
                <qrcode-vue
                  v-if="codeOption.value"
                  id="qrcode"
                  :value="codeOption.value"
                  :size="codeOption.size"
                />
                <p style="display: flex;justify-content: space-between;">
                  <a-input v-model:value="codeOption.value" disabled placeholder="分享地址" style="width: 200px;margin-right: 20px;" />
                  <a-button type="primary" @click="handle2Copy">复制</a-button>
                </p>
                <a-button :loading="isExport" style="width: 100%;" @click="handle2Export">导出报告</a-button>
              </div>
            </template>
            <div class="share-btn" @click="handleExportH5">分享</div>
          </a-popover>
          <div
            v-if="
              route.query.from == 'courseAnalyse1' ||
              route.query.from == 'courseAnalyse2' ||
              route.query.from == 'researchPlan' ||
              route.query.from == 'researchResources'
            "
            @click="handle3Back"
            class="box"
          >
            <ysIcon class="icon" type="icon-close" />
          </div>
          <div v-else @click="handle2Back" class="box">
            <ysIcon class="icon" type="icon-close" />
          </div>
        </a-space>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<style lang="scss" scoped>
.active {
  color: #2997ff;
  border-color: #2997ff;
}
.ai-brain-report {
  min-width: 1440px;
  padding-bottom: 32px;
  background: url('@/pages/videoAudioBrain/assets/container_bg.png') no-repeat center center / cover;
  .head {
    height: 64px;
  }

  .head-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    // width: 97%;
    margin: 0 32px;
    // max-width: 1520px;

    .head-title {
      font-size: 18px;
      font-weight: 700;
      span {
        margin-left: 8px;
      }
    }

    .nav {
      .nav-item {
        cursor: pointer;
        display: inline-block;
        font-size: 16px;
        height: 32px;
        // width: 88px;
        border-radius: 30px;
        text-align: center;
        line-height: 32px;
        background: transparent;
        margin: 0 22px;
      }
      .nav-item.active,
      .nav-item:hover {
        position: relative;
        color: #007AFF;
        font-weight: bold;
        &::after {
          content: "";
          position: absolute;
          right: -5px;
          bottom: 0;
          display: block;
          width: 21px;
          height: 9px;
          background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='21' height='9' viewBox='0 0 21 9'><defs><linearGradient id='g' x1='0' y1='0' x2='21' y2='0' gradientUnits='userSpaceOnUse'><stop offset='0%' stop-color='%23CD4ECA'/><stop offset='30%' stop-color='%23CD4ECA'/><stop offset='70%' stop-color='%2310BBDE'/><stop offset='100%' stop-color='%2310BBDE'/></linearGradient></defs><path d='M1 8 C7 2, 14 2, 20 8' stroke='url(%23g)' stroke-width='4' fill='none' stroke-linecap='round'/></svg>") center/contain no-repeat;
        }
      }
    }

    .group-select {
      :deep(.ant-select-selector) {
        @include round-box(20px);
        border: none;
      }
    }
    .share-btn {
      @include round-box(20px);
      padding: 7px 16px;
      background: #ffffff;
      cursor: pointer;
    }
    .box {
      @include round-box(50%);
      cursor: pointer;
      width: 36px;
      height: 36px;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .icon {
        color: #8C8C8C;
        font-size: 16px;
      }
    }
  }
}
.qrcode-popover {
  width: 300px;
  text-align: center;
  padding: 0 10px;
  p {
    margin-top: 15px;
  }
}
</style>
