<template>
    <div class="overview">
        <div class="flex-between">
            <!-- s-t & Rt-Ch分析 -->
            <commonCard
                icon="classroom"
                title="S-T & Rt-Ch分析"
                width="50%"
                height="360px"
            >
                <template #prefix>
                    <a-tag color="green">混合型</a-tag>
                </template>
                <template #suffix>
                    <a-radio-group v-model:value="stMode">
                        <a-radio-button :value="1">标准</a-radio-button>
                        <a-radio-button :value="2">常模</a-radio-button>
                    </a-radio-group>
                </template>
                <div class="st-conatiner">
                    <ysEmpt v-if="!aiClassStData?.stData" />
                    <div class="st" v-else>
                        <stLine width="300px" height="300px" />
                        <stInfo :stMode="stMode" width="300px" height="300px" />
                    </div>
                </div>
            </commonCard>
            <div class="flex-between" style="width: 50%">
                <!-- 布鲁姆问题分析 -->
                <commonCard
                    icon="blm"
                    title="布鲁姆问题分析"
                    width="50%"
                    height="360px"
                >
                    <ysEmpt v-if="!roleChats" />
                    <bloom v-else />
                </commonCard>

                <!-- “四何”问题分析 -->
                <commonCard
                    icon="shfx"
                    title="“四何”问题分析"
                    width="50%"
                    height="360px"
                >
                    <ysEmpt v-if="!roleChats" />
                    <fourRader v-else width="350px" />
                </commonCard>
            </div>
        </div>

        <div class="flex-between" style="margin-top: 24px">
            <!-- 教学方法 -->
            <commonCard
                icon="classroom"
                title="教学方法"
                width="50%"
                height="144px"
            >
                <ysEmpt v-if="!methodList.length" />
                <div v-else class="method">
                    <div class="method-item" v-for="item in methodList">
                        <img :src="setMethodIcon(item)" />
                        <span>{{ item }}</span>
                    </div>
                </div>
            </commonCard>

            <!-- 教师发言情况 -->
            <commonCard
                icon="classroom"
                title="教师发言情况"
                width="50%"
                height="144px"
            >
                <div class="speech">
                    <div class="speech-item">
                        <span class="title">
                            {{ teachaerTotalMinutes }}
                        </span>
                        <span>教师发言时长/分</span>
                        <img src="../../assets/speech1.png" alt="" />
                    </div>
                    <div class="speech-item">
                        <span class="title"> {{ trackLeft }}% </span>
                        <span>教师发言占比</span>
                        <img src="../../assets/speech2.png" alt="" />
                    </div>
                    <div class="speech-item">
                        <span class="title">
                            {{ fluency }}
                        </span>
                        <span>流利度</span>
                        <img src="../../assets/speech3.png" alt="" />
                    </div>
                    <div class="speech-item">
                        <span class="title">
                            {{ ktcWords }}
                        </span>
                        <span>口头禅/次</span>
                        <img src="../../assets/speech4.png" alt="" />
                    </div>
                    <div
                        v-if="
                            classroomType == 3 && remoteInteraction['指数评级']
                        "
                        class="speech-item"
                    >
                        <span class="title">
                            {{ remoteInteraction["指数评级"] }}
                        </span>
                        <span>
                            互动指数
                            <a-tooltip
                                placement="bottom"
                                color="white"
                                :overlayStyle="{ maxWidth: '520px' }"
                            >
                                <template #title>
                                    <h4>远端互动详细分析</h4>
                                    <div
                                        v-html="remoteText"
                                        style="
                                            font-size: 14px;
                                            color: #262626;
                                            line-height: 20px;
                                        "
                                    ></div>
                                </template>
                                <ys-icon
                                    class="iconfont"
                                    type="iconwenjian2"
                                    style="
                                        font-size: 18px;
                                        color: #007aff;
                                        cursor: pointer;
                                    "
                                />
                            </a-tooltip>
                        </span>
                        <img src="../../assets/speech5.png" alt="" />
                    </div>
                </div>
            </commonCard>
        </div>

        <!-- 课堂分析概述 -->
        <commonTitle title="课堂分析概述" />
        <div class="grid-container-3">
            <markdownCard
                icon="classroom"
                title="课程综评"
                height="144px"
                :content="html2"
                :lines="5"
            />
            <markdownCard
                icon="classroom"
                title="老师教学方法中值得称赞的地方"
                height="144px"
                :content="html2"
                :lines="5"
            />
            <markdownCard
                icon="classroom"
                title="老师在课堂上展现的教学亮点"
                height="144px"
                :content="html2"
                :lines="5"
            />
            <markdownCard
                icon="classroom"
                title="学生可以得到的学习体验和启发"
                height="144px"
                :content="html2"
                :lines="5"
            />
            <markdownCard
                icon="classroom"
                title="核心素养落实情况"
                height="144px"
                :content="html3"
                :lines="5"
            />
            <markdownCard
                icon="classroom"
                title="新课标要求落实情况"
                height="144px"
                :content="html4"
                :lines="5"
            />
        </div>

        <!-- <div style="margin-top: 24px" class="title2">布鲁姆问题分析</div>
        <div
            v-show="!bloomQuestionList.length"
            style="position: relative; height: 75px"
        >
            <ysEmpt />
        </div>
        <div class="bloom">
            <div
                class="bloom-item"
                v-for="(item, index) in bloomQuestionList"
                :key="index"
            >
                <ys-icon
                    class="icon"
                    :type="item.icon as string"
                    :style="{ fontSize: '28px', color: item.color }"
                />
                <span style="margin-top: 12px">{{ item.text }}</span>
                <span class="num">{{ item.num }}</span>
            </div>
        </div>

        <div style="margin-top: 24px" class="title2">“四何”问题分析</div>
        <div
            v-show="!fourRaderQuestionList.length"
            style="position: relative; height: 75px"
        >
            <ysEmpt />
        </div>
        <div class="four-rader">
            <div
                class="four-rader-item"
                v-for="(item, index) in fourRaderQuestionList"
                :key="index"
            >
                <span class="english" :style="{ color: item.color }">{{
                    item.englishText
                }}</span>
                <span style="font-weight: bold">{{ item.text }}</span>
                <span class="num">{{ item.num }}</span>
            </div>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, Ref, ref, watch } from "vue";
import { ysEmpt, ysIcon } from "@ys/ui";
import commonCard from "@/pages/videoAudioBrain/components/commonCard.vue";
import commonTitle from "@/pages/videoAudioBrain/components/commonTitle.vue";
import markdownCard from "@/pages/videoAudioBrain/components/markdownCard/index.vue";
import stLine from "@/pages/videoAudioBrain/components/charts/stLine.vue";
import stInfo from "@/pages/videoAudioBrain/components/charts/stInfo.vue";
import bloom from "@/pages/videoAudioBrain/components/charts/bloom.vue";
import fourRader from "@/pages/videoAudioBrain/components/charts/fourRader.vue";
import icon1 from "./icon1.png";
import icon2 from "./icon2.png";
import icon3 from "./icon3.png";
import icon4 from "./icon4.png";
import icon5 from "./icon5.png";
import icon6 from "./icon6.png";
import icon7 from "./icon7.png";
import icon8 from "./icon8.png";
import icon9 from "./icon9.png";
import { MergedSegmentProps, Static } from "@/pages/videoAudioBrain/entity";
import { marked } from "marked";
import { isJson } from "@/utils";

const aiClassStData = inject<Ref<Static>>("aiClassStData");
const mergedSegments = inject<Ref<MergedSegmentProps[]>>("mergedSegments");
const roleChats = inject<Ref<any>>("roleChats");
const classroomType = inject<Ref<number>>("classroomType");

const teachaerTotalMinutes = ref("0");
const fluency = ref("");
const teachaerAverageSpeed = ref("0");
const ktcWords = ref(0);

const methodList = ref<string[]>([]);
const methodRemain = ref(0);

const setMethodList = (value: any[]) => {
    let arr: string[] = [];
    value.forEach((item) => {
        if (item["教学方式"] != "无") {
            arr.push(item["教学方式"]);
        }
    });
    let arr2: any = [];
    arr.forEach((item) => {
        if (item.indexOf("/") != -1) {
            let obj_arr = item.split("/");
            obj_arr.forEach((item2) => {
                arr2.push(item2);
            });
        } else if (item.indexOf("、") != -1) {
            let obj_arr = item.split("、");
            obj_arr.forEach((item2) => {
                arr2.push(item2);
            });
        } else {
            arr2.push(item);
        }
    });
    arr2 = Array.from(new Set(arr2));
    methodList.value = arr2;
    methodRemain.value = computedRowToRemain(arr2.length, 5);
};

function computedRowToRemain(length: number, row: number): number {
    const flag = length % row;
    return flag ? row - (length % row) : 0;
}

const setMethodIcon = (val: string) => {
    if (val == "讲授") {
        return icon1;
    } else if (val == "自主学习") {
        return icon2;
    } else if (val == "问答") {
        return icon3;
    } else if (val == "讨论") {
        return icon4;
    } else if (val == "练习") {
        return icon5;
    } else if (val == "任务驱动") {
        return icon6;
    } else if (val == "参观教学") {
        return icon7;
    } else if (val == "现场教学") {
        return icon8;
    } else if (val == "自主学习") {
        return icon9;
    } else {
        return icon8;
    }
};

const trackLeft = computed(() => {
    const p = (Number(teachaerAverageSpeed.value) / 400) * 100;
    if (p > 100) return "100";
    return p.toFixed(1);
});
const paper = (val: Static) => {
    if (!val.speak) return;
    const speak = JSON.parse(val.speak);

    if (speak.teacher) {
        fluency.value = speak.teacher.fluency;
    } else if (speak.student) {
        fluency.value = speak.student.fluency;
    }

    if (speak.teacher) {
        teachaerAverageSpeed.value = speak.teacher.averageSpeed.toFixed(2);
        teachaerTotalMinutes.value = speak.teacher.totalMinutes.toFixed(2);
    } else {
        teachaerAverageSpeed.value = "0";
        teachaerTotalMinutes.value = "0";
    }
};
watch(
    () => aiClassStData?.value,
    (val) => {
        nextTick(() => {
            paper(val as Static);
            ktcWords.value = (val as Static).ktcWords;
        });
    },
    // { deep: true }
    { immediate: true }
);
// watch(
//   () => mergedSegments?.value,
//   (val) => {
//     if (val) {
//       console.log("MergedSegmentProps", val);
//       let arr: string[] = [];
//       (val as MergedSegmentProps[]).forEach((item) => {
//         arr.push(item["教学方式"]);
//       });
//       arr = Array.from(
//         new Set(arr.map((item) => (item.includes("讲授") ? "讲授" : item)))
//       );
//       methodList.value = arr;
//       methodRemain.value = computedRowToRemain(arr.length, 5);
//       onSetQuestion();
//     }
//   },
//   // { deep: true }
//   { immediate: true }
// );

const stMode = ref<number>(1); // 1: 标准 2：常模

const remoteInteraction = ref<any>({});
const remoteText = computed(() => {
    const grade = remoteInteraction.value["指数评级"];
    const explanation = remoteInteraction.value["评级说明"];
    const comment = remoteInteraction.value["点评"];
    const improvement = remoteInteraction.value["建议"].join(",");
    return `
        <div>互动指数为${grade}级，${explanation}</div>
        <div>${comment}</div>
        <div>改进时，${improvement}</div>
    `;
});

const trackBg = computed(() => {
    const p = ((Number(remoteInteraction.value["指数评级"]) - 4) / 6) * 100;
    if (p > 80) {
        return "#FA541C";
    } else if (p > 60) {
        return "#9FA624";
    } else if (p > 40) {
        return "#6AD429";
    } else if (p > 20) {
        return "#44B89F";
    } else if (p > 0) {
        return "#27A3FA";
    }
});

const html1 = ref<string>("");
const html2 = ref<string>("");
const html3 = ref<string>("");
const html4 = ref<string>("");

const initMarkdown = async () => {
    const list = roleChats?.value[3];
    const find31 = list.find((item: any) => item.questionType === 31);
    const find32 = list.find((item: any) => item.questionType === 32);
    const find34 = list.find((item: any) => item.questionType === 34);
    if (find31) {
        let find31_answer = find31.answer;

        // 兼容老数据
        if (find31_answer.includes("# 课程综评\n")) {
            find31_answer = find31_answer.replace("# 课程综评\n", "");
        }
        html2.value = await marked.parse(find31_answer, {
            breaks: true,
        });
    }
    if (find32) {
        let find32_answer = find32.answer;
        let markdown = find32_answer;
        if (isJson(find32_answer)) {
            const answer = JSON.parse(find32_answer);
            markdown = answer["总结"];
        }

        html3.value = await marked.parse(markdown, {
            breaks: true,
        });
    }
    if (find34) {
        let find34_answer = find34.answer;
        let markdown = find34_answer;

        if (isJson(find34_answer)) {
            const answer = JSON.parse(find34_answer);
            markdown = answer["总结"];
        }
        html4.value = await marked.parse(markdown, {
            breaks: true,
        });
    }
};

watch(
    () => roleChats?.value,
    (newV) => {
        if (newV) {
            const roleChat3 = newV[3] as any[];
            const data19 = roleChat3.find((item) => item.questionType === 19);
            if (data19) {
                const data19Answer = JSON.parse(data19.answer);
                setMethodList(data19Answer);
            }
            const data35 = roleChat3.find((item) => item.questionType === 35);
            if (data35 && data35.answer) {
                const data35Answer = JSON.parse(data35.answer);
                remoteInteraction.value = data35Answer;
            }
            initMarkdown();
        }
    },
    { immediate: true }
);
</script>

<style lang="scss" scoped>
@font-face {
    font-family: "YouSheBiaoTiHei";
    src: url("@/assets/fonts/YouSheBiaoTiHei.TTF") format("truetype");
    font-weight: normal;
    font-style: normal;
}
.overview {
    width: 100%;
    // height: 908px;
    // padding: 20px;
    box-sizing: border-box;
    border-radius: 4px;
    // border: 1px solid #e5e5e5;
    // overflow-y: auto;
    .title2 {
        font-weight: bold;
        font-size: 16px;
        color: #262626;
        line-height: 24px;
    }
    .overview-title {
        display: flex;
        align-items: center;
        .type {
            margin-left: 12px;
            padding: 2px 8px;
            font-size: 13px;
            color: #fff;
            background: #17be6b;
            border-radius: 4px;
            box-sizing: border-box;
        }
    }

    .speech {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 43px;
        .speech-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            .title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            img {
                width: 24px;
                height: 24px;
                margin: 10px 0;
            }
        }
    }
    .method {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 43px;
        .method-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: 4px;
            img {
                width: 50px;
                height: 50px;
            }
            span {
                margin-top: 10px;
            }
        }
    }
    .percentage {
        display: inline-block;
        margin-top: 12px;
        padding-top: 10px;
        position: relative;
        width: 100%;
        .track {
            position: relative;
            border-radius: 6px;
            height: 8px;
            background: linear-gradient(
                270deg,
                #fa541c 0%,
                #6ad429 48%,
                #27a3fa 100%
            );
        }
        .track-tip {
            z-index: 10;
            position: absolute;
            top: 0;
            width: 0;
            height: 0;
            border: 8px solid v-bind(trackBg);
            border-left-color: transparent;
            border-bottom-color: transparent;
            border-right-color: transparent;
            transform: translateX(-8px);
            .track-tip-line {
                position: absolute;
                border-left: 1px dashed #fff;
                top: 0;
                height: 14px;
                left: 0px;
                width: 1px;
            }
        }
        .track-line {
            color: #8c8c8c;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
    :deep(.ant-radio-group) {
        padding: 2px;
        background: #f5f5f5;
        border-radius: 4px;
    }
    :deep(.ant-radio-button-wrapper) {
        border: none !important;
        background: transparent !important;
        height: 28px;
        line-height: 28px;
        padding: 0 12px;
        border-radius: 4px !important;
        color: #8c8c8c;
        box-shadow: none !important;
    }
    :deep(.ant-radio-button-wrapper::before) {
        display: none;
    }
    :deep(
        .ant-radio-button-wrapper-checked:not(
            .ant-radio-button-wrapper-disabled
        )
    ) {
        background: #ffffff !important;
        color: #262626 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
}

.st-conatiner {
    position: relative;
    height: 100%;
    .st {
        height: 100%;
        display: flex;
        justify-content: space-around;
    }
}
</style>
