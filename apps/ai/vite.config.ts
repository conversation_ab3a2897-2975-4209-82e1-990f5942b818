import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import { createHtmlPlugin } from "vite-plugin-html";
const pkg = require("./package.json");
const base = process.env.BASE ? process.env.BASE + `/${pkg.name}` : "./";
export default defineConfig({
  base,
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  plugins: [vue(),
  createHtmlPlugin({
    inject: {
      data: {
        isProduction: process.env.NODE_ENV === "production",
        timeTamp: +Date.now()
      },
    },
  }),],
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          "primary-color": "#007AFF",
          "link-color": "#007AFF",
          "border-radius-base": "4px",
          "height-base": "36px",
          "table-header-bg": "#F7F7F7",
          "text-color": "#262626",
        },
        javascriptEnabled: true,
      },
      scss: {
        additionalData: `@import "@/assets/css/common.scss";`
      }
    },
  },
  server: {
    open: true,
    port: 4041,
    host: "0.0.0.0",
  },
});
