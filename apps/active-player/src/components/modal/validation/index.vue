<script lang="ts" setup>
import { ref, watchPostEffect } from "vue"
import { message } from "ant-design-vue"
import { validationPlay } from "@/api"
import { emitter, useInfo } from "@/hooks"
import { CommonRes, common } from "@ys/tools";
const { getSessionUser } = common;
const { id, validation } = useInfo();

const time = ref(0);
const randomTime = ref(0);
const currentTime = ref(0);

const validateInfo = ref({
  isOpen: 2,
  validationBegin: 1,
  validationEnd: 2,
})

emitter.on("wsMessage", ({ c, s }: any) => {
  if (c === 20002) {
    validateInfo.value = {
      isOpen: s.openStatus,
      validationBegin: s.start,
      validationEnd: s.end,
    }
    createRandomTime()
  }
})

function createRandomTime() {
  const { validationBegin, validationEnd } = validateInfo.value;
  randomTime.value = Math.floor(
    Math.random() * (validationEnd * 60 - validationBegin * 60) + validationBegin * 60
  );
}

const mountContainer = ref();


watchPostEffect(() => {
  if (!validation.value) return;
  const { isOpen, validationBegin, validationEnd } = validation.value
  validateInfo.value = {
    isOpen,
    validationBegin,
    validationEnd,
  }
  createRandomTime();
  const mainVideo = document.querySelector('#ysPlayer');
  if (!mainVideo) return;
  mountContainer.value = mainVideo;
  const video = mainVideo.querySelector('#mainVideo') as HTMLVideoElement;
  const user = getSessionUser();
  if (!user) return;
  video?.addEventListener('timeupdate', e => {
    time.value += 0.25;
    if (time.value % randomTime.value === 0 && validateInfo.value.isOpen === 1) {
      validateVisible.value = true;
      videoPlayOrPause();
      currentTime.value = video.currentTime;
    }
  })
})


function videoPlayOrPause() {
  const video = document.getElementById('mainVideo') as HTMLVideoElement;
  const subVideo = document.getElementById('subVideo') as HTMLVideoElement;
  if (video.paused) {
    video.play();
  } else {
    video.pause();
  }
  if (subVideo.paused) {
    subVideo.play();
  } else {
    subVideo.pause();
  }

}
async function onCheckSuccess() {

  // validationPlay
  const result = await validationPlay<CommonRes<null>>({
    objectId: id?.value,
    objectType: 1,
    sourceFor: 1,
    tagType: 1, // 1.直播  2.录播
    tagTime: currentTime.value,
  });
  if (result.data.code === 0) {
    message.success('验证成功');

    time.value = 0;
    validateVisible.value = false;
    videoPlayOrPause();

    /* 重置验证状态 */
    status.value = false;
    const bg = document.querySelector(".track .track-bg") as HTMLDivElement;
    const trackBtn = document.querySelector(".track .track-htn") as HTMLDivElement;
    bg.style.width = "0px";
    trackBtn.style.transform = "translateX(" + 0 + "px)"
  }
}


const validateVisible = ref(false);
const status = ref(false);
function startMove(event: any) {
  if (status.value) {
    return false
  }
  const pWidth = 240
  const targetWidth = 60
  const canMoveWidth = pWidth - targetWidth
  let startX = event.clientX
  let distance = 0;
  document.onmousemove = (e) => {
    let endX = e.clientX
    distance = endX - startX
    if (distance <= 0) {
      distance = 0
    }
    if (distance >= canMoveWidth) {
      distance = canMoveWidth
    }
    event.target.style.transform = "translateX(" + distance + "px)"
    const bg = document.querySelector(".track .track-bg") as HTMLDivElement;
    // if (!bg) return;
    bg.style.width = distance + 30 + "px"
    e.preventDefault()
  }
  document.onmouseup = () => {
    if (distance != canMoveWidth) {
      distance = 0
      event.target.style.transform = "translateX(" + distance + "px)"
      const bg = document.querySelector(".track .track-bg") as HTMLDivElement;
      bg.style.width = "0px"
    } else {
      status.value = true
      onCheckSuccess();
    }
    document.onmousemove = null
    document.onmouseup = null
  }
}
</script>

<template>
  <a-modal title="验证" :closable="false" :maskClosable="false" v-model:visible="validateVisible" centered :footer="null"
    :width="300" :getContainer="() => mountContainer">
    <div class="track" :class="{ active: status }">
      <span v-if="!status">请滑动完成验证</span>
      <span v-else style="color:#fff">验证通过</span>
      <div class="track-bg"></div>
      <div @mousedown="startMove" class="track-htn">
        <i v-if="!status" style="fontSize:20px" class="el-icon-d-arrow-right"></i>
        <i v-else style="color:#7ac23c;fontSize:20px" class="el-icon-success"></i>
      </div>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
.track {
  position: relative;
  margin: 10px auto;
  width: 240px;
  height: 40px;
  background: #eaedf1;
  border-radius: 20px;
  color: rgba(192, 196, 204, 1);
  text-align: center;
  line-height: 40px;
  overflow: hidden;

  &.active {
    background: #7ac23c;
  }

  span {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
  }

  span::selection {
    background: none;
    color: none;
  }

  .track-htn {
    position: relative;
    z-index: 3;
    transition: translateX 0.1s;
    text-align: center;
    line-height: 40px;
    width: 60px;
    height: 40px;
    background: #ffffff;
    border: 1px solid #cfd0d2;
    border-radius: 20px;
    cursor: pointer;

    i {
      pointer-events: none;
      position: relative;
      left: 2px;
      top: 2px;
    }
  }

  .track-bg {
    z-index: 1;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background: #7ac23c;
    text-align: center;
    color: #fff;
  }
}
</style>