<script lang="ts" setup>
import { ref, watch, nextTick, h, onMounted, watchPostEffect } from "vue";
import { LoadingOutlined } from "@ant-design/icons-vue";
import { useInfo, emitter } from "@/hooks";
import liveCover from "./liveCover.vue";
import danmuCover from "./danmuCover.vue";
import waterCover from "./waterCover.vue";
import { ysIcon, useFullScreen, useBullet } from "@ys/ui";
import { common } from "@ys/tools";
import {
  VideoDanmu,
  VideoPoint,
  VideoZimi,
  deelZimu,
  clearScreen,
  deelDamu,
  createBulletInfo,
} from "./helper";
const { liveUrls, waterOpen, multiviewList, captionOpen } = useInfo();
const indicator = h(LoadingOutlined, {
  style: {
    fontSize: "40px",
  },
  spin: true,
});
const { isFullScreen, requestFullScreen, exitFullscreen } = useFullScreen();
const { isCdn, secondsFormat } = common;
const props = defineProps<{
  mode: "live" | "video" | "doubleVideo";
  videoPoint: VideoPoint[];
  danmu: VideoDanmu[];
  zimu: VideoZimi[];
  el: string;
  container: string;
}>();
const emit = defineEmits(["onToggleDoubleVideo"]);

const bullet = useBullet(".danmuScreen", { trackHeight: 35 });

const video = ref<HTMLVideoElement>();
const show3 = ref(false);
const loading = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const playing = ref(false);
const maxWidth = ref(0);
const width = ref("0px");
const volumn = ref(0);

watch(
  () => volumn.value,
  (val) => {
    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    mainVideo!.volume = val / 100;
  }
);
const clonedVideoDanmu = ref<VideoDanmu[]>([]);

function unListenVideo() {
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  const subMainVideo = document.getElementById(
    "subMainVideo"
  ) as HTMLVideoElement;
  [mainVideo, subMainVideo].forEach((element) => {
    element.onplay = null;
    element.onpause = null;
    element.oncanplay = () => {
      element.play();
    };
    element.onwaiting = null;
    element.onseeking = null;
    element.onended = null;
    element.ontimeupdate = null;
    element.onloadeddata = null;
  });
}
watchPostEffect(() => {
  unListenVideo();
  clonedVideoDanmu.value = [...props.danmu];
  video.value = document.getElementById(props.el) as HTMLVideoElement;
  video.value!.onplay = () => {
    // 播放速度重置
    handle2ChangeSpeed(1.0);
    playing.value = true;
    bullet.value?.resume();
  };
  video.value!.onpause = () => {
    playing.value = false;
    bullet.value?.pause();
  };
  video.value.oncanplay = () => {
    loading.value = false;
    video.value?.play();
  };
  video.value.onwaiting = () => {
    loading.value = true;
  };
  video.value.onseeking = () => {
    clearScreen();
    deelZimu(props.zimu, video.value!.currentTime);

    const currentTime = video.value!.currentTime;
    clonedVideoDanmu.value = props.danmu.filter(
      (item) => item.eventAttrTime >= currentTime
    );
  };
  video.value.onended = () => {
    if (props.mode != "video") return;
    // 判断是否自动播放下一个;
    const autoPlaySwitch = document.querySelector(".auto-play-next");
    if (!autoPlaySwitch) return;
    const isNext = autoPlaySwitch.getAttribute("aria-checked");
    if (isNext === "true") {
      emitter.emit("autoPlayNext");
    }
  };
  video.value.ontimeupdate = () => {
    if (props.mode === "live") return;
    duration.value = video.value!.duration;
    currentTime.value = video.value!.currentTime;
    width.value = (currentTime.value / duration.value) * maxWidth.value + "px";
    deelZimu(props.zimu, currentTime.value);
    deelDamu(bullet.value, clonedVideoDanmu.value, currentTime.value);
  };
  video.value.onloadstart = () => {
    clearScreen();
    show3.value = false;
  };
  video.value.onloadeddata = () => {
    volumn.value = video.value!.volume * 100;
    show3.value = true;
    if (props.mode === "live") return;
    width.value = "0px";
    nextTick(() => {
      maxWidth.value = getProgressWidth();
      const zimuScreen = document.querySelector(
        ".zimuScreen"
      ) as HTMLDivElement;
      if (zimuScreen) {
        zimuScreen.style.opacity = showZimu.value ? "1" : "0";
      }
    });
    currentTime.value = video.value!.currentTime;
    duration.value = video.value!.duration;
  };
});

onMounted(() => {
  const videoScreen = document.querySelector(".video-screen");
  const videoController = document.querySelector(".video-progress");
  let timer: any;
  videoScreen?.addEventListener("mouseenter", (e) => {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
      videoController?.classList.add("hide");
    }, 5000);
  });
  videoScreen?.addEventListener("mousemove", (e) => {
    videoController?.classList.remove("hide");
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
      videoController?.classList.add("hide");
    }, 5000);
  });
  videoScreen?.addEventListener("mouseleave", (e) => {
    videoController?.classList.add("hide");
  });
  window.addEventListener("resize", (e) => {
    maxWidth.value = getProgressWidth();
    width.value = (currentTime.value / duration.value) * maxWidth.value + "px";
  });
  window.onkeydown = function (e) {
    // 空格
    if (e.keyCode === 32) {
      onTogglePlay();
    }
    // 快进
    if (e.keyCode === 39) {
      onChangeCurrentTime("add");
    }
    // 快退
    if (e.keyCode === 37) {
      onChangeCurrentTime("sub");
    }
    return e.keyCode !== 32;
  };
});

function getProgressWidth() {
  const progress = document.querySelector(".progress") as HTMLDivElement;
  if (!progress) return 0;
  const { width } = progress.getBoundingClientRect();
  return width;
}

function onTimeChange(e: any) {
  const currentTime = (e.offsetX / maxWidth.value) * duration.value;
  video.value!.currentTime = currentTime;
  width.value = (currentTime / duration.value) * maxWidth.value + "px";

  if (props.mode === "doubleVideo") {
    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    const subMainVideo = document.getElementById(
      "subMainVideo"
    ) as HTMLVideoElement;
    const threeMainVideo = document.getElementById("threeMainVideo");
    mainVideo.currentTime = currentTime;
    subMainVideo.currentTime = currentTime;
    if (threeMainVideo) {
      (threeMainVideo as HTMLVideoElement).currentTime = currentTime;
    }
  }
}
function onPoint(point: VideoPoint) {
  video.value!.currentTime = point.noteTime;
  width.value = (point.noteTime / duration.value) * maxWidth.value + "px";
}
function onProgressBar(e: any) {
  const cur = parseInt(width.value);
  document.onmousemove = (evt) => {
    const dis = evt.clientX - e.clientX;
    let currentWidth = cur + dis;
    currentWidth = Math.max(0, currentWidth);
    currentWidth = Math.min(currentWidth, maxWidth.value);
    width.value = currentWidth + "px";
    video.value!.currentTime = (currentWidth / maxWidth.value) * duration.value;
  };
  document.onmouseup = () => {
    document.onmousemove = null;
    document.onmouseup = null;
  };
}
const pointerContainer = ref();

const showZimu = ref(true);
watch(
  () => captionOpen.value,
  (newV) => {
    if (newV == 1) {
      showZimu.value = true;
    } else {
      showZimu.value = false;
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
function handleToggleZimu() {
  const zimuScreen = document.querySelector(".zimuScreen") as HTMLDivElement;
  if (!zimuScreen) return;
  showZimu.value = !showZimu.value;
  zimuScreen.style.opacity = showZimu.value ? "1" : "0";
}

function handleFullScreen() {
  requestFullScreen(document.getElementById(props.container)!);
}

function onChangeCurrentTime(type: "add" | "sub") {
  if (props.mode === "live") return;
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  if (!mainVideo.getAttribute("src")) return;
  if (type === "add") {
    mainVideo.currentTime += 10;
  } else {
    mainVideo.currentTime -= 10;
  }
  const subMainVideo = document.getElementById(
    "subMainVideo"
  ) as HTMLVideoElement;
  if (!subMainVideo.getAttribute("src")) return;
  if (type === "add") {
    subMainVideo.currentTime += 10;
  } else {
    subMainVideo.currentTime -= 10;
  }
  const threeMainVideo = document.getElementById(
    "threeMainVideo"
  ) as HTMLVideoElement;
  if (!threeMainVideo.getAttribute("src")) return;
  if (type === "add") {
    threeMainVideo.currentTime += 10;
  } else {
    threeMainVideo.currentTime -= 10;
  }
}
function onTogglePlay() {
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  if (!mainVideo.getAttribute("src")) return;
  if (props.mode === "doubleVideo") {
    const subMainVideo = document.getElementById(
      "subMainVideo"
    ) as HTMLVideoElement;
    const threeMainVideo = document.getElementById(
      "threeMainVideo"
    ) as HTMLVideoElement;
    if (video.value?.paused) {
      mainVideo.play();
      subMainVideo.play();
      if (threeMainVideo.getAttribute("src")) {
        threeMainVideo.play();
      }
    } else {
      mainVideo.pause();
      subMainVideo.pause();
      if (threeMainVideo.getAttribute("src")) {
        threeMainVideo.pause();
      }
    }
    return;
  }
  if (video.value?.paused) {
    video.value.play();
  } else {
    video.value?.pause();
  }
}

function onToggleFullScreen() {
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  if (!mainVideo.getAttribute("src")) return;

  if (isFullScreen.value) {
    exitFullscreen();
  } else {
    handleFullScreen();
  }
}

const playRateList = [2.0, 1.5, 1.25, 1.0, 0.5];
const currentRate = ref(1.0);
function handle2ChangeSpeed(rate: number) {
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  if (!mainVideo.getAttribute("src")) return;
  currentRate.value = rate;
  mainVideo.playbackRate = rate;

  const subMainVideo = document.getElementById(
    "subMainVideo"
  ) as HTMLVideoElement;
  const threeMainVideo = document.getElementById(
    "threeMainVideo"
  ) as HTMLVideoElement;

  subMainVideo && (subMainVideo.playbackRate = rate);
  threeMainVideo && (threeMainVideo.playbackRate = rate);
}
</script>

<template>
  <div
    class="video-screen"
    @click="onTogglePlay"
    @dblclick="onToggleFullScreen"
  >
    <div v-show="loading" class="loading">
      <a-spin :indicator="indicator" />
    </div>
    <waterCover
      v-if="mode === 'live' && waterOpen === 1 && liveUrls.length > 0"
    />
    <liveCover v-if="mode === 'live' && liveUrls.length === 0" />
    <danmuCover
      :mode="mode"
      @onEmitBullet="(d) => createBulletInfo(bullet, d)"
    />
    <div v-if="show3" class="zimuScreen"></div>

    <div class="video-progress" v-show="show3" ref="pointerContainer">
      <div v-show="mode != 'live'" @click="onTimeChange" class="progress">
        <div class="progress-track">
          <div @click.stop="" @mousedown.stop="onProgressBar" class="bar"></div>
        </div>

        <a-popover
          :getPopupContainer="() => pointerContainer"
          v-for="point in videoPoint"
          color="rgba(0, 0, 0, 0.8)"
        >
          <template #content>
            <div class="point-item">
              <img
                v-if="point.markPicUrl"
                :src="isCdn(point.markPicUrl)"
                alt=""
              />
              <div>{{ point.noteExplain }}</div>
            </div>
          </template>
          <div
            @click.stop="onPoint(point)"
            class="progress-point"
            :style="{ left: (point.noteTime / duration) * maxWidth - 4 + 'px' }"
          ></div>
        </a-popover>
      </div>

      <div class="control-list">
        <div class="l">
          <ys-icon
            @click.stop="onTogglePlay"
            v-show="playing"
            title="暂停"
            class="icon"
            type="iconzanting"
          />
          <ys-icon
            @click.stop="onTogglePlay"
            v-show="!playing"
            title="播放"
            class="icon"
            type="iconicon_play"
          />
          <a-space v-show="mode != 'live'" style="margin-left: 20px">
            <span>{{ secondsFormat(currentTime, "HH:mm:ss") }}</span>
            <span>/</span>
            <span>{{ secondsFormat(duration, "HH:mm:ss") }}</span>
          </a-space>
        </div>
        <div class="r">
          <a-space size="large">
            <div
              v-if="mode === 'doubleVideo'"
              @click.stop="() => emit('onToggleDoubleVideo', 2)"
              style="cursor: pointer"
            >
              <ysIcon
                class="icon"
                type="iconqiehuan"
                style="vertical-align: middle"
              />
              <span style="vertical-align: middle; margin-left: 8px"
                >画面2</span
              >
            </div>

            <div
              v-if="mode === 'doubleVideo' && multiviewList[0].viewThreeId"
              @click.stop="() => emit('onToggleDoubleVideo', 3)"
              style="cursor: pointer"
            >
              <ysIcon
                class="icon"
                type="iconqiehuan"
                style="vertical-align: middle"
              />
              <span style="vertical-align: middle; margin-left: 8px"
                >画面3</span
              >
            </div>

            <a-popover
              :getPopupContainer="() => pointerContainer"
              placement="top"
              color="rgba(0, 0, 0, 0.8)"
            >
              <template #content>
                <div
                  @click.stop="() => handle2ChangeSpeed(rate)"
                  :class="['rate-item', { active: rate === currentRate }]"
                  v-for="rate in playRateList"
                  :key="rate"
                >
                  {{ rate }}X
                </div>
              </template>
              <span v-show="mode != 'live'">播放速度</span>
            </a-popover>

            <span
              v-show="mode != 'live'"
              @click.stop="handleToggleZimu"
              style="cursor: pointer"
              >字幕{{ showZimu ? "开" : "关" }}</span
            >

            <div class="volumn-container">
              <div @click.stop="" class="volumn-bar">
                <a-slider vertical v-model:value="volumn" />
              </div>
              <ysIcon
                @click.stop="volumn = 100"
                v-if="volumn === 0"
                class="icon"
                type="iconmn_shengyinwu_fill"
              />
              <ysIcon
                @click.stop="volumn = 0"
                v-else
                class="icon"
                type="iconmn_shengyin_fill"
              />
            </div>

            <ysIcon
              @click.stop="handleFullScreen"
              v-if="!isFullScreen"
              title="全屏"
              class="icon"
              type="iconquanping2"
            />
            <ysIcon
              @click.stop="exitFullscreen"
              v-else
              title="全屏"
              class="icon"
              type="iconsuoxiao"
            />
          </a-space>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.video-screen {
  /* overflow: hidden; */
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.danmuScreen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.zimuScreen {
  position: absolute;
  left: 0;
  bottom: 66px;
  width: 100%;
  text-align: center;
}

.icon {
  vertical-align: top;
  cursor: pointer;
  font-size: 20px;
}

.video-progress {
  position: absolute;
  z-index: 9;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  transition: all 0.2s linear;

  &.hide {
    transform: translateY(80px);
  }
}

/* ===== */

.progress {
  width: 100%;
  position: relative;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  height: 8px;
  margin-bottom: 12px;
  cursor: pointer;

  &:hover {
    .bar {
      display: block !important;
    }
  }

  .progress-track {
    border-radius: 4px;
    width: v-bind(width);
    height: 100%;
    background: #007aff;
    position: relative;

    .bar {
      transition: all 0.2s linear;
      display: none;
      z-index: 2;
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
      transform: translateX(50%);
      margin-top: -3px;
      width: 14px;
      height: 14px;
      background: #007aff;
      border-radius: 50%;
    }
  }

  .progress-point {
    z-index: 3;
    cursor: pointer;
    position: absolute;
    top: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #fff;
  }
}

.control-list {
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .l {
    display: flex;
    align-items: center;
  }

  .volumn-container {
    position: relative;
    top: -4px;
    padding-top: 10px;

    &:hover {
      .volumn-bar {
        display: block;
      }
    }

    .volumn-bar {
      display: none;
      padding: 10px 0 20px 0;
      border-radius: 4px;
      position: absolute;
      height: 120px;
      background: rgba(0, 0, 0, 0.6);
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.point-item {
  width: 240px;

  img {
    width: 240px;
    height: 135px;
    margin-bottom: 8px;
  }

  div {
    word-wrap: break-word;
  }

  color: #fff;
}

/* zimu */
.zimuScreen {
  position: absolute;
  z-index: 1;
  left: 0;
  bottom: 66px;
  width: 100%;
  text-align: center;
}

.rate-item {
  color: #fff;
  text-align: center;
  cursor: pointer;

  &.active {
    color: #14c9c9;
  }

  &:hover {
    color: #14c9c9;
  }

  & + .rate-item {
    margin-top: 10px;
  }
}
</style>

<style>
.zimuScreen .zimu-content {
  text-align: justify;
  max-width: 80%;
  color: #fff;
  font-size: 18px;
  display: inline-block;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 8px 16px;
}
</style>
