import { inject, ref, Ref, computed, watch } from "vue";
import { message } from "ant-design-vue";
import { useRouter, useRoute } from "vue-router";
import { common, CommonRes } from "@ys/tools";
import { useInfo, emitter } from "@/hooks";
import { createChatMsg } from "@/hooks/useSoket";
import { insertEventMessage, saveEventBarrageInfo, queryAiTask } from "@/api";
import { getUrlQuery } from "@ys/tools/common/path";
import { aiTaskGroupItemType } from "@/pages/setting/application/videoAudioBrain/entity";
const { getSessionUser, bureauBlankOpen } = common;

export const getAiAuth = () => {
  const user = getSessionUser();
  if (user?.id) {
    if (user.userType == 1002) {
      return 1; // 教师
    } else if (user.userType == 1003) {
      return 2; // 学生
    } else {
      return 3; // 不予显示
    }
  } else {
    // 没有登录的情况下
    return 2;
  }
};

export function useChat() {
  const taskGroupList = inject<Ref<aiTaskGroupItemType[]>>("taskGroupList");
  const route = useRoute();
  const router = useRouter();
  const {
    id,
    isAdmin,
    videoBarrageOpen,
    liveBarrageOpenManage,
    liveBarrageOpen,
  } = useInfo();

  const ws = inject<Ref<WebSocket>>("ws");

  const chatColor = ref("#fff");
  const chatValue = ref("");
  const openDanmu = ref(true);

  function switchDanmu() {
    openDanmu.value = !openDanmu.value;
    const danmuScreen = document.querySelector(
      ".danmuScreen"
    ) as HTMLDivElement;
    if (openDanmu.value) {
      danmuScreen.style.opacity = "1";
    } else {
      danmuScreen.style.opacity = "0";
    }
  }

  async function onLiveSub() {
    if (ws && ws.value && ws.value.readyState === 1) {
      const result = await insertEventMessage<CommonRes<null>>({
        eventInfoId: id?.value,
        eventMessageInfo: chatValue.value,
        eventMessageType: 1,
      });
      if (result.data.code != 0) {
        return;
      }
      ws.value?.send(
        createChatMsg({
          color: chatColor.value,
          msg: chatValue.value,
          id: id!.value,
        })
      );
      message.success("发送成功");
      chatValue.value = "";
    } else {
      message.error("websoket 连接失败, 请刷新重试");
    }
  }

  const currentEventAttrId = ref();
  emitter.on("onPlayVideo", (e: any) => {
    currentEventAttrId.value = e.currentEventAttr.eventAttrId;
    // onGetTask(e.currentEventAttr.eventAttrId);
  });
  async function onVideoSub() {
    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    const result = await saveEventBarrageInfo<CommonRes<null>>({
      barrageSendColor: chatColor.value,
      barrageSendInfo: chatValue.value,
      eventInfoId: id?.value,
      eventAttrId: currentEventAttrId.value,
      eventAttrTime: mainVideo.currentTime,
    });

    if (result.data.code === 0) {
      const userInfo = getSessionUser();
      emitter.emit("pushCustomDanmu", {
        n: userInfo!.name,
        org: userInfo!.orgName,
        r: id?.value,
        f: userInfo!.userFace,
        i: userInfo!.id,
        m: chatValue.value,
        t: new Date(),
        color: chatColor.value,
      });
      message.success("发送成功");
      chatValue.value = "";
    }
  }

  const isShowAi = ref(false);
  async function onGetTask(eventAttrId: string) {
    isShowAi.value = false;

    const result = await queryAiTask<CommonRes<{ taskStatus: number }>>({
      questionRole: 0,
      eventAttrId,
    });
    if (result.data.code === 0) {
      if (result.data.data.taskStatus === 1) {
        isShowAi.value = true;
      } else {
        isShowAi.value = false;
      }
    }
  }

  function handle2AIreport() {
    // if (!currentEventAttrId.value) return;
    // const link = router.resolve(
    //   `/active-ai/${id?.value}?attrId=${currentEventAttrId.value}`
    // );
    // bureauBlankOpen(link.href, false, "_self");
    // 上面是之前黎总写的
    const fliters = taskGroupList?.value.filter((item) => {
      return item.taskGroupStatus == 1;
    });
    if (fliters?.length) {
      // const link = router.resolve(
      //   `/active-ai/${id?.value}?groupId=${fliters[0].id}`
      // );
      // bureauBlankOpen(link.href, false, "_self");
      // 现在统一跳转到影像大脑
      const bureauId = getUrlQuery("bureauId");
      const jd = getUrlQuery("jd");
      let url = `${location.origin}/yskt/ys/ai/#/active-ai/${id?.value}/1/${getAiAuth()}?groupId=${fliters[0].id}&bureauId=${bureauId}&jd=${jd}&taskType=${fliters[0].taskType}`;
      location.href = url;
    }
  }
  function handle2AIClassDiff() {
    // if (!currentEventAttrId.value) return;
    // const link = router.resolve(
    //   `/active-ai/class-diff/${id?.value}?attrId=${currentEventAttrId.value}`
    // );
    // bureauBlankOpen(link.href, false, "_self");
    // 上面是之前黎总写的
    const fliters = taskGroupList?.value.filter((item) => {
      return item.taskGroupStatus == 1;
    });
    if (fliters?.length) {
      // const link = router.resolve(
      //   `/active-ai/class-diff/${id?.value}?groupId=${fliters[0].id}`
      // );
      // bureauBlankOpen(link.href, false, "_self");

      // 现在统一跳转到影像大脑
      const bureauId = getUrlQuery("bureauId");
      const jd = getUrlQuery("jd");
      let url = `${location.origin}/yskt/ys/ai/#/active-ai/class-diff/${id?.value}/1/${getAiAuth()}?groupId=${fliters[0].id}&bureauId=${bureauId}&jd=${jd}&taskType=${fliters[0].taskType}`;
      location.href = url;
    }
  }
  return {
    chatColor,
    chatValue,
    openDanmu,
    isShowAi,
    switchDanmu,
    onVideoSub,
    onLiveSub,
    handle2AIreport,
    handle2AIClassDiff,
  };
}
