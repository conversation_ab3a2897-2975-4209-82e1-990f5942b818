<script lang="ts" setup>
import chatInfo from "./chatInfo/index.vue";
import emptCover from "./videoScreen/emptCover.vue";
import videoScreen from "./videoScreen/index.vue";
import { usePlayer } from "./usePlayer";
const { mode, hasVideoSource, videoDanmu, videoZimu, videoPoint, elProgress } =
  usePlayer();
function onDoubleChangeVideo(type: 2 | 3) {
  if (mode.value != "doubleVideo") return;
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  const subMainVideo = document.getElementById(
    "subMainVideo"
  ) as HTMLVideoElement;
  const threeMainVideo = document.getElementById(
    "threeMainVideo"
  ) as HTMLVideoElement;

  const mainVideoUrl = mainVideo.src;
  if (type === 2) {
    mainVideo.src = subMainVideo.src;
    subMainVideo.src = mainVideoUrl;
    threeMainVideo.src = threeMainVideo.src;
    /* 1,2画面切换 */
    if (elProgress.value != "threeMainVideo") {
      if (elProgress.value === "mainVideo") {
        elProgress.value = "subMainVideo";
      } else {
        elProgress.value = "mainVideo";
      }
    }
  } else if (type === 3) {
    mainVideo.src = threeMainVideo.src;
    subMainVideo.src = subMainVideo.src;
    threeMainVideo.src = mainVideoUrl;
    /* 1,3画面切换 */
    if (elProgress.value != "subMainVideo") {
      if (elProgress.value === "mainVideo") {
        elProgress.value = "threeMainVideo";
      } else {
        elProgress.value = "mainVideo";
      }
    }
  }
  mainVideo.play();
  subMainVideo.play();
  threeMainVideo.play();
}
</script>

<template>
  <!-- 没有直播&录像 -->
  <empt-cover />
  <div v-if="hasVideoSource" id="ysPlayer">
    <videoScreen
      :el="elProgress"
      container="ysPlayer"
      :videoPoint="videoPoint"
      :danmu="videoDanmu"
      :zimu="videoZimu"
      :mode="mode"
      @onToggleDoubleVideo="onDoubleChangeVideo"
    />
    <div :class="['video-container', { double: mode === 'doubleVideo' }]">
      <video id="mainVideo" src=""></video>
      <div class="subVideoContainer" v-show="mode === 'doubleVideo'">
        <video id="subMainVideo" src="" muted></video>
        <video id="threeMainVideo" src="" muted></video>
      </div>
    </div>
  </div>
  <chatInfo :mode="mode" />
</template>

<style lang="scss" scoped>
#ysPlayer {
  position: relative;
}

.video-container {
  width: 100%;
  height: 100%;
  background: #000;

  #mainVideo {
    height: 100%;
    width: 100%;
  }
}

.double {
  display: flex;
  align-items: center;

  #mainVideo {
    width: 66.6667%;
  }

  #subMainVideo {
    width: 100%;
  }

  #threeMainVideo {
    width: 100%;
  }

  .subVideoContainer {
    width: 33.3333%;
    font-size: 0;
  }
}
</style>
