import { computed, ref, watchPostEffect, onMounted, nextTick } from "vue";
import { common } from "@ys/tools";
import { saveLookAction } from "@/api";
import { emitter, useInfo } from "@/hooks";
const { isCdn, createFlvPlayer, getSessionUser } = common;
type VideoDanmu = {
  eventBarrageId: string;
  barrageSendColor: string;
  barrageSendInfo: string;
  orgName: string;
  userName: string;
  userFace: string;
  eventAttrTime: number;
  barrageSendTime: number;
  preferred: 1 | 2;
};
type VideoZimi = {
  e: string;
  c: string;
  s: string;
  id: string;
};
type VideoPoint = {
  markPicUrl: string;
  noteExplain: string;
  noteTime: number;
};

function createPlayerContainer() {
  const ysPlayer = document.getElementById("ysPlayer");
  if (!ysPlayer) return;
  const { width } = ysPlayer.getBoundingClientRect();
  ysPlayer.style.height = width * 0.5625 + "px";

  const mainVideo = document.getElementById("mainVideo");
  if (!mainVideo) return;
  const { width: videoWidth } = mainVideo?.getBoundingClientRect();
  mainVideo.style.height = videoWidth * 0.5625 + "px";

  const subVideoContainer = document.querySelector(
    ".subVideoContainer"
  ) as HTMLDivElement;
  if (!subVideoContainer) return;
  subVideoContainer.style.height = videoWidth * 0.5625 + "px";

  const subVideo = document.getElementById("subMainVideo");
  if (!subVideo) return;
  // const { width: subvideoWidth } = subVideo?.getBoundingClientRect()
  // subVideo.style.height = subvideoWidth * 0.5625 + "px"
  subVideo.style.height = (videoWidth * 0.5625) / 2 + "px";

  const threeVideo = document.getElementById("threeMainVideo");
  if (!threeVideo) return;
  // const { width: threeVideoWidth } = subVideo?.getBoundingClientRect()
  // threeVideo.style.height = threeVideoWidth * 0.5625 + "px"
  threeVideo.style.height = (videoWidth * 0.5625) / 2 + "px";
}

export function usePlayer() {
  const {
    hasLiveRoom,
    videoList,
    videoAllowWatch,
    liveUrls,
    id,
    multiviewList,
  } = useInfo();
  const mode = ref<"live" | "video" | "doubleVideo">("live");

  const hasVideoSource = computed(() => {
    if (hasLiveRoom.value) return true;
    if (videoList.value.length > 0 && videoAllowWatch.value === 1) return true;
    return false;
  });

  onMounted(() => {
    window.addEventListener("resize", () => {
      if (!hasVideoSource.value) return;
      createPlayerContainer();
    });
  });

  watchPostEffect(() => {
    if (!hasVideoSource.value) return;
    createPlayerContainer();
  });

  // 初始化录播
  const currentEventAttr = ref({
    eventAttrId: "",
    eventAttrPath: "",
  });
  const videoDanmu = ref<VideoDanmu[]>([]);
  const videoZimu = ref<VideoZimi[]>([]);
  const videoPoint = ref<VideoPoint[]>([]);

  emitter.on("onPlayVideo", (e: any) => {
    mode.value = "video";
    nextTick(() => {
      createPlayerContainer();
    });
    elProgress.value = "mainVideo";
    currentEventAttr.value = e.currentEventAttr;
    videoDanmu.value = e.videoDanmu;
    videoZimu.value = e.videoZimu;
    videoPoint.value = e.videoPoint;

    if (liveMainPlayerInstance.value) {
      liveMainPlayerInstance.value.destroy();
      liveMainPlayerInstance.value = "";
    }

    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    mainVideo.src = isCdn(currentEventAttr.value.eventAttrPath);
    setTimeout(() => {
      mainVideo.play();
    }, 300);
  });

  const elProgress = ref("mainVideo");
  emitter.on("onPlayDoubleVideo", (e: any) => {
    mode.value = "doubleVideo";
    elProgress.value = e.targetEl;
    nextTick(() => {
      createPlayerContainer();
    });

    if (liveMainPlayerInstance.value) {
      liveMainPlayerInstance.value.destroy();
      liveMainPlayerInstance.value = "";
    }
    currentEventAttr.value = e.currentEventAttr;
    videoDanmu.value = [...e.videoDanmu];
    videoZimu.value = [...e.videoZimu];
    videoPoint.value = [...e.videoPoint];

    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    const subMainVideo = document.getElementById(
      "subMainVideo"
    ) as HTMLVideoElement;
    const threeMainVideo = document.getElementById(
      "threeMainVideo"
    ) as HTMLVideoElement;

    const { viewTwoId, viewOneId, viewThreeId } = multiviewList.value[0];
    const viewOneAttr = videoList.value.find(
      (video) => video.eventAttrId === viewOneId
    );
    const viewTwoAttr = videoList.value.find(
      (video) => video.eventAttrId === viewTwoId
    );
    const viewThreeAttr = videoList.value.find(
      (video) => video.eventAttrId === viewThreeId
    );
    mainVideo.src = isCdn(viewOneAttr?.eventAttrPath);
    subMainVideo.src = isCdn(viewTwoAttr?.eventAttrPath);
    threeMainVideo.src = isCdn(viewThreeAttr?.eventAttrPath);

    setTimeout(() => {
      mainVideo.play();
      subMainVideo.play();
      viewThreeAttr && threeMainVideo.play();
    }, 300);
  });

  const liveMainPlayerInstance = ref();
  const firstIn = ref(true);
  function createLiveFlv(url: string) {
    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    mainVideo.src = "";
    if (!url) return;
    if (firstIn.value) {
      mainVideo.volume = 0;
      firstIn.value = false;
    }
    if (liveMainPlayerInstance.value) {
      liveMainPlayerInstance.value.destroy();
      liveMainPlayerInstance.value = "";
    }
    liveMainPlayerInstance.value = createFlvPlayer(url, mainVideo);
  }
  emitter.on("onPlayMainLive", (url: any) => {
    mode.value = "live";
    nextTick(() => {
      createPlayerContainer();
    });
    elProgress.value = "mainVideo";

    const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
    if (!mainVideo) return;
    mainVideo.src = "";
    if (url) {
      createLiveFlv(url);
      // 记录动作
      // if (!getSessionUser()) return
      saveLookAction({
        lookType: 1,
        eventId: id?.value,
        deviceType: 1,
        attrId: "",
      });
    }
  });

  return {
    mode,
    hasVideoSource,
    videoDanmu,
    videoZimu,
    videoPoint,

    elProgress,
  };
}
