<script lang="ts" setup>
import { ref, computed, onMounted, watchEffect } from "vue"
import { useInfo, emitter } from "@/hooks"
import chatBar from "./chatBar/index.vue"
import videoBar from "./videoBar/index.vue"
type ActiveTab = 'chatBar' | 'videoBar'
const activeTab = ref<ActiveTab>('chatBar');
const { videoList, hasLiveRoom, videoAllowWatch, liveUrls } = useInfo();

function changeTab(type: ActiveTab) {
  activeTab.value = type
  if (type === 'chatBar') {
    emitter.emit('Toggle2Live')
  } else {
    emitter.emit('Toggle2Video')
  }
};

emitter.on("onLookPlayback", () => {
  activeTab.value = "videoBar"
})


const showVideoTab = computed(() => {
  return hasLiveRoom.value || (videoList.value.length > 0 && videoAllowWatch.value === 1)
})
const hasVideo = computed(() => {
  return videoList.value.length > 0 && videoAllowWatch.value === 1
})

watchEffect(() => {
  if (hasLiveRoom.value) {
    activeTab.value = 'chatBar'
  } else if (hasVideo.value) {
    activeTab.value = 'videoBar'
  }
})

const loop = ref(false);
</script>

<template>
  <div class="video-tab" v-if="showVideoTab">
    <!-- 有直播间、有录播 -->
    <div class="tab-container" v-if="hasVideo && hasLiveRoom">
      <div @click="changeTab('chatBar')" :class="{ active: activeTab === 'chatBar' }">
        <img v-if="liveUrls[0]" src="@/assets/images/living.gif" alt="">
        正在直播
      </div>
      <div @click="changeTab('videoBar')" :class="{ active: activeTab === 'videoBar' }">录像 {{ videoList.length }}</div>
    </div>
    <!-- 只有录播 -->
    <div v-else-if="hasVideo && !hasLiveRoom" class="only-video">
      <div>
        <span>录像</span>
        <span class="label">{{ videoList.length }}</span>
      </div>
      <div>
        <span class="label">自动连播</span>
        <a-switch class="auto-play-next" v-model:checked="loop" />
      </div>
    </div>

    <chatBar v-show="activeTab === 'chatBar'" />
    <videoBar v-show="activeTab === 'videoBar'" />
  </div>
</template>

<style lang="scss" scoped>
.video-tab {
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
  margin-left: 20px;
  width: 320px;
  background: #fff;
}

.tab-container {
  height: 48px;
  display: flex;
  border-bottom: 1px solid #F0F0F0;

  img {
    width: 16px;
    vertical-align: middle;
  }

  div {
    cursor: pointer;
    border-bottom: 2px solid transparent;
    flex: 1;
    text-align: center;
    line-height: 48px;
    font-size: 16px;
  }

  div.active {
    color: #007AFF;
    border-bottom-color: #007AFF;
  }
}

.only-video {
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background: #F5F5F5;

  >div {
    display: flex;
    align-items: center;
  }

  .label {
    margin-right: 8px;
    color: #8C8C8C;
  }
}
</style>