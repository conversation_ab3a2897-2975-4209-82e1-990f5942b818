<script lang="ts" setup>
import { ref, nextTick, onMounted } from "vue"
import { ysEmpt, ysCollapse, ysIcon, useDomScrollBottom, ysCreateEmojiUrl } from "@ys/ui"
import announceModal from "./announceModal.vue"
import { useInfo, emitter } from "@/hooks"
import { common } from "@ys/tools"
const { createFlvPlayer } = common;
const { isAdmin, isOpemCommentOrg, liveUrls, basicInfo } = useInfo();
const { getIsBottom, scroll2Bottom } = useDomScrollBottom("#chatList");
const chatList = ref<any[]>([]);
const visible = ref(false);
const announceInfo = ref({
  value: '',
  show: false
})

const liveSubPlayerInstance = ref();
emitter.on("onPlaySubLive", (url: any) => {
  const subVideo = document.getElementById("subVideo") as HTMLVideoElement;
  if (!url || !subVideo) return;
  if (liveSubPlayerInstance.value) {
    liveSubPlayerInstance.value.destroy();
    liveSubPlayerInstance.value = "";
  }
  liveSubPlayerInstance.value = createFlvPlayer(url, subVideo);
})
const liveThreePlayerInstance = ref();
emitter.on("onPlayThreeLive", (url: any) => {
  const threeVideo = document.getElementById("threeVideo") as HTMLVideoElement;
  if (!url || !threeVideo) return;
  if (liveThreePlayerInstance.value) {
    liveThreePlayerInstance.value.destroy();
    liveThreePlayerInstance.value = "";
  }
  liveThreePlayerInstance.value = createFlvPlayer(url, threeVideo);
})
function destroyLive() {
  if (liveSubPlayerInstance.value) {
    liveSubPlayerInstance.value.destroy();
    liveSubPlayerInstance.value = "";
  }
  if (liveThreePlayerInstance.value) {
    liveThreePlayerInstance.value.destroy();
    liveThreePlayerInstance.value = "";
  }
}
emitter.on("onPlayVideo", destroyLive);
emitter.on("onPlayDoubleVideo", destroyLive);

emitter.on("wsMessage", async ({ c, d, comment }: any) => {
  if (c === 1000) {
    let msg = d.m;
    msg = msg.replace(/{emoji: (\d+)}/gi, function (index: any) {
      const [num] = index.match(/\d+/g);
      return `<img style="width:22px;height:22px;vertical-align: middle" src=${ysCreateEmojiUrl(
        num - 2
      )}>`;
    });
    msg = msg.replace(/{emoji:(\d+)}/gi, function (index: any) {
      const [num] = index.match(/\d+/g);
      return `<img style="width:22px;height:22px;vertical-align: middle" src=${ysCreateEmojiUrl(
        num - 2
      )}>`;
    });
    const isBottom = getIsBottom();
    chatList.value.push({
      name: d.n,
      value: msg,
      org: d.org,
    });
    await nextTick();
    if (isBottom) {
      scroll2Bottom();
    }
  }
  if (c === 1111 || c === 1112) {
    announceInfo.value = {
      value: d.m,
      show: true
    }
  }
  if (c === 9999) {
    const { a, b, c, d, e, f, g } = comment;
    basicInfo!.value.eventOtherInfo.liveBarrageOpen = a;
    basicInfo!.value.eventOtherInfo.liveBarrageOpenManage = b;
    basicInfo!.value.eventOtherInfo.videoBarrageOpen = c;
    basicInfo!.value.eventOtherInfo.commentsOpen = d;
    basicInfo!.value.eventOtherInfo.liveSystemBarrageOpen = e;
    basicInfo!.value.eventOtherInfo.liveSystemBarrageInfo = f;
    basicInfo!.value.eventOtherInfo.liveSystemBarrageInterval = g;
    adminNotice();
  }
});
onMounted(() => {
  adminNotice();
})
const timer = ref();
function adminNotice() {
  if (timer.value) {
    clearInterval(timer.value);
  }
  /* 监听管理员公告 */
  const {
    liveSystemBarrageOpen,
    liveSystemBarrageInfo,
    liveSystemBarrageInterval,
  } = basicInfo?.value.eventOtherInfo!;
  if (liveSystemBarrageOpen === 1 && liveSystemBarrageInfo) {
    timer.value = setInterval(() => {
      chatList.value.push({
        name: "管理员",
        value: liveSystemBarrageInfo,
        org: "",
      });
      emitter.emit("adminLiveDanmu", {
        m: liveSystemBarrageInfo,
        n: '管理员',
        color: '#fff'
      })
    }, liveSystemBarrageInterval * 60 * 1000);
  }
}

function handleToggleLive(type: 1 | 2, e: MouseEvent) {
  const target = e.target as HTMLDivElement;
  const chatBarDOM = document.querySelector('#liveChatBar') as HTMLDivElement;
  let tempUrl = chatBarDOM.dataset.url;
  if (type === 1) {
    emitter.emit("onPlaySubLive", tempUrl);
  }
  if (type === 2) {
    emitter.emit("onPlayThreeLive", tempUrl);
  }
  emitter.emit("onPlayMainLive", target.dataset.url);
  chatBarDOM.dataset.url = target.dataset.url;
  target.dataset.url = tempUrl;
}
</script>

<template>
  <div class="chat-bar" id="liveChatBar" :data-url="liveUrls[0]?.flvUrl">
    <announceModal v-model:visible="visible" />
    <ysCollapse title="直播副画面1" :collapse="!liveUrls[1]">

      <template #icon v-if="liveUrls[1]">
        <div class="suv-video-point"></div>
      </template>

      <div class="suv-video">
        <div @click="(e) => handleToggleLive(1, e)" :data-url="liveUrls[1]?.flvUrl" v-if="liveUrls[1]"
          class="toggle-icon">
          <ysIcon class="icon" type="iconqiehuan" style="vertical-align: middle;" />
        </div>
        <span v-else class="label">暂无直播</span>
        <video src="" id="subVideo" muted></video>
      </div>
    </ysCollapse>

    <ysCollapse title="直播副画面2" :collapse="!liveUrls[2]">
      <template #icon v-if="liveUrls[2]">
        <div class="suv-video-point"></div>
      </template>
      <div class="suv-video">
        <div @click="(e) => handleToggleLive(2, e)" :data-url="liveUrls[2]?.flvUrl" v-if="liveUrls[2]"
          class="toggle-icon">
          <ysIcon class="icon" type="iconqiehuan" style="vertical-align: middle;" />
        </div>
        <span v-else class="label">暂无直播</span>
        <video src="" id="threeVideo" muted></video>
      </div>
    </ysCollapse>

    <ysCollapse title="直播互动">
      <template #icon v-if="isAdmin">
        <ysIcon @click.stop="visible = true" class="icon" type="icongonggao1" />
      </template>
      <!-- 公告 -->
      <div class="announce" v-if="announceInfo.show">
        <div class="announce-head">
          <div>
            <ysIcon type="icongonggao1" style="font-size: 20px;vertical-align: middle;margin-right: 4px;" />
            <span style="vertical-align: middle;">公告</span>
          </div>
          <ysIcon @click="announceInfo.show = false" type="icon-close" style="font-size: 20px;cursor: pointer;" />
        </div>
        <div class="announce-body">
          {{ announceInfo.value }}
        </div>
      </div>
      <!-- 公告 -->

      <div class="chat-list" id="chatList">
        <div v-for="chat in chatList" class="chat-item">
          <div class="name">
            {{ chat.name }}<span v-if="isOpemCommentOrg && chat.org">-{{ chat.org }}</span>：
          </div>
          <div class="value" v-html="chat.value">
          </div>
        </div>
        <ysEmpt v-if="chatList.length === 0">暂无互动</ysEmpt>
      </div>
    </ysCollapse>
  </div>
</template>

<style lang="scss" scoped>
.chat-bar {}

.suv-video-point {
  z-index: 9;
  top: 50%;
  left: 6px;
  position: absolute;
  width: 6px;
  height: 6px;
  background: #17BE6B;
  border-radius: 50%;
  transform: translateY(-50%);
}

.suv-video {
  position: relative;
  background: #262626;

  .toggle-icon {

    z-index: 9;
    position: absolute;
    right: 8px;
    top: 8px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    text-align: center;
    cursor: pointer;

    .icon {
      pointer-events: none;
      color: #fff;
    }
  }

  .label {
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  video {
    width: 100%;
    height: 180px;
    vertical-align: bottom;
  }
}

.chat-list {

  padding: 16px;
  position: relative;
  height: 612px;
  overflow: auto;

  .chat-item {
    overflow: hidden;

    .name {
      float: left;
      color: #8C8C8C;
    }

    .value {
      word-break: break-all;
    }

    &+.chat-item {
      margin-top: 14px;
    }
  }

}

.announce {
  background: #FFAA00;
  color: #fff;
  position: absolute;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;

  .announce-head {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .announce-body {
    word-break: break-all;
    padding: 0 16px 16px;
    max-height: 200px;
    overflow-y: auto;
  }
}

.icon {
  font-size: 20px;
  color: #8C8C8C;
}
</style>