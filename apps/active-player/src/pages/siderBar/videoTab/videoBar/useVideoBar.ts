import { inject, nextTick, onMounted, ref, Ref, watchEffect } from "vue";
import {
  getAllEventBarrageInfo,
  getAllCaptions,
  getAllPoint,
  saveLookAction,
  selectCaptions,
} from "@/api";
import { message } from "ant-design-vue";
import { useRoute } from "vue-router";
import { emitter, useInfo } from "@/hooks";
import { CommonRes, common } from "@ys/tools";
const { getSessionUser } = common;

type VideoDanmu = {
  eventBarrageId: string;
  barrageSendColor: string;
  barrageSendInfo: string;
  orgName: string;
  userName: string;
  userFace: string;
  eventAttrTime: number;
  barrageSendTime: number;
};
type VideoZimi = {
  e: string;
  c: string;
  s: string;
  id: string;
};
type VideoPoint = {
  markPicUrl: string;
  noteExplain: string;
  noteTime: number;
};

export function useVideoBar() {
  const route = useRoute();
  const { hasLiveRoom, videoList, videoAllowWatch, liveUrls, multiviewList } =
    useInfo();
  const id = inject<Ref<string>>("id");
  const currentEventAttr = ref({
    eventAttrId: "",
    eventAttrPath: "",
  });
  const isDoubleVideoMode = ref(false);
  const videoDanmu = ref<VideoDanmu[]>([]);
  const videoZimu = ref<VideoZimi[]>([]);
  const videoPoint = ref<VideoPoint[]>([]);

  function getVideoDanmu(eventAttrId: string): Promise<VideoDanmu[]> {
    return new Promise((resolve) => {
      getAllEventBarrageInfo<CommonRes<VideoDanmu[]>>({
        eventAttrId,
        eventInfoId: id?.value,
      }).then((result) => {
        resolve(result.data.data);
      });
    });
  }
  async function getVideoZimu(eventAttrId: string): Promise<VideoZimi[]> {
    return new Promise((resolve) => {
      selectCaptions<CommonRes<VideoZimi[]>>({
        objId: eventAttrId,
        objType: 1,
      }).then((result) => {
        resolve(result.data.data || []);
      });
    });
    // return new Promise((resolve) => {
    //   getAllCaptions<CommonRes<VideoZimi[]>>({
    //     eventAttrId,
    //   }).then((result) => {
    //     resolve(result.data.data);
    //   });
    // });
  }
  async function getPoint(eventAttrId: string): Promise<VideoPoint[]> {
    return new Promise((resolve) => {
      getAllPoint<CommonRes<VideoPoint[]>>({
        id: eventAttrId,
      }).then((result) => {
        resolve(result.data.data);
      });
    });
  }
  async function saveLookCount() {
    // if (!getSessionUser()) return
    await saveLookAction<CommonRes<null>>({
      lookType: 2,
      eventId: id?.value,
      deviceType: 1,
      attrId: currentEventAttr.value.eventAttrId,
    });
  }
  async function onChooseVideo({
    eventAttrId,
    eventAttrPath,
  }: {
    eventAttrId: string;
    eventAttrPath: string;
  }) {
    currentEventAttr.value = { eventAttrId, eventAttrPath };

    const danmuList = await getVideoDanmu(eventAttrId);
    videoDanmu.value = danmuList;

    const zimuList = await getVideoZimu(eventAttrId);
    videoZimu.value = zimuList;

    const pointList = await getPoint(eventAttrId);
    videoPoint.value = pointList;

    await saveLookCount();
    emitter.emit("onPlayVideo", {
      currentEventAttr: currentEventAttr.value,
      videoDanmu: [...videoDanmu.value],
      videoZimu: [...videoZimu.value],
      videoPoint: [...videoPoint.value],
    });
  }

  function onCancelDoubleVideo() {
    isDoubleVideoMode.value = false;
    onChooseVideo(videoList.value[0]);
  }

  async function onChooseDoubleVideo() {
    const { viewOneId, viewTwoId, viewThreeId, useCaptionType } =
      multiviewList.value[0];

    const view1Video = document.getElementById(
      "view1Video"
    ) as HTMLVideoElement;
    const view2Video = document.getElementById(
      "view2Video"
    ) as HTMLVideoElement;
    const view3Video = document.getElementById("view3Video");

    if (
      view1Video.dataset.canplay === "0" ||
      view2Video.dataset.canplay === "0"
    ) {
      message.info("多画面加载中，请稍后再试");
      return;
    }
    if (viewThreeId && view3Video?.dataset.canplay === "0") {
      message.info("多画面加载中，请稍后再试");
      return;
    }

    let targetEl = "mainVideo";
    let maxDuration = Math.max(
      Number(view1Video.dataset.duration),
      Number(view2Video.dataset.duration),
      Number(view3Video?.dataset.duration)
    );
    if (Number(view1Video.dataset.duration) === maxDuration) {
      targetEl = "mainVideo";
    }
    if (Number(view2Video.dataset.duration) === maxDuration) {
      targetEl = "subMainVideo";
    }
    if (Number(view3Video?.dataset.duration) === maxDuration) {
      targetEl = "threeMainVideo";
    }

    isDoubleVideoMode.value = true;

    const viewOneAttr = videoList.value.find(
      (video) => video.eventAttrId === viewOneId
    );
    currentEventAttr.value = viewOneAttr!;

    const danmuOneList = await getVideoDanmu(viewOneId);
    const danmuTwoList = await getVideoDanmu(viewTwoId);
    let danmuThreeList: any = [];
    if (viewThreeId) {
      danmuThreeList = await getVideoDanmu(viewThreeId);
    }
    const sortedDanmu = [
      ...danmuOneList,
      ...danmuTwoList,
      ...danmuThreeList,
    ].sort((pre, next) => {
      return pre.eventAttrTime - next.eventAttrTime;
    });

    videoDanmu.value = sortedDanmu;

    let zimuList: VideoZimi[] = [];
    if (useCaptionType === 1) {
      zimuList = await getVideoZimu(viewOneId);
    }
    if (useCaptionType === 2) {
      zimuList = await getVideoZimu(viewTwoId);
    }
    if (useCaptionType === 3) {
      zimuList = await getVideoZimu(viewThreeId);
    }
    videoZimu.value = zimuList;

    videoPoint.value = [];

    emitter.emit("onPlayDoubleVideo", {
      currentEventAttr: currentEventAttr.value,
      videoDanmu: sortedDanmu,
      videoZimu: zimuList,
      videoPoint: [],
      targetEl,
    });
  }

  onMounted(() => {
    if (hasLiveRoom.value) {
      emitter.emit("onPlayMainLive", liveUrls.value[0]?.flvUrl);
      emitter.emit("onPlaySubLive", liveUrls.value[1]?.flvUrl);
      emitter.emit("onPlayThreeLive", liveUrls.value[2]?.flvUrl);
    } else if (videoList.value.length > 0) {
      onChooseVideo(videoList.value[0]);
    }
  });
  watchEffect(() => {
    // 从资源中心跳转过来处理
    if (!route.query.attrId || !route.query.time) return;
    if (videoList.value.length === 0) return;
    if (!videoAllowWatch.value) return;
    const targetVideo = videoList.value.find(
      (video) => video.eventAttrId === route.query.attrId
    );
    if (targetVideo) {
      onChooseVideo(videoList.value[0]);
      emitter.emit("onLookPlayback");
      setTimeout(() => {
        const mainVideo = document.getElementById(
          "mainVideo"
        ) as HTMLVideoElement;
        mainVideo.currentTime = Number(route.query.time) / 1000;
      }, 1000);
    }
  });

  emitter.on("autoPlayNext", () => {
    if (isDoubleVideoMode.value) return;
    const index = videoList.value.findIndex(
      (video) => video.eventAttrId === currentEventAttr.value.eventAttrId
    );
    if (index + 1 < videoList.value.length) {
      onChooseVideo(videoList.value[index + 1]);
    }
  });
  emitter.on("onLookPlayback", () => {
    onChooseVideo(videoList.value[0]);
  });
  emitter.on("Toggle2Video", () => {
    if (videoList.value.length === 1 && videoAllowWatch.value === 1) {
      onChooseVideo(videoList.value[0]);
    }
  });
  emitter.on("Toggle2Live", () => {
    if (isDoubleVideoMode.value) {
      // 退出多画面点播
      isDoubleVideoMode.value = false;
    }
    if (!currentEventAttr.value.eventAttrId) return;
    // 正在录播的情况
    currentEventAttr.value = {
      eventAttrId: "",
      eventAttrPath: "",
    };
    videoDanmu.value = [];
    videoZimu.value = [];
    videoPoint.value = [];

    emitter.emit("onPlayMainLive", liveUrls.value[0]?.flvUrl);
    emitter.emit("onPlaySubLive", liveUrls.value[1]?.flvUrl);
    emitter.emit("onPlayThreeLive", liveUrls.value[2]?.flvUrl);
  });
  emitter.on("pushCustomDanmu", async () => {
    if (!isDoubleVideoMode.value) {
      const danmuList = await getVideoDanmu(currentEventAttr.value.eventAttrId);
      videoDanmu.value = danmuList;
    }
  });
  return {
    onChooseDoubleVideo,
    onCancelDoubleVideo,
    onChooseVideo,
    currentEventAttr,
    videoDanmu,
    videoZimu,
    isDoubleVideoMode,
  };
}
