<script lang="ts" setup>
import { ref, watch, nextTick, watchPostEffect } from "vue"
import { ysEmpt, ysCollapse, ysCreateEmojiUrl, ysIcon } from "@ys/ui"
import dayjs from "dayjs"
import { common } from "@ys/tools"
import videoItem from "./videoItem.vue";
import { useInfo, emitter } from "@/hooks"
import { useVideoBar } from "./useVideoBar";
const { videoList, videoAllowWatch, multiviewSetting, multiviewList, hasLiveRoom } = useInfo();
const { secondsFormat, isCdn } = common;
const { onChooseVideo, videoDanmu, videoZimu, currentEventAttr, onChooseDoubleVideo, isDoubleVideoMode, onCancelDoubleVideo } = useVideoBar();

function msgFormat(msg: string) {
  msg = msg.replace(/{emoji: (\d+)}/gi, function (index: any) {
    const [num] = index.match(/\d+/g)
    return `<img style="width:22px;height:22px;vertical-align: top;margin:0 2px" src=${ysCreateEmojiUrl(num - 2)}>`
  })
  msg = msg.replace(/{emoji:(\d+)}/gi, function (index: any) {
    const [num] = index.match(/\d+/g)
    return `<img style="width:22px;height:22px;vertical-align: top;margin:0 2px" src=${ysCreateEmojiUrl(num - 2)}>`
  })
  return msg;
}

const filterVideoDanmu = ref([...videoDanmu.value]);
watch(() => videoDanmu.value, val => {
  filterVideoDanmu.value = [...val];
}, { immediate: true })
const videoDanmuFilter = ref('');
function onDanmuSearch() {
  if (filterVideoDanmu.value) {
    filterVideoDanmu.value = videoDanmu.value.filter((item: any) => item.barrageSendInfo.indexOf(videoDanmuFilter.value) != -1)
    const reg = new RegExp(videoDanmuFilter.value, 'gi');
    filterVideoDanmu.value = filterVideoDanmu.value.map(damu => {
      const barrageSendInfo = damu.barrageSendInfo.replace(reg, function (txt: any) {
        return `<span style="color:#007AFF">${txt}</span>`
      })
      return {
        ...damu,
        barrageSendInfo
      }
    })
  } else {
    filterVideoDanmu.value = [...videoDanmu.value]
  }
}
function handle2Seek(currentTime: number) {
  const mainVideo = document.getElementById("mainVideo") as HTMLVideoElement;
  mainVideo!.currentTime = currentTime;
  if (isDoubleVideoMode.value) {
    const subMainVideo = document.getElementById("subMainVideo") as HTMLVideoElement;
    subMainVideo!.currentTime = currentTime;
  }
  // mainVideo.play();
}
/* 字幕 */
const filterVideoZimu = ref([...videoZimu.value]);
watch(() => videoZimu.value, val => {
  filterVideoZimu.value = [...val];
}, { immediate: true })
const videoZimuFilter = ref('');
function onZimuSearch() {
  if (videoZimuFilter.value) {
    filterVideoZimu.value = videoZimu.value.filter((item: any) => item.c.indexOf(videoZimuFilter.value) != -1)
    const reg = new RegExp(videoZimuFilter.value, 'gi');
    filterVideoZimu.value = filterVideoZimu.value.map(zimu => {
      const c = zimu.c.replace(reg, function (txt: any) {
        return `<span style="color:#007AFF">${txt}</span>`
      })
      return {
        ...zimu,
        c
      }
    })
  } else {
    filterVideoZimu.value = [...videoZimu.value]
  }
}


function findTargetVideoPath(eventAttrId: string) {
  return videoList.value.find(video => video.eventAttrId === eventAttrId)?.eventAttrPath
}

watchPostEffect(() => {
  if (multiviewSetting.value === 1) {
    const view1Video = document.getElementById('view1Video') as HTMLVideoElement;
    const view2Video = document.getElementById('view2Video') as HTMLVideoElement;
    const view3Video = document.getElementById('view3Video') as HTMLVideoElement;
    view1Video?.addEventListener('canplay', e => {
      view1Video.dataset.canplay = "1";
    })
    view2Video?.addEventListener('canplay', e => {
      view2Video.dataset.canplay = "1";
    })
    view3Video?.addEventListener('canplay', e => {
      view3Video.dataset.canplay = "1";
    })
    view1Video?.addEventListener('durationchange', e => {
      view1Video.dataset.duration = view1Video.duration + ''
    })
    view2Video?.addEventListener('durationchange', e => {
      view2Video.dataset.duration = view2Video.duration + ''
    })
    view3Video?.addEventListener('durationchange', e => {
      view3Video.dataset.duration = view3Video.duration + ''
    })
  }
})

const loop = ref(false);

</script>

<template>
  <div class="video-bar" :style="{ paddingTop: multiviewSetting === 1 ? '44px' : '0' }">

    <!-- 多画面点播 -->
    <div class="multiview" v-if="multiviewSetting === 1">
      <video preload="load" id="view1Video" data-duration="0" data-canplay="0" style="display: none;"
        :src="isCdn(findTargetVideoPath(multiviewList[0]?.viewOneId))"></video>
      <video preload="load" id="view2Video" data-duration="0" data-canplay="0" style="display: none;"
        :src="isCdn(findTargetVideoPath(multiviewList[0]?.viewTwoId))"></video>
      <video preload="load" id="view3Video" data-duration="0" data-canplay="0" style="display: none;"
        :src="isCdn(findTargetVideoPath(multiviewList[0]?.viewThreeId))"></video>
      <ysIcon class="icon" type="iconbuju" />
      <span style="flex:1;margin-left: 6px;">{{ !isDoubleVideoMode ? '该活动支持多画面点播' : '正在使用多画面模式' }}</span>
      <span v-if="!isDoubleVideoMode" @click="onChooseDoubleVideo" style="cursor: pointer">立即切换</span>
      <span v-else @click="onCancelDoubleVideo" style="cursor: pointer">返回普通模式</span>
    </div>
    <!-- 多画面点播 -->
    <template v-if="videoList.length > 1 && !isDoubleVideoMode">
      <div v-if="videoAllowWatch" class="video-list">
        <!-- 有直播的时候才有 -->
        <div class="video-list-head" v-if="hasLiveRoom">
          <div>录像列表</div>
          <div class="play-switch">
            <span class="label">自动连播</span>
            <a-switch class="auto-play-next" v-model:checked="loop" />
          </div>
        </div>
        <video-item :onChooseVideo="onChooseVideo" :currentEventAttrId="currentEventAttr.eventAttrId" v-bind="video"
          v-for="video in videoList" :key="video.eventAttrId" />
      </div>
      <div v-else class="video-list">
        <ysEmpt>录像上传中...</ysEmpt>
      </div>
    </template>
    <ysCollapse :title="`字幕 ${videoZimu.length}`" :collapse="!videoZimu.length">
      <div v-if="videoZimu.length === 0" :style="{ height: '300px', position: 'relative' }">
        <ysEmpt>暂无字幕</ysEmpt>
      </div>
      <div v-else>
        <div class="search">
          <a-input allow-clear @keydown.stop="" @input="onZimuSearch" v-model:value="videoZimuFilter"
            placeholder="请输入字幕相关信息">
            <template #prefix>
              <ysIcon type="icon31sousuo" style="color: #BFBFBF" />
            </template>
          </a-input>
        </div>
        <div class="zimu-list">
          <div class="zimu-body">
            <div v-if="filterVideoZimu.length" class="zimu-item" v-for="zimu in filterVideoZimu">
              <span @click="handle2Seek(Number(zimu.s) / 1000)" class="time">{{ secondsFormat(Number(zimu.s) / 1000,
                'HH:mm:ss') }}</span>
              <span class="value" v-html="zimu.c"></span>
            </div>
            <ysEmpt v-else>暂无搜索结果</ysEmpt>
          </div>
        </div>
      </div>
    </ysCollapse>
    <ysCollapse :title="`录像弹幕 ${videoDanmu.length}`" :collapse="!videoZimu.length">
      <div v-if="videoDanmu.length === 0" :style="{ height: '300px', position: 'relative' }">
        <ysEmpt>暂无弹幕</ysEmpt>
      </div>
      <div v-else>
        <div class="search">
          <a-input allow-clear @keydown.stop="" @input="onDanmuSearch" v-model:value="videoDanmuFilter"
            placeholder="请输入弹幕相关信息">
            <template #prefix>
              <ysIcon type="icon31sousuo" style="color: #BFBFBF" />
            </template>
          </a-input>
        </div>
        <div class="list">
          <div v-if="filterVideoDanmu.length" class="list-head">
            <span class="time">时间</span>
            <span class="value">弹幕内容</span>
            <span class="send-time">发送时间</span>
          </div>
          <div class="list-body">
            <div @click="handle2Seek(danmu.eventAttrTime)" v-if="filterVideoDanmu.length" class="list-item"
              v-for="danmu in filterVideoDanmu">
              <span class="time">{{ secondsFormat(danmu.eventAttrTime, 'HH:mm:ss') }}</span>
              <span class="value">
                <div class="text-overflow" v-html="msgFormat(danmu.barrageSendInfo)"></div>
              </span>
              <span class="send-time">{{ dayjs(danmu.barrageSendTime).format('MM-DD HH:mm') }}</span>
            </div>
            <ysEmpt v-else>暂无搜索结果</ysEmpt>
          </div>
        </div>
      </div>
    </ysCollapse>
  </div>
</template>

<style lang="scss" scoped>
.video-bar {
  width: 100%;
  position: relative;
}

.video-list {
  width: 100%;
  position: relative;
  overflow: auto;
  height: 176px;
  padding: 8px 0;

  .video-list-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    height: 40px;

    .play-switch {
      display: flex;
      align-items: center;

      .label {
        margin-right: 8px;
        color: #8C8C8C;
      }
    }
  }
}

.multiview {
  padding: 0 16px;
  z-index: 9;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #FFAA00;
  display: flex;
  align-items: center;
  color: #fff;

  .icon {
    font-size: 20px;
  }
}

.search {
  padding: 16px 16px 8px;
}

/* 字幕 */
.zimu-list {
  .zimu-body {
    position: relative;
    height: 300px;
    overflow: auto;
    padding: 0 16px;
  }

  .zimu-item {
    margin-top: 10px;

    .time {
      cursor: pointer;
      color: #8C8C8C;

      &:hover {
        color: #007AFF;
      }
    }

    .value {
      word-break: break-all;
      margin-left: 10px;
    }
  }
}

/* 弹幕 */
.list {
  .list-body {
    position: relative;
    height: 300px;
    overflow: auto;
  }

  .list-head .value {
    color: #8C8C8C;
  }

  .list-head,
  .list-item {
    display: flex;
    align-items: center;
    height: 36px;
    color: #8C8C8C;
    text-align: center;
  }

  .list-item {
    cursor: pointer;

    &:hover {
      background: #F5F5F5;

      .time,
      .value {
        color: #007AFF;
      }
    }
  }

  .time {
    flex: 1.4;
  }

  .value {
    width: 0;
    flex: 2;
    color: #262626;
  }

  .send-time {
    flex: 2;
  }
}
</style>
<style>
.hight-active {
  color: #007AFF;
}
</style>