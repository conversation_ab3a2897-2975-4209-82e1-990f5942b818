import { onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { getInfoForContent } from "./../../api";
import { CommonRes } from "@ys/tools";
interface Content {
  eventInfo: {
    eventName: string;
    eventCode: string;
  };
  eventOtherInfo: {
    captionOpen: 1 | 2;
    multiviewSetting: 1 | 2;
    videoAllowWatch: 1 | 2;
    videoIsDownload: 1 | 2;
  };
  videoList: {
    eventAttrId: string;
    eventAttrPath: string;
    eventAttrName: string;
    eventAttrSmallImgUrl: string;
    checked: boolean;
    caption: boolean;
    notes: number;
    orderNumber: number;
  }[];
  imageList: {
    eventAttrId: string;
    eventAttrName: string;
    eventAttrSmallImgUrl: string;
    checked: boolean;
    orderNumber: number;
  }[];
  attrList: {
    eventAttrId: string;
    eventAttrName: string;
    eventAttrPath: string;
    checked: boolean;
    orderNumber: number;
  }[];
  eventAptRecord: {
    homeworkList: {
      eventAptRecordHomeworkId: string;
      homeworkUrl: string;
      checked: boolean;
      orderNumber: number;
    }[];
  };
  eventMultiviewSettingList: {
    viewOneId: string;
    viewTwoId: string;
    viewThreeId: string;
    eventMultiviewSettingId: string;
    useCaptionType: number;
  }[];
}
export function useSetting() {
  const route = useRoute();
  const videoList = ref<Content["videoList"]>([]);
  const loading = ref(false);
  // 视频管理
  const captionOpen = ref<1 | 2>(2);
  const multiviewSetting = ref<1 | 2>(2);
  const videoAllowWatch = ref<1 | 2>(2);
  const videoIsDownload = ref<1 | 2>(2);

  const eventMultiviewSettingId = ref("0");
  const useCaptionType = ref(0);
  const view1Box = ref<Content["videoList"][number]>();
  const view2Box = ref<Content["videoList"][number]>();
  const view3Box = ref<Content["videoList"][number]>();

  async function getInfo() {
    loading.value = true;
    const result = await getInfoForContent<CommonRes<Content>>({
      eventId: route.params.id,
    });
    videoList.value = result.data.data.videoList.sort((a, b) => {
      return a.orderNumber - b.orderNumber;
    });

    multiviewSetting.value =
      result.data.data.eventOtherInfo.multiviewSetting || 2;
    videoAllowWatch.value =
      result.data.data.eventOtherInfo.videoAllowWatch || 2;
    videoIsDownload.value =
      result.data.data.eventOtherInfo.videoIsDownload || 2;
    captionOpen.value = result.data.data.eventOtherInfo.captionOpen || 2;

    const eventMultiviewSettingList =
      result.data.data.eventMultiviewSettingList;
    if (multiviewSetting.value && eventMultiviewSettingList) {
      eventMultiviewSettingId.value =
        eventMultiviewSettingList[0].eventMultiviewSettingId;
      useCaptionType.value = eventMultiviewSettingList[0].useCaptionType;
      view1Box.value = videoList.value.find(
        (item) => item.eventAttrId === eventMultiviewSettingList[0].viewOneId
      );
      view2Box.value = videoList.value.find(
        (item) => item.eventAttrId === eventMultiviewSettingList[0].viewTwoId
      );
      view3Box.value = videoList.value.find(
        (item) => item.eventAttrId === eventMultiviewSettingList[0].viewThreeId
      );
    }

    loading.value = false;
  }

  return {
    id: route.params.id,
    loading,
    videoList,
    getInfo,
    captionOpen,
    multiviewSetting,
    videoAllowWatch,
    videoIsDownload,

    view1Box,
    view2Box,
    view3Box,
    eventMultiviewSettingId,
    useCaptionType,
  };
}
