<script lang="ts" setup>
import { ref, watch } from "vue";
import { message } from "ant-design-vue";
import { setVideoIsDownload } from "./../../api";
import { ysLoading, ysIcon } from "@ys/ui";
import { CommonRes, common } from "@ys/tools";
import { useSetting } from "./useSetting";
const { isCdn } = common;
const props = defineProps<{
  visible: boolean;
}>();
interface Video {
  eventAttrId: string;
  eventAttrPath: string;
  eventAttrName: string;
  eventAttrSmallImgUrl: string;
  checked: boolean;
  caption: boolean;
  notes: number;
  orderNumber: number;
}
const emit = defineEmits(["update:visible"]);
const {
  loading,
  captionOpen,
  multiviewSetting,
  videoAllowWatch,
  videoIsDownload,
  getInfo,
  videoList,
  id,
  view1Box,
  view2Box,
  view3Box,
  eventMultiviewSettingId,
  useCaptionType,
} = useSetting();

watch(
  () => props.visible,
  (val) => {
    val && getInfo();
  }
);

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

function onStartDrag(e: any, video: Video) {
  e.dataTransfer.setData("id", video.eventAttrId);
}
function onDragover(e: any, dom: string) {
  const targetDom = document.querySelector(dom) as HTMLDivElement;
  targetDom.style.outlineColor = "#007AFF";
  e.preventDefault();
}
function onDragleave(e: any, dom: string) {
  const targetDom = document.querySelector(dom) as HTMLDivElement;
  targetDom.style.outlineColor = "transparent";
  e.preventDefault();
}
function onDrop(e: any, dom: string) {
  var data = e.dataTransfer.getData("id");
  const targetDom = document.querySelector(dom) as HTMLDivElement;
  targetDom.style.outlineColor = "transparent";
  if (!data) return;
  const target = videoList.value.find((item) => item.eventAttrId === data);
  if (dom === ".view-box-1") {
    view1Box.value = target;
  } else if (dom === ".view-box-2") {
    view2Box.value = target;
  } else if (dom === ".view-box-3") {
    view3Box.value = target;
  }
}

async function submit() {
  if (multiviewSetting.value === 1) {
    if (!view1Box.value) {
      message.info("请设置主画面");
      return;
    }
    if (!view2Box.value && !view3Box.value) {
      message.info("请设置副画面");
      return;
    }
    // if (view1Box.value.eventAttrId === view2Box.value.eventAttrId) {
    //   message.info('请拖入不同的视频')
    //   return
    // }
    // if (view1Box.value.eventAttrId === view3Box.value.eventAttrId) {
    //   message.info('请拖入不同的视频')
    //   return
    // }
    // if (view2Box.value.eventAttrId === view3Box.value.eventAttrId) {
    //   message.info('请拖入不同的视频')
    //   return
    // }
  }
  let viewTwoId = view2Box.value?.eventAttrId;
  let viewThreeId = view3Box.value?.eventAttrId;
  if (!viewTwoId) {
    // 修正画面2缺失
    viewTwoId = viewThreeId;
    viewThreeId = "";
    if (useCaptionType.value === 3) {
      useCaptionType.value = 2;
    }
  }
  const result = await setVideoIsDownload<CommonRes<null>>({
    eventId: id,
    videoAllowWatch: videoAllowWatch.value,
    videoIsDownload: videoIsDownload.value,
    multiviewSetting: multiviewSetting.value,
    eventMultiviewSetting: {
      eventInfoId: id,
      eventMultiviewSettingId: eventMultiviewSettingId.value,
      viewOneId: view1Box.value?.eventAttrId,
      viewTwoId,
      viewThreeId,
      useCaptionType: useCaptionType.value,
    },
    captionOpen: captionOpen.value,
  });
  if (result.data.code === 0) {
    message.success("保存成功");
  }
}
</script>

<template>
  <a-drawer
    :visible="visible"
    width="1020px"
    :bodyStyle="{ padding: 0 }"
    :closable="false"
    title="视频管理"
    placement="right"
  >
    <template #extra>
      <ysIcon
        @click="emit('update:visible', false)"
        class="icon"
        type="icon-close"
      />
    </template>
    <div class="setting-container">
      <ysLoading :loading="loading" />
      <a-form class="form" v-bind="layout">
        <a-form-item label="录像观看">
          <a-switch
            v-model:checked="videoAllowWatch"
            :unCheckedValue="2"
            :checkedValue="1"
          ></a-switch>
        </a-form-item>
        <a-form-item label="录像下载">
          <a-switch
            v-model:checked="videoIsDownload"
            :unCheckedValue="2"
            :checkedValue="1"
          ></a-switch>
        </a-form-item>
        <a-form-item label="多画面模式">
          <a-switch
            style="margin-top: 8px"
            v-model:checked="multiviewSetting"
            :unCheckedValue="2"
            :checkedValue="1"
          ></a-switch>
          <div class="multiview" v-show="multiviewSetting === 1">
            <div class="view">
              <div class="label">画面1</div>
              <div
                class="view-box view-box-1"
                @drop="(e) => onDrop(e, '.view-box-1')"
                @dragover="(e) => onDragover(e, '.view-box-1')"
                @dragleave="(e) => onDragleave(e, '.view-box-1')"
              >
                请将右侧待选视频拖入
                <img
                  v-if="view1Box"
                  :src="isCdn(view1Box.eventAttrSmallImgUrl)"
                  alt=""
                />
                <div v-if="view1Box" class="text-overflow">
                  {{ view1Box?.eventAttrName }}
                </div>
              </div>
            </div>
            <div class="view">
              <div class="label">画面2</div>
              <div
                class="view-box view-box-2"
                @drop="(e) => onDrop(e, '.view-box-2')"
                @dragover="(e) => onDragover(e, '.view-box-2')"
                @dragleave="(e) => onDragleave(e, '.view-box-2')"
              >
                请将右侧待选视频拖入
                <img
                  v-if="view2Box"
                  :src="isCdn(view2Box.eventAttrSmallImgUrl)"
                  alt=""
                />
                <div v-if="view2Box" class="text-overflow">
                  {{ view2Box?.eventAttrName }}
                </div>
              </div>
            </div>
            <div class="view">
              <div class="label">画面3</div>
              <div
                class="view-box view-box-3"
                @drop="(e) => onDrop(e, '.view-box-3')"
                @dragover="(e) => onDragover(e, '.view-box-3')"
                @dragleave="(e) => onDragleave(e, '.view-box-3')"
              >
                请将右侧待选视频拖入
                <img
                  v-if="view3Box"
                  :src="isCdn(view3Box.eventAttrSmallImgUrl)"
                  alt=""
                />
                <div v-if="view3Box" class="text-overflow">
                  {{ view3Box?.eventAttrName }}
                </div>
              </div>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="字幕" v-if="multiviewSetting === 1">
          <a-select style="width: 320px" v-model:value="useCaptionType">
            <a-select-option :value="0">不使用字幕</a-select-option>
            <a-select-option :value="1">使用画面1的字幕</a-select-option>
            <a-select-option :value="2">使用画面2的字幕</a-select-option>
            <a-select-option :value="3">使用画面3的字幕</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="画面布局" v-if="multiviewSetting === 1">
          <div class="layout-preview">
            <div class="prev1">
              画面1
              <img
                v-if="view1Box"
                :src="isCdn(view1Box.eventAttrSmallImgUrl)"
                alt=""
              />
            </div>
            <div style="flex: 1">
              <div class="prev2">
                画面2
                <img
                  v-if="view2Box"
                  :src="isCdn(view2Box.eventAttrSmallImgUrl)"
                  alt=""
                />
              </div>
              <div class="prev3">
                画面3
                <img
                  v-if="view3Box"
                  :src="isCdn(view3Box.eventAttrSmallImgUrl)"
                  alt=""
                />
              </div>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="字幕">
          <a-radio-group v-model:value="captionOpen">
            <a-radio :value="1">默认开</a-radio>
            <a-radio :value="2">默认关</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item :wrapper-col="{ ...layout.wrapperCol, offset: 4 }">
          <a-space>
            <a-button @click="submit" type="primary">确定</a-button>
            <a-button @click="emit('update:visible', false)">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
      <div
        class="video-list"
        :style="{ visibility: multiviewSetting === 1 ? 'visible' : 'hidden' }"
      >
        <div
          @dragstart="(e: any) => onStartDrag(e, video)"
          draggable
          class="video-item"
          v-for="video in videoList"
        >
          <img :src="isCdn(video.eventAttrSmallImgUrl)" alt="" />
          <div class="desc text-overflow">{{ video.eventAttrName }}</div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<style lang="scss" scoped>
.icon {
  font-size: 20px;
}

.setting-container {
  height: 100%;
  display: flex;
}

.form {
  flex: 1;
  padding-top: 24px;
  overflow-y: auto;
  height: 100%;
}

.multiview {
  display: flex;
  margin-top: 24px;

  .view {
    margin-right: 16px;

    .label {
      font-weight: 500;
      margin-bottom: 12px;
    }

    .view-box {
      outline: 1px dashed transparent;
      outline-offset: 2px;
      width: 196px;
      height: 110px;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #8c8c8c;
      position: relative;

      img {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      > div {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.45);
        color: #fff;
        padding: 0 8px;
        line-height: 40px;
      }
    }
  }
}

.layout-preview {
  width: 440px;
  height: 246px;
  background: #262626;
  padding: 32px 0;
  display: flex;
  color: #8c8c8c;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .prev1 {
    position: relative;
    width: 300px;
    height: 100%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .prev2 {
    position: relative;
    margin-left: 2px;
    height: 90px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .prev3 {
    position: relative;
    margin-left: 2px;
    margin-top: 2px;
    height: 90px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.video-list {
  overflow-y: auto;
  height: 100%;
  padding: 24px;
  width: 244px;
  border-left: 1px solid #f0f0f0;

  .video-item {
    height: 110px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }

    .desc {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 40px;
      background: rgba(0, 0, 0, 0.45);
      color: #fff;
      padding: 0 8px;
      line-height: 40px;
    }
  }
}
</style>
