<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { ysIcon } from "@ys/ui";
import { settingMenu, componentsMap } from "./entity";
import { common } from "@ys/tools";
import { useInfo, emitter } from "@/hooks";
const { id, eventType, eventClassify } = useInfo();
const { bureauBlankOpen } = common;
const router = useRouter();
const settingVisible = ref(false);
const selectId = ref("1");

emitter.on("showSetting", (e: unknown) => {
  selectId.value = e as string;
  settingVisible.value = true;
});

function handleChoose(menu: any) {
  /* 内容管理 */
  if (menu.id === "6") {
    const link = router.resolve(`/active-content/${id?.value}`);
    bureauBlankOpen(link.href, false, "_self");
    return;
  }
  if (menu.childrens) return;
  selectId.value = menu.id;
}

function onClose() {
  settingVisible.value = false;
  emitter.emit("updateEvent");
}

const menuList = ref(settingMenu);
// 自定义活动
if (
  Number(eventType.value) > 7 &&
  Number(eventType.value) != 12 &&
  eventClassify?.value?.isLive !== 1
) {
  menuList.value = settingMenu.filter((item) => item.id !== "2");
}
if ((window as any).ysActive.showMaiMeng == 0) {
  const targetMenu = menuList.value.find((item) => item.id === "3")!;
  targetMenu.childrens = targetMenu.childrens?.filter(
    (item) => item.id != "3-6"
  );
}
</script>

<template>
  <!-- <section ref="active_player_seting_drawer"> -->
  <a-drawer
    :bodyStyle="{ padding: 0 }"
    :destroyOnClose="true"
    :width="1080"
    title="设置"
    :visible="settingVisible"
    :closable="false"
  >
    <!-- :getContainer="() => $refs.active_player_seting_drawer" -->
    <template #extra>
      <ysIcon @click="onClose" class="icon" type="icon-close" />
    </template>
    <div class="container">
      <div class="content">
        <component
          :is="componentsMap[selectId as keyof typeof componentsMap]"
        ></component>
      </div>
      <div class="menus">
        <div
          @click="handleChoose(menu)"
          :class="['menu', { active: selectId[0] === menu.id }]"
          v-for="menu in menuList"
          :key="menu.id"
        >
          <template v-if="menu.childrens">
            <div class="sub-menu">
              <div
                @click.stop="handleChoose(sub)"
                :class="['sub-menu-item', { active: selectId === sub.id }]"
                v-for="sub in menu.childrens"
                :key="sub.id"
              >
                <ysIcon v-if="sub.icon" class="icon" :type="sub.icon" />
                <img width="22" v-else :src="sub.url" alt="" />
                <div class="sub-menu-name">{{ sub.label }}</div>
              </div>
            </div>
          </template>
          <div class="menu-icon">
            <ysIcon class="icon" :type="menu.icon" />
          </div>
          <div class="menu-name">{{ menu.label }}</div>
        </div>
      </div>
    </div>
  </a-drawer>
  <!-- </section> -->
</template>

<style lang="scss" scoped>
.icon {
  cursor: pointer;
  font-size: 20px;
}

.container {
  height: 100%;
  display: flex;

  .content {
    position: relative;
    flex: 1;
    padding: 24px;
    overflow-y: auto;
  }

  .menus {
    width: 144px;
    background: #eee;
    border-left: 1px solid #ebeef5;
  }

  .menu {
    cursor: pointer;
    position: relative;
    cursor: pointer;
    padding: 14px 0;
    text-align: center;

    &:hover {
      background: #e6f2ff;

      .menu-name {
        color: #007aff;
      }

      .sub-menu {
        display: block;
      }
    }

    &.active {
      background: #e6f2ff;

      .menu-name {
        color: #007aff;
      }
    }

    .menu-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #007aff;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      .icon {
        font-size: 24px;
        color: #fff;
      }
    }

    .menu-name {
      margin-top: 10px;
      color: #8c8c8c;
    }
  }

  .sub-menu {
    z-index: 9;
    display: none;
    box-shadow: 0 0 8px 0 rgba(0, 0, 82, 0.06);
    padding: 4px 0;
    position: absolute;
    top: 0;
    right: 100%;
    width: 160px;
    background: #ffffff;
    border-radius: 2px 0px 0px 2px;

    .sub-menu-item {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 14px;

      &:hover {
        background: #f7f7f7;
      }

      .icon {
        color: #8c8c8c;
      }

      .sub-menu-name {
        margin-left: 8px;
      }

      &.active {
        background: #e6f2ff;
        color: #007aff;

        .icon {
          color: #007aff;
        }
      }
    }
  }
}
</style>
