import { createRouter, createWebHashHistory } from "vue-router";
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/:id",
      component: () => import("@/pages/index.vue"),
    },
    {
      path: "/active-content/:id",
      component: () => import("@/pages/content/index.vue"),
    },
    {
      path: "/active-ai/:id",
      component: () =>
        import("@/pages/setting/application/videoAudioBrain/report/index.vue"),
      children: [
        {
          path: "",
          component: () =>
            import(
              "@/pages/setting/application/videoAudioBrain/report/analys.vue"
            ),
        },
        {
          path: "/active-ai/class-diff/:id",
          component: () =>
            import(
              "@/pages/setting/application/videoAudioBrain/report/_clasStruPort/index.vue"
            ),
        },
      ],
    },
    /* 供教研预览 */
    {
      path: "/claStruPortView",
      props: {
        isView: true,
      },
      component: () =>
        import(
          "@/pages/setting/application/videoAudioBrain/report/_clasStruPort/index.vue"
        ),
    },
    {
      path: "/chat",
      component: () => import("@/pages/chatAI.vue"),
    },
    {
      path: "/InvalidLink",
      component: () => import("@/pages/siderBar/achievement/analyReport-new/InvalidLink.vue"),
    },
    {
      path: "/activity-report/:reportId",
      component: () => import("@/pages/siderBar/achievement/analyReport-new/index.vue"),
    },
  ],
});

router.beforeEach((to, from, next) => {
  if (from.query.bureauId && !to.query.bureauId) {
    let query = to.query;
    query.bureauId = from.query.bureauId;
    query.jd = from.query.jd;
    next({
      path: to.path,
      query: query,
    });
  } else {
    next();
  }
});
export default router;
