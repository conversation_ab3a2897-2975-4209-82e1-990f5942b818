import {
  auditR<PERSON>ord,
  getResourceList,
  getUnAuditCount,
  exportResource,
  exportTemplate,
  exportClassHour,
  delResource,
  exportClassScore,
  exportClassScoreTemplate,
  getResourceListHour,
  getResourceListScore,
  HourResourceList
} from "@/request";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import { ref, reactive, watch } from "vue";
import useRouterUtils from "./useRouterUtils";
import { infoHidden } from "@/utils";

export enum auditStatus {
  pending = "0",
  resolve = "1",
  reject = "2",
  noSubmit = "3"
}

export interface resourceParamsProps {
  name: null | string;
  startTime: null | string;
  endTime: null | string;
  type: null | string;
  month: null | string;
  pageNo: number;
  pageSize: number;
  status: null | string;
  queryType: null | string;
  sortType: number;
  userId: null | string;
  dataSource: null | number;
  sourceType: null | string | number
  // eduResourceIds: null | Array<string>;
}

export interface resourceItemProps {
  eduResourceId: string;
  resourceName: string;
  userName: string;
  idCard: string;
  resourceType: number;
  classHour: number;
  status: number;
  createTime: string;
  createTimestamp: number;
}

const useResourceList = () => {
  const resourceListHour = ref<Array<resourceItemProps>>([]);
  const resourceListScore = ref<Array<resourceItemProps>>([]);
  const resourceList = ref<Array<resourceItemProps>>([]);
  const orgName = ref<any>(null)
  const applyTypeOptions = [
    { label: "灵活性", value: "1" },
    { label: "规范性", value: "2" },
  ];

  const applyStatusOptions = [
    { label: "未提交", value: auditStatus.noSubmit },
    { label: "待审核", value: auditStatus.pending },
    { label: "已通过", value: auditStatus.resolve },
    { label: "已拒绝", value: auditStatus.reject },
  ];

  const applyStatusOptions2 = [
    { label: "已通过", value: auditStatus.resolve },
    { label: "已拒绝", value: auditStatus.reject },
  ];
  const applyStatusOptions3 = [
    { label: "待审核", value: auditStatus.pending },
    { label: "已通过", value: auditStatus.resolve },
    { label: "已拒绝", value: auditStatus.reject },
  ];
  const loading = ref(false);
  const resourceTotal = ref(0);
  const resourceHourTotal = ref(0);
  const resourceScoreTotal = ref(0);
  const resourceDate = ref<any[]>([]);
  const disabledDate = (current: any) => {
    if (!resourceDate.value || resourceDate.value.length === 0) {
      return false;
    }
    const diffDate = current.diff(resourceDate.value[0], "days");
    return Math.abs(diffDate) < 365;
  };

  const onCalendarChange = (val: any) => {
    if (val == null) {
      resourceDate.value = [];
    } else if (val[0] && !val[1]) {
      let start = val[0];
      let end = String(Number(val[0]) + 1);
      resourceDate.value = [start, end];
    } else {
      resourceDate.value = val;
    }
  };

  const resourceParams: resourceParamsProps = reactive({
    name: null,
    startTime: null,
    endTime: null,
    month: null,
    type: null,
    pageNo: 1,
    pageSize: 10,
    status: null,
    sortType: 2,
    queryType: null,
    userId: null,
    dataSource: null,
    sourceType: null,
    //导出学时/学分列表所选择Id集合
    // eduResourceIds: null
    // 1为学时列表，2为学分列表
  });
  const rejectReasonVisible = ref(false);
  const batchDeclareVisible = ref(false);
  const selectedRowKeys = ref<Array<string>>([]);
  const { routerNewPage } = useRouterUtils();

  const onSelectRowChange = (data: string[]) => {
    selectedRowKeys.value = data;
  };
  const onSelectRowChange1 = (data: string[], rows: any) => {
    // data.forEach(item => selectedRowKeys.value.push(item))
    // selectedRowKeys.value = uniqueArray(selectedRowKeys.value)

  };
  // 手动选中/取消所有列
  const onChooseAllChange1 = (selected: any, selectedRows: any, changeRows: any) => {
    if (!selected) {
      for (let i = 0; i < selectedRowKeys.value.length; i++) {
        for (let j = 0; j < changeRows.length; j++) {
          if (selectedRowKeys.value[i] == changeRows[j].eduResourceId) {
            selectedRowKeys.value.splice(i, 1)
            i--
          }
        }
      }
    }
    else {
      for (let i = 0; i < selectedRows.length; i++) {
        for (let j = 0; j < changeRows.length; j++) {
          if (selectedRows[i] != undefined) {
            if (selectedRows[i].eduResourceId == changeRows[j].eduResourceId) {
              selectedRowKeys.value.push(selectedRows[i].eduResourceId)
            }
          }
        }
      }
    }
  }

  // 手动选中/取消单列
  const onChooseChange1 = (rows: any, boo: any) => {
    // console.log('row',rows)
    // console.log('boo',boo)
    if (!boo) {
      for (let i = 0; i < selectedRowKeys.value.length; i++) {
        if (selectedRowKeys.value[i] == rows.eduResourceId) {
          selectedRowKeys.value.splice(i, 1)
        }
      }

    }
    else {
      selectedRowKeys.value.push(rows.eduResourceId)
    }
    console.log(selectedRowKeys.value)
  }

  const getCheckboxProps = (record: resourceItemProps) => ({
    disabled: String(record.status) != auditStatus.pending,
    name: record.resourceName,
  });

  const resourceDateWatch = watch(
    () => resourceDate.value,
    (newV) => {
      if (newV == null) {
        resourceParams.startTime = null;
        resourceParams.endTime = null;
      } else if (newV.length > 0) {
        resourceParams.startTime = newV[0];
        resourceParams.endTime = newV[1];
      } else {
        resourceParams.startTime = null;
        resourceParams.endTime = null;
      }
    }
  );

  // 获取待审核总数
  const unAuditCount = ref(0);
  const getResouceUnAuditCount = async (num: any) => {
    const response = await getUnAuditCount({ type: num == 2 ? num : null });
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      unAuditCount.value = res.data;
    }
  };
  // 获取学分管理列表
  const getScoreResourceList = async () => {
    loading.value = true;
    let params = { relationOrgid: orgName.value, ...resourceParams }
    // let response = await getResourceListScore(params);
    let response = await HourResourceList(params)
    loading.value = false;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      resourceScoreTotal.value = Number(res.totalDatas);
      if (res.data) {
        resourceListScore.value = res.data.map((item: resourceItemProps) => {
          item.createTimestamp = new Date(item.createTime).getTime();
          item.idCard = infoHidden(2, item.idCard);
          return item;
        });
      } else {
        resourceListScore.value = [];
      }
    }
  };
  // 获取学时管理列表
  const getHourResourceList = async () => {
    loading.value = true;
    let params = { relationOrgid: orgName.value, ...resourceParams }
    // let response = await getResourceListHour(params);
    let response = await HourResourceList(params);
    HourResourceList
    loading.value = false;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      resourceHourTotal.value = Number(res.totalDatas);
      if (res.data) {
        resourceListHour.value = res.data.map((item: resourceItemProps) => {
          item.createTimestamp = new Date(item.createTime).getTime();
          item.idCard = infoHidden(2, item.idCard);
          return item;
        });
      } else {
        resourceListHour.value = [];
      }
    }
  };
  // 列表数据请求
  const getResouceAuditList = async (needCount: boolean = false) => {
    try {
      loading.value = true;
      let params = { relationOrgid: orgName.value, ...resourceParams }
      const response = await getResourceList(params);
      loading.value = false;
      const res = response.data as any;
      if (res.code == 0 && res.success) {
        resourceTotal.value = Number(res.totalDatas);
        if (res.data) {
          resourceList.value = res.data.map((item: resourceItemProps) => {
            item.createTimestamp = new Date(item.createTime).getTime();
            item.idCard = infoHidden(2, item.idCard);
            return item;
          });
        } else {
          resourceList.value = [];
        }
        if (needCount) getResouceUnAuditCount(1);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    }
  };

  // 查询
  const handleResourceQuery = () => {
    resourceParams.pageNo = 1;
    getResouceAuditList();
  };
  const handleResourceQuery2 = () => {
    resourceParams.pageNo = 1;

    getHourResourceList();
  };
  const handleResourceQuery3 = () => {
    resourceParams.pageNo = 1;
    getScoreResourceList();
  };
  function newSiftSearchForHours(obj: any) {
    orgName.value = obj.orgValue
    resourceParams.name = obj.name;
    resourceParams.type = obj.type;
    resourceParams.startTime = null;
    resourceParams.endTime = null;
    if (!!obj.resourceDate && obj.resourceDate.length) {
      resourceParams.startTime = obj.resourceDate[0];
      resourceParams.endTime = obj.resourceDate[1];
    }
    resourceParams.status = obj.status;
    handleResourceQuery2();
  }
  function newSiftSearchForScore(obj: any) {
    orgName.value = obj.orgValue
    resourceParams.name = obj.name;
    resourceParams.type = obj.type;
    resourceParams.startTime = null;
    resourceParams.endTime = null;
    if (!!obj.resourceDate && obj.resourceDate.length) {
      resourceParams.startTime = obj.resourceDate[0];
      resourceParams.endTime = obj.resourceDate[1];
    }
    resourceParams.status = obj.status;
    handleResourceQuery3();
  }
  /* 更新查询条件 */
  function newSiftSearch(obj: any) {
    orgName.value = obj.orgValue
    resourceParams.name = obj.name;
    resourceParams.type = obj.type;
    resourceParams.startTime = null;
    resourceParams.endTime = null;
    if (!!obj.resourceDate && obj.resourceDate.length) {
      resourceParams.startTime = obj.resourceDate[0];
      resourceParams.endTime = obj.resourceDate[1];
    }
    resourceParams.status = obj.status;
    handleResourceQuery();
  }
  // 重置学分管理列表
  const handleResSourceReset = () => {
    orgName.value = null
    resourceDate.value = [];
    selectedRowKeys.value = [];
    resourceParams.pageNo = 1;
    resourceParams.name = "";
    resourceParams.startTime = null;
    resourceParams.endTime = null;
    resourceParams.type = null;
    resourceParams.status = null;
    resourceParams.sortType = 2;
    getScoreResourceList();
  };
  // 重置学时管理列表
  const handleResourceHourReset = () => {
    orgName.value = null
    resourceDate.value = [];
    selectedRowKeys.value = [];
    resourceParams.pageNo = 1;
    resourceParams.name = "";
    resourceParams.startTime = null;
    resourceParams.endTime = null;
    resourceParams.type = null;
    resourceParams.status = null;
    resourceParams.sortType = 2;
    getHourResourceList();
  };
  // 重置
  const handleResourceReset = () => {
    orgName.value = null
    resourceDate.value = [];
    selectedRowKeys.value = [];
    resourceParams.pageNo = 1;
    resourceParams.name = "";
    resourceParams.startTime = null;
    resourceParams.endTime = null;
    resourceParams.type = null;
    resourceParams.status = null;
    resourceParams.sortType = 2;
    getResouceAuditList();
  };
  // 分页
  const handlePaginationChange3 = (page: number, pageSize: number) => {
    // 清空下勾选项
    // selectedRowKeys.value = [];
    resourceParams.pageNo = page;
    resourceParams.pageSize = pageSize;
    getScoreResourceList();
  };
  // 分页
  const handlePaginationChange2 = (page: number, pageSize: number) => {
    // 清空下勾选项
    // selectedRowKeys.value = [];
    resourceParams.pageNo = page;
    resourceParams.pageSize = pageSize;
    getHourResourceList();
  };
  // 分页
  const handlePaginationChange = (page: number, pageSize: number) => {
    // 清空下勾选项
    selectedRowKeys.value = [];
    resourceParams.pageNo = page;
    resourceParams.pageSize = pageSize;
    getResouceAuditList();
  };

  const computedStatusText = (status: number) => {
    let result = {
      text: "",
      className: "",
    };
    if (status == 0) {
      result.text = "待审核";
      result.className = "pending";
    } else if (status == 1) {
      result.text = "已通过";
      result.className = "resolve";
    } else if (status == 2) {
      result.text = "已拒绝";
      result.className = "reject";
    } else if (status == 3) {
      result.text = "未提交";
      result.className = "noSubmit";
    }
    return result;
  };

  const computedResourceTypeText = (resourceType: number) => {
    return resourceType == 1 ? "灵活性" : "规范性";
  };

  // 单条审核通过
  const handleAuditOnePass = async (record: resourceItemProps) => {
    let params = {
      ids: [record.eduResourceId],
      type: 1,
    };
    try {
      loading.value = true;
      const response = await auditRecord(params);
      loading.value = false;
      const res = response.data as any;
      if (res.code == 0 && res.success) {
        message.success("审核成功");
        getResouceAuditList(true);
      }
    } catch (error) {
      message.error("审核失败");
    }
  };

  // 单条审核拒绝
  const handleAuditOneRejectOpen = (record: resourceItemProps) => {
    rejectReasonVisible.value = true;
    selectedRowKeys.value = [record.eduResourceId];
  };
  // 单条审核拒绝
  const handleAuditOneRejectCancel = () => {
    rejectReasonVisible.value = false;
    selectedRowKeys.value = [];
  };
  // 单条审核拒绝
  const handleAuditOneRejectConfirm = async (value: string) => {
    let params = {
      ids: selectedRowKeys.value,
      type: 2,
      reason: value,
    };
    try {
      loading.value = true;
      const response = await auditRecord(params);
      loading.value = false;
      const res = response.data as any;
      if (res.code == 0 && res.success) {
        message.success("审核成功");
        handleAuditOneRejectCancel();
        getResouceAuditList(true);
      }
    } catch (error) {
      message.error("审核失败");
    }
  };

  // 多条审核
  const handleBatchAudit = async () => {
    if (selectedRowKeys.value.length == 0) {
      message.error("至少选择一条数据");
      return false;
    }
    batchDeclareVisible.value = true;
  };

  const handleBatchDefine = async (payload: any) => {
    const { declare, value } = payload;
    let params = {
      ids: selectedRowKeys.value,
      type: declare,
      reason: value,
    };
    try {
      loading.value = true;
      const response = await auditRecord(params);
      loading.value = false;
      const res = response.data as any;
      if (res.code == 0 && res.success) {
        message.success("审核成功");
        selectedRowKeys.value = [];
        batchDeclareVisible.value = false;
        getResouceAuditList();
      }
    } catch (error) {
      message.error("审核失败");
    }
  };

  // 跳转到编辑
  const handleAuditEdit = (record: resourceItemProps) => {
    routerNewPage(`/client/apply-edit/${record.eduResourceId}`);
  };
  function handleDeleteProject3(ids: Array<string>) {
    let bol =
      resourceParams.pageNo > 1 && ids.length == resourceListScore.value.length;
    if (bol) resourceParams.pageNo = resourceParams.pageNo - 1;
    console.log(bol);
    delResource(ids).then((res: any) => {
      if (res.data.code == 0) {
        message.success("已删除");
        selectedRowKeys.value = [];
        getScoreResourceList();
      }
    });
  }
  function handleDeleteProject2(ids: Array<string>) {
    let bol =
      resourceParams.pageNo > 1 && ids.length == resourceListHour.value.length;
    if (bol) resourceParams.pageNo = resourceParams.pageNo - 1;
    delResource(ids).then((res: any) => {
      if (res.data.code == 0) {
        message.success("已删除");
        selectedRowKeys.value = [];
        getHourResourceList();
      }
    });
  }
  // 删除
  function handleDeleteProject(ids: Array<string>) {
    let bol =
      resourceParams.pageNo > 1 && ids.length == resourceList.value.length;
    if (bol) resourceParams.pageNo = resourceParams.pageNo - 1;
    delResource(ids).then((res: any) => {
      if (res.data.code == 0) {
        message.success("已删除");
        selectedRowKeys.value = [];
        getResouceAuditList();
      }
    });
  }

  function handleTabSort2(page: any, data: any, order: any) {
    resourceParams.sortType = order.order == "ascend" ? 1 : 2;
    resourceParams.pageNo = 1;
    getResouceAuditList();
  }
  // 表格排序
  function handleTabSort(page: any, data: any, order: any) {
    resourceParams.sortType = order.order == "ascend" ? 1 : 2;
    resourceParams.pageNo = 1;
    getHourResourceList();
    // getResouceAuditList();
  }
  // 表格排序
  function handleTabSort1(page: any, data: any, order: any) {
    resourceParams.sortType = order.order == "ascend" ? 1 : 2;
    resourceParams.pageNo = 1;
    getScoreResourceList();
  }
  /* 状态处理 */
  function handleStatusSift(num: number) {
    let obj = {
      color: num == 0 ? "#FFAA00" : num == 1 ? "#17BE6B" : "#F53F3F",
      text: num == 0 ? "待审核" : num == 1 ? "已通过" : "已拒绝",
    };
    return obj;
  }
  /* 导出 type 1-学时列表，2学时导入模板，3学分列表，4学分导入模板 */
  async function handleExport(type: number, active?: any) {
    let name = "";
    let res: any;
    let obj: any = { ...resourceParams }
    if (type == 1) {
      name = "学时列表.xlsx";
      let obj: any = { ...resourceParams }
      obj.relationOrgid = orgName.value
      obj.eduResourceIds = selectedRowKeys.value
      res = await exportResource(obj);
    } else if (type == 2) {
      name = "学时导入模板.xlsx";
      res = await exportTemplate();
    } else if (type == 3) {
      obj.eduResourceIds = selectedRowKeys.value
      name = "学分列表.xlsx";
      res = await exportClassScore(obj);
    } else if (type == 4) {
      name = "学分导入模板.xlsx";
      res = await exportClassScoreTemplate();
    } else if (type == 5) {
      if (active == 1 || !active) {
        resourceParams.sourceType = null
        name =
          resourceParams.queryType == "1" ? "学时认证列表.xlsx" : "申报记录.xlsx";
      }
      else {
        resourceParams.sourceType = 1
        name =
          resourceParams.queryType == "1" ? "学分认证列表.xlsx" : "申报记录.xlsx";
      }
      res = await exportClassHour(resourceParams);
    }
    console.log(res);
    // return
    let blob = new Blob([res.data], { type: "application/vnd.ms-execl" });
    let link = window.URL.createObjectURL(blob);
    let a = document.createElement("a");
    document.body.appendChild(a);
    a.download = name;
    a.href = link;
    a.click();
    window.URL.revokeObjectURL(link);
    document.body.removeChild(a);
  }

  /* 单个审核是刷新状态 */
  function handleOneAuditNewStatus(id: string, newStatus: number) {
    resourceList.value.forEach((v: resourceItemProps) => {
      if (v.eduResourceId == id) v.status = newStatus;
    });
  }

  return {
    applyStatusOptions2,
    applyTypeOptions,
    applyStatusOptions,
    applyStatusOptions3,
    loading,
    batchDeclareVisible,
    rejectReasonVisible,
    resourceParams,
    resourceDate,
    resourceHourTotal,
    resourceTotal,
    resourceScoreTotal,
    resourceListScore,
    unAuditCount,
    resourceListHour,
    resourceList,
    selectedRowKeys,
    disabledDate,
    onCalendarChange,
    getCheckboxProps,
    computedStatusText,
    computedResourceTypeText,
    onSelectRowChange,
    onSelectRowChange1,
    onChooseChange1,
    onChooseAllChange1,
    getResouceUnAuditCount,
    getResouceAuditList,
    getHourResourceList,
    getScoreResourceList,
    handleResourceQuery,
    handleResourceReset,
    handlePaginationChange,
    handlePaginationChange2,
    handlePaginationChange3,
    handleAuditOnePass,
    handleAuditOneRejectOpen,
    handleAuditOneRejectCancel,
    handleAuditOneRejectConfirm,
    handleBatchAudit,
    handleBatchDefine,
    handleAuditEdit,
    handleStatusSift,
    handleTabSort,
    handleTabSort1,
    handleTabSort2,
    handleExport,
    newSiftSearch,
    handleDeleteProject,
    handleOneAuditNewStatus,
    newSiftSearchForHours,
    newSiftSearchForScore,
    handleDeleteProject2,
    handleDeleteProject3,
    handleResourceHourReset,
    handleResSourceReset,

  };
};

export default useResourceList;
