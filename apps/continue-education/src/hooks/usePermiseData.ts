import { ref, nextTick } from 'vue'
import { request } from '@ys/tools'
import { doUserOrgLevel, getConfig } from "@/request/index"
import { common } from "@ys/tools"
import { getSessionUser } from "@ys/tools/common/user";
import { useRouter } from 'vue-router';
const router = useRouter()
const userInfo = getSessionUser();
const { commonApi } = request
const { bureauBlankOpen, getUrlQuery } = common
const currentOneAdmin = ref<string>('')
const testAdmin = ref<Array<number>>(
    [2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
)
const navData = ref<Array<any>>([
    {
        icon: 'iconkebiao',
        label: '签到项目',
        key: '/admin/signProject',
        num: 12,
    },
    {
        icon: 'iconrenwuchaxun-copy',
        label: '申报查询',
        key: '/admin/declareSearch',
        extraSelectedKeys: ['/admin/declareSearch/editOrAdd', '/admin/declareSearch/list'],
        num: 2,
    },
    {
        icon: 'iconshijian2',
        label: '学时管理',
        key: '/admin/studyTime',
        num: 3,
    },
    {
        icon: 'iconwenjianchenggong',
        label: '学时达标管理',
        key: '/admin/standard',
        num: 16
    },
    {
        icon: 'iconpingfen',
        label: '学分管理',
        key: '/admin/studyScore',
        num: 4,
    },
    {
        icon: 'iconwenjianshenhe',
        label: '审核',
        key: '/admin/examine',
        num: 5,
        children: [

            {
                icon: '',
                label: '个人认证审核',
                key: '/admin/examine/confirmExamine',
                num: 7,
            },
            {
                icon: '',
                label: '整体认证审核',
                key: '/admin/examine/wholeExamine',
                num: 14,
            },
            {
                icon: '',
                label: '审核设置',
                key: '/admin/examine/examineSet',
                num: 6,
            },
        ]
    },
    {
        icon: 'iconshezhi3',
        label: '设置',
        key: '/admin/setting',
        num: 8,
        children: [
            {
                icon: '',
                label: '登记表设置',
                key: '/admin/setting/registrationSetting',
                num: 9,
            },
            {
                icon: '',
                label: '证书规则设置',
                key: '/admin/setting/certificateRuleSetting',
                num: 10,
            },
            {
                icon: '',
                label: '学年设置',
                key: '/admin/setting/systemYearSetting',
                num: 11,
            },
            {
                icon: '',
                label: '活动学时设置',
                key: '/admin/setting/systemActivitySetting',
                num: 15
            },
            {
                icon: '',
                label: '系统设置',
                key: '/admin/setting/systemSetting',
                num: 13
            }
        ]
    },

])
//#endregion

function useAdminPermise() {

    /* 获取权限 */
    function getPermise() {
        commonApi.permission({ menu: 'edu' }).then((res: any) => {
            if (res.data.code == 0) {
                // testAdmin.value = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
                testAdmin.value = res.data.data.menuIds || []
                console.log('res.data.data', res.data.data)
                if (testAdmin.value.length > 0) {
                    if (testAdmin.value.indexOf(8) != -1) {
                        testAdmin.value.push(13)
                        testAdmin.value.push(15)
                    }
                    isSetPermiss()
                    setTimeout(() => {
                        navData.value = newPermise(navData.value, testAdmin.value)
                        routerJump(navData.value)
                    }, 500)
                }
                else {
                    bureauBlankOpen(`/yskt/a/#/no-auth`, false, "_self");
                }

            }
        })
    }
    function isSetPermiss() {
        doUserOrgLevel().then((res: any) => {
            if (res.data.code == 0) {
                getConfig().then((res1: any) => {
                    if (res1.data.code == 0) {
                        if (res1.data.data.personalCertify == 2 || res1.data.data.teamCertify == 2) {
                            if (res.data.data == false) {
                                for (let i = 0; i < testAdmin.value.length; i++) {
                                    // if (testAdmin.value[i] == 8) {
                                    //     testAdmin.value.splice(i, 1)
                                    // }
                                    if (testAdmin.value[i] == 9) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    // if (testAdmin.value[i] == 10) {
                                    //     testAdmin.value.splice(i, 1)
                                    // }
                                    if (testAdmin.value[i] == 11) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 13) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 15) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                }
                            }
                            else {

                            }
                        }
                        else {
                            if (res.data.data == false) {
                                for (let i = 0; i < testAdmin.value.length; i++) {
                                    if (testAdmin.value[i] == 8) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 9) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 10) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 11) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 13) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                    if (testAdmin.value[i] == 15) {
                                        testAdmin.value.splice(i, 1)
                                    }
                                }
                            }
                            else {

                            }
                        }
                    }
                });
                console.log('testAdmin.value', testAdmin.value)

            }

        })
        let bureauId = getUrlQuery('bureauId')
        if (userInfo?.orgId != bureauId) {
            for (let i = 0; i < testAdmin.value.length; i++) {
                if (testAdmin.value[i] == 12) {
                    testAdmin.value.splice(i, 1)
                }
            }
        }
    }
    /* 递归判断权限 */
    function newPermise(arr: any, persmiseArr: Array<number>) {
        let newArr: any = []
        arr.forEach((v: any) => {
            if (!!v.children && !!v.children.length) {
                v.children = newPermise(v.children, persmiseArr,)
            }
            if (persmiseArr.includes(v.num)) {
                newArr.push(v)
            }
        })
        return newArr
    }

    function routerJump(persmiseArr: any) {
        for (let i = 0; i < persmiseArr.length; i++) {
            if (!!!persmiseArr[i].children) {
                currentOneAdmin.value = persmiseArr[i].key
                return
            } else routerJump(persmiseArr[i].children)
        }
    }

    return {
        navData,
        getPermise,
        currentOneAdmin,
    }
}
export default useAdminPermise

