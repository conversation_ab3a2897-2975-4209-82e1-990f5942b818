import { ref } from "vue"

const useExamineDetails = () => {
    const examineDetailsVisible = ref<boolean>(false);
    const examineDetailsId = ref<string>("");
    const examineRecord = ref<boolean>(false);
    const examineDhowAlert = ref<boolean>(false);

    const handleOpenExamineDetail = (eduResourceId: string) => {
        examineDetailsVisible.value = true
        examineDetailsId.value = eduResourceId
    }

    const handleCloseExamineDetail = () => {
        examineDetailsVisible.value = false
        examineDetailsId.value = ""
    }

    return {
        examineDetailsVisible,
        examineDetailsId,
        examineRecord,
        examineDhowAlert,
        handleOpenExamineDetail,
        handleCloseExamineDetail
    };
};

export default useExamineDetails;
