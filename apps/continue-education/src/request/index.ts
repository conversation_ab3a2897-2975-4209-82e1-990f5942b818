import { request } from "@ys/tools";
const { instance, sd_url, base_url } = request;

//获取用户信息
export const getUserInfo = <T>() => {
  return instance<T>({
    url: `${sd_url}/meet/meeting/selectUserInfoById.do`,
  });
};

// 获取学段
export const dictGetByType = <T>(params: any) => {
  return instance<T>({
    url: `${base_url}/base/api/dict/getByType`,
    params,
  });
};

// 获取学科
export const getSubjectByGrade = <T>(params: any) => {
  return instance<T>({
    url: `${base_url}/base/api/external/getSelectSubjectByPeriodId`,
    params,
  });
};

// 批量审核
export const auditRecord = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/auditRecord`,
    data,
    method: "post",
  });
};

// 学时转换
export const convertHourScore = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/convertHourScore`,
    data,
    method: "post",
  });
};

// 删除审核人员
export const delAuditPerson = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/delAuditPerson`,
    params,
  });
};

// 删除申报项目
export const delResource = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/delResource`,
    data,
    method: "post",
  });
};

// 获取用户是否具有设置权限
export const doUserSetting = <T>() => {
  return instance<T>({
    url: `${sd_url}/event/edu/doUserSetting`,
  });
};

// 编辑审核设置
export const editAuditSetting = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/editAuditSetting`,
    data,
    method: "post",
  });
};

// 认证列表导出列表
export const exportClassHour = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/exportClassHour`,
    responseType: "blob",
    data,
    method: "post",
  });
};

// 导出学时列表
export const exportResource = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/exportResource`,
    responseType: "blob",
    data,
    method: "post",
  });
};

// 导出学时列表模板
export const exportTemplate = <T>() => {
  return instance<T>({
    url: `${sd_url}/event/edu/exportTemplate`,
    responseType: "blob",
  });
};

// 获取审核人员
export const getAuditPerson = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getAuditPerson`,
    params,
  });
};

// 获取审核设置
export const getAuditSetting = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getAuditSetting`,
    params
  });
};

// 获取首页数据
export const getIndexData = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getIndexData`,
    params,
  });
};

// 获取首页数据
export const getIndexYear = <T>() => {
  return instance<T>({
    url: `${sd_url}/event/edu/getIndexYear`,
  });
};

// 获取审核记录
export const getResourceAuditRecord = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getResourceAuditRecord`,
    params,
  });
};

// 获取申报详情
export const getResourceDetail = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getResourceDetail`,
    params,
  });
};

// 获取申报列表
export const getUnAuditCount = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getUnAuditCount`,
    params
  });
};

// 获取申报列表
export const getResourceList = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/getResourceList`,
    data,
    method: "post",
  });
};

// 导入学时列表模板
export const importResource = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/importResource`,
    data,
    method: "post",
  });
};

// 导入学分列表模板
export const importClassScore = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/importClassScore`,
    data,
    method: "post",
  });
};

// 添加审核人员
export const saveAuditPerson = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/saveAuditPerson`,
    data,
    method: "post",
  });
};

// 资料申报或编辑
export const saveResource = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/saveResource`,
    data,
    method: "post",
  });
};

/* 获取导入日志 */
export const getImportLog = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getImportLog`,
    params,
  });
};

/* 获取导入日志详情 */
export const getImportLogDetails = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getImportLogDetail`,
    params,
  });
};

/* 导出学分列表 */
export const exportClassScore = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/exportClassScore`,
    responseType: "blob",
    data,
    method: "post",
  });
};

/* 下载学分导入模板 */
export const exportClassScoreTemplate = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/edu/exportClassScoreTemplate`,
    responseType: "blob",
  });
};

/* 获取添加审核人员 */
export const getAddAuditPerson = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getAddAuditPerson`,
    params,
  });
};

/* 获取用户等级机构 */
export const doUserLevel = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/edu/doUserLevel`,
  });
};

// 学时1.1

// 学时1.1-新增/修改证书规则设置
export const editCertifyRule = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/editCertifyRule`,
    data,
    method: "post",
  });
};
// 学时1.1-获取证书规则设置
export const getCertifyRule = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getCertifyRule`,
    params
  });
};
// 学时1.1-新增/修改登记表设置
export const editRegisterForm = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/editRegisterForm`,
    data,
    method: "post",
  });
};
// 学时1.1-获取登记表设置
export const getRegisterForm = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/edu/getRegisterForm`,
  });
};
// 学时1.1-新增/修改系统设置
export const editSystemSet = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/editSystemSet`,
    data,
    method: "post",
  });
};
export const delSystemSet = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/delSystemSet`,
    params,
  });
};

// 学时1.1-获取系统设置
export const getSystemSet = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/edu/getSystemSet`,
  });
};
// 学时1.1-获取登记卡数据
export const registrationForm = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/registrationForm`,
    params,
  });
};
// 学时1.1-查看证书
export const lookCertify = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/lookCertify`,
    data,
    method: "post",
  });
};
// 学时1.1-获取学时证书列表
export const getHourCertify = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getHourCertify`,
    params,
  });
};
// 学时1.1-获取学分证书列表
export const getScoreCertify = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getScoreCertify`,
    params,
  });
};
// 学时1.1-学时管理列表
export const getResourceListHour = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getHourResourceList`,
    data,
    method: "post",
  });
};
// 学时1.1-学分管理列表
export const getResourceListScore = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getScoreResourceList`,
    data,
    method: "post",
  });
};
// 学时1.2 学时规则配置
export const convertRule = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/convertRule`,
    data,
    method: "post",
  });
};
// 学时1.2  获取学时配置规则
export const getConvertRule = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getConvertRule`,
    params,
    method: "get",
  });
};
// 学时管理1.2.2 判断用户是否为顶级机构

export const doUserOrgLevel = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/edu/doUserOrgLevel`,
    // url: `http://192.168.40.36:90/edu/doUserOrgLevel`,
    method: "get",
  });
};

export const getTeacher = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/game/pageTeacher`,
    data,
    method: 'post'
  });
};
export const editPersonalCertifyRule = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/editPersonalCertifyRule`,
    data,
    method: "post",
  });
};


export const getPersonalCertifyRule = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getPersonalCertifyRule`,
    params,
    method: "get",
  });
};
export const personalCertify = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/personalCertify`,
    params,
    method: "get",
  });
};
export const exportRegistrationForm = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/exportRegistrationForm`,
    responseType: "blob",
    params,
    method: "get",
  });
};
// 学时签到结算提交
export const commitSettlement = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/commitSettlement`,
    params,
    method: "get",
  });
};
// 删除签到培训项目
export const delSignProject = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/delSignProject`,
    params,
    method: "get",
  });
};
// 编辑学时签到结算列表
export const editSettlement = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/editSettlement`,
    params,
    method: "get",
  });
};
// 创建签到培训项目
export const editSignProject = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/editSignProject`,
    data,
    method: "post",
  });
};
// 创建签到培训规则
export const editSignProjectRule = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/editSignProjectRule`,
    data,
    method: "post",
  });
};
// 获取学时签到项目详情
export const eduSignDetail = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/eduSignDetail`,
    params,
    method: "get",
  });
};
// 获取学时签到项目
export const eduSignList = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/eduSignList`,
    params,
    method: "get",
  });
};
// 学时签到结算列表
export const eduSignSettlement = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/eduSignSettlement`,
    data,
    method: "post",
  });
};
// 学时签到结算列表导出
export const exportSettlement = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/exportSettlement`,
    responseType: "blob",
    data,
    method: "post",
  });
};
// 学时签到培训列表
export const SignProjectList = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/SignProjectList`,
    params,
    method: "get",
  });
};
export const refresh_get = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/sign/sign/task/refresh/get.do`,
    params,
  });
};
// 学时管理1.2.4
export const revokeProject = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/edu/revoke`,
    params,
  });
};
// 学时管理1.2.5
// 获取用户端活动报名列表
// 学时签到培训列表
export const signActiveList = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/SignActiveList`,
    params,
    method: "get",
  });
};
// 报名
export const activitySign = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/sign`,
    data,
    method: "post",
  });
};
// 检查是否能报名
export const checkSign = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/checkSign`,
    data,
    method: "post",
  });
};
// 模糊查询机构
export const queryOrg = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/queryOrg`,
    data,
    headers: {
      "Content-Type": "application/json",
    },
    method: "post",
  });
};
export const checkLate = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSign/checkLate`,
    // url: `http://192.168.40.24:90/eduSign/checkLate`,
    params,
    method: "get",
  });
};
// 添加拒绝常用语
export const editCommonPhrases = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/editCommonPhrases`,
    data,
    method: "post",
  });
};

// 获取拒绝常用语
export const getCommonPhrases = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getCommonPhrases`,
    params,
    method: "get",
  });
};
// 删除拒绝常用语
export const delCommonPhrases = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/delCommonPhrases`,
    params,
    method: "get",
  });
};
// 跨年证书-汇总学年
export const yearsCertificate = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/yearsCertificate`,
    data,
    method: "post",
  });
};
// 获取跨年证书列表
export const yearsCertificateList = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/yearsCertificateList`,
    params,
    method: "get",
  });
};
// 查看跨年证书
export const lookYearsCertificate = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/lookYearsCertificate`,
    data,
    method: "post",
  });
};
// 删除跨年证书
export const delYearsCertificate = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/delYearsCertificate`,
    params,
    method: "get",
  });
};
// 新的系统学年度
export const getYearsCertificateSystemSet = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/edu/getYearsCertificateSystemSet`,
  });
};
// 学时管理1.2.9-编辑系统设置
export const editConfig = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduSystem/editConfig`,
    data,
    method: "post",
  });
};
// 学时管理1.2.9-获取系统设置
export const getConfig = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/eduSystem/getConfig`,
  });
};
// 获取学时管理统计
export const getHourStaticsResourceList = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/getHourStaticsResourceList`,
    data,
    method: "post",
  });
};
// 学时2.0
// 1.获取树形结构分类
export const hanlleGetTree = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/tree`,
    params
  });
};
// 2.根据分类查询达标要求
export const classifyCompliance = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/classifyCompliance`,
    params
  });
};
// 3.查询历年达标情况
export const complianceHistory = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/complianceHistory`,
    params
  });
};
// 4.查询学士达标要求列表
export const complianceList = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/complianceList`,
  });
};
// 5.查询达标管理教职工名单
export const complianceTeacher = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/complianceTeacher`,
    data,
    method: "post",
  });
};
// 6.删除达标要求
export const delCompliance = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/delCompliance`,
    params
  });
};
// 7.启用/停用节点
export const enableNode = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/enable`,
    params
  });
};
// 8.删除节点
export const removeNode = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/remove`,
    params
  });
};
// 9.添加分类
export const saveClassification = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/save`,
    data,
    method: "post",
  });
};
// 10.保存学时达标要求
export const saveCompliance = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/saveCompliance`,
    data,
    method: "post",
  });
};
// 11.获取整体认证申报人员列表
export const applyUser = function <T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eduTrain/applyUser`,
    params
  });
};
// 12. 整体认证审核详情
export const getcheckDetail = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/eduTrain/detail`,
  });
};
// 13.根据配置，获取下级机构
export const getNextNode = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/eduTrain/nextNode`,
  });
};
// 14.查询整体认证审核列表
export const searchCheckList = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduTrain/search`,
    data,
    method: "post",
  });
};
// 15.获取顶级教育局名称
export const getTopOrgName = function <T>() {
  return instance<T>({
    url: `${sd_url}/event/eduTrain/getTopOrgName`,
    method: "post",
  });
};
// 16.导出教职工名单
export const exportComplianceTeacher = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduClassify/exportComplianceTeacher`,
    method: "post",
    data,
    responseType: "blob",
  });
};
export const HourResourceList = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/edu/HourResourceList`,
    method: "post",
    data
  });
};
// 整体认证审核
export const handleAudit = function <T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/eduTrain/audit`,
    method: "post",
    data
  });
};