<template>
    <a-modal v-if="visible" v-model:visible="visible" title="导入" centered width="672px" :maskClosable="false"
        wrapClassName="basic_modal_reset_li" @cancel="emit('close')" @ok="handleOk">
        <a-alert class="mb24" message="若导入的信息已存在于系统中，则已有的数据将被覆盖" show-icon></a-alert>
        <div class="box mb16">
            <div class="mb16">1.下载导入模板
                <span class="c8c">（根据提示信息完善表格内容）</span>
            </div>
            <div>
                <a-button @click="handleExport(props.type)">
                    <DownloadOutlined />下载空的模板表格
                </a-button>
            </div>
        </div>
        <div class="box">
            <div class="flex_js mb16">
                <span>2.上传完善后的模板</span>
                <span class="c07 cursor" @click="emit('lookLog')">
                    <ClockCircleOutlined style="margin-right: 4px;" />导入日志
                </span>
            </div>
            <div>
                <a-upload-dragger v-model:fileList="fileList" :headers="headers" :data="ossData" name="file" :max-count="1"
                    :action="action" @change="handleChangeData" :before-upload="beforeUploadData">
                    <p class="ant-upload-drag-icon">
                        <InboxOutlined v-if="!!!fileList.length" />
                    <div v-else>
                        <ysIcon style="font-size:48px" type="iconexcel" />
                        <p>{{ getFileNameEllipsis(fileList[0].name) }} <span class="c8c">({{ fileSize }})</span></p>
                    </div>
                    </p>
                    <a-button>{{ !!fileList[0] ? '重新上传' : '选择文件' }}</a-button>
                    <p v-if="!!!fileList[0]" class="c8c" style="margin-top:12px;">下载模板完善信息后，可将文件拖拽到这里上传，支持格式：xls,xlsx</p>
                </a-upload-dragger>
                <!-- <Loading v-if="fileUpStatus" /> -->
            </div>
        </div>
    </a-modal>
</template>

<script lang='ts' setup>
import { ref, watch } from 'vue'
import {
    DownloadOutlined,
    ClockCircleOutlined,
    InboxOutlined
} from '@ant-design/icons-vue'
import { ysIcon } from '@ys/ui'
import { message, Upload } from "ant-design-vue";
import { commonApi } from '@ys/tools/request/commonApi'
import { common } from "@ys/tools"
import { filterSize, getFileNameEllipsis } from '@/utils'
import {
    importResource,
    importClassScore
} from '@/request'
import { useRouter } from 'vue-router';
import useResourceList from '@/hooks/useResourceList'
const { getTokenFromCookie } = common
const {
    handleExport,

} = useResourceList()
const router = useRouter()
const props = defineProps<{
    visible: boolean,
    type: number
}>()
watch(
    () => props.visible,
    (bol) => {
        if (bol) {
            fileList.value = []
            let bureauId = router.currentRoute.value.query.bureauId
            headers.value = {
                Authorization: getTokenFromCookie(),
            }
            if (!!bureauId) {
                headers.value.bureauId = bureauId
            }
            let open: any = window as any
            isOpenOss.value = !!open.yskjConfig.isopen ? open.yskjConfig.isopen : false
            ossUrl.value = !!open.yskjConfig.uploadUrls ? open.yskjConfig.uploadUrls : ''
            if (!!isOpenOss.value && !!open.yskjConfig.uploadUrls) {
                ossUrl.value = open.yskjConfig.uploadUrls
            }
        }

        visible.value = bol
    }
)
const emit = defineEmits(['close', 'lookLog'])
/* --------------- data --------------- */
//#region
const visible = ref<boolean>(false)
const isOpenOss = ref<boolean>(true)
const ossUrl = ref<string>('https://ysclass-shanghai-bucket.oss-cn-shanghai.aliyuncs.com/')
const action = ref(!!isOpenOss.value ? ossUrl.value : `${location.origin}/sd-api/uploadFile/upload/upload_file.do`);
const headers = ref();
const ossData = ref<any>();
const dataTypes = ref<Array<string>>(['jpg', "xlsx", "xls", 'mp4']);
const fileList = ref<Array<any>>([]);
const fileSize = ref()
const fileUpStatus = ref<boolean>(false)

//#endregion

/* --------------- methods --------------- */
//#region

function handleOk() {
    if (!!!fileList.value.length) return message.warning('请选择需要导入的模板')
    else {
        let form: any = new FormData()
        form.append('file', fileList.value[0])
        if (props.type == 2) {
            importResource(form).then((res: any) => {
                if (res.data.code == 0) {
                    message.success(`已成功导入${res.data.data.success}条数据,有${res.data.data.error}条数据导入失败`)
                }
            })
        } else {
            importClassScore(form).then((res: any) => {
                if (res.data.code == 0) {
                    message.success(`已成功导入${res.data.data.success}条数据,有${res.data.data.error}条数据导入失败`)
                }
            })
        }

    }
}
/* 资料上传事件 */
async function handleChangeData(file: any) {
    if (file.file.status == "done") {
        if (!!isOpenOss.value) {
            fileList.value = file.fileList.map((v: any) => {
                return { name: v.name, url: v.key_url };
            });
        } else {
            fileList.value = file.fileList.map((v: any) => {
                return { name: v.name, url: v.response.data.fileUrl };
            });
        }

        fileUpStatus.value = false
    }
}
/* 资料上传签名 */
async function beforeUploadData(file: any, e: any) {

    fileUpStatus.value = true
    fileSize.value = filterSize(file.size)
    let str = file.name.split(".")[file.name.split(".").length - 1];
    let isOk = dataTypes.value.some((v: any) => str == v);
    if (!isOk) {
        message.error(`文件格式错误`);
        return isOk || Upload.LIST_IGNORE;
    }
    fileList.value = [file]
    return Upload.LIST_IGNORE
    if (!!isOpenOss.value) {
        let res = await commonApi
            .getSign({ oldFilename: file.name })
            .then((res: any) => res);
        if (res.data.code == 0) {
            let value = res.data.data;
            ossData.value = {
                name: file.name,
                key: value.key,
                policy: value.policy,
                OSSAccessKeyId: value.accessid,
                success_action_status: 200,
                signature: value.signature,
            };
            file.key_url = value.key;
            console.log(ossData.value)
            console.log(file)
        } else return;
    }
}
//#endregion


</script>

<style lang='scss' scoped>
.box {
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

::v-deep(.ant-upload-list) {
    display: none;
}
</style>