<template>
  <a-drawer
    v-if="visible"
    v-model:visible="visible"
    width="640"
    :title="props.title"
    :maskClosable="false"
    @close="emit('close')"
  >
    <section style="padding: 20px 28px 20px 24px">
      <a-alert
        v-if="props.showAlert"
        show-icon
        :type="
          info.status == 1 ? 'success' : info.status == 0 ? 'warning' : 'error'
        "
        :message="
          info.status == 1 ? '已通过' : info.status == 0 ? '待审核' : '已拒绝'
        "
      >
        <template v-if="info.status == 2" #icon>
          <CloseCircleFilled style="font-size: 14px; padding-top: 0" />
        </template>
        <template v-if="!!info.reason" #description>
          <span style="font-size: 12px; color: #8c8c8c">
            <span>拒绝理由：</span>
            {{ info.reason }}
          </span>
        </template>
      </a-alert>
      <section class="header_line flex_js mb24">
        <div class="title">{{ info.title }}</div>
        <div class="top_btns">
          <div @click="handleChangeLook(-1)" class="btns">
            <ysIcon type="iconzuojiantou" />
          </div>
          <div
            @click="handleChangeLook(1)"
            class="btns"
            style="margin-left: 6px"
          >
            <ysIcon type="iconyoujiantou" />
          </div>
          <a-button
            v-if="props.record"
            type="primary"
            @click="recordVisible = true"
            >审核记录</a-button
          >
        </div>
      </section>
      <section class="container mb24">
        <div class="info_line flex" v-if="detailType != 2">
          <span class="small_title">姓名</span>
          <span class="line_content">{{ info.userName }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2">
          <span class="small_title">身份证号</span>
          <span class="line_content">{{ infoHidden(2, info.personCode) }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2">
          <span class="small_title">所属机构</span>
          <span class="line_content">{{ info.orgName }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2">
          <span class="small_title">选择年度</span>
          <span class="line_content">
            {{ info.gameSystemName }}
          </span>
        </div>
        <div class="info_line flex" v-if="info.sourceType">
          <span class="small_title">学时来源</span>
          <span class="line_content">
            {{ info.sourceType == 1 ? "培训" : "其他" }}
          </span>
        </div>
        <div class="info_line flex" v-if="detailType == 2 && info.orgName">
          <span class="small_title">申报机构</span>
          <span class="line_content">
            {{ info.orgName }}
          </span>
        </div>
        <div class="info_line flex" v-if="detailType == 2">
          <span class="small_title">申报时间</span>
          <span class="line_content">
            {{ info.createTime }}
          </span>
        </div>
        <div class="info_line flex" v-if="info.classifyName">
          <span class="small_title">申报分类</span>
          <span class="line_content">
            {{ info.classifyName }}
          </span>
        </div>
        <div class="info_line flex">
          <span class="small_title">培训级别</span>
          <span class="line_content">
            {{ getTrian(info.trainLevel) }}
          </span>
        </div>
        <div class="info_line flex" v-if="info.trainWay">
          <span class="small_title">培训方式</span>
          <span class="line_content">
            {{ getTrianWay(info.trainWay) }}
          </span>
        </div>
        <div class="info_line flex" v-if="detailType == 2">
          <span class="small_title">培训时间</span>
          <span class="line_content">
            {{ info.startTime }}~{{ info.endTime }}
          </span>
        </div>
        <div class="info_line flex" v-if="detailType == 2">
          <span class="small_title">培训人数</span>
          <span class="line_content">
            {{ info.joinCount }}
            <span
              @click="handlePersonDetails(info)"
              style="margin-left: 54px; color: #007aff; cursor: pointer"
              >查看名单</span
            >
          </span>
        </div>
        <div class="info_line flex" v-if="detailType == 2">
          <span class="small_title">学时年度</span>
          <span class="line_content">
            {{ info.gameSystemName }}
          </span>
        </div>
        <div class="info_line flex" v-if="detailType != 2 && info.joinRole">
          <span class="small_title">角色参与</span>
          <span class="line_content">
            {{
              info.joinRole == 1 ? "主讲人" : info.joinRole == 2 ? "学员" : ""
            }}
          </span>
        </div>
        <div class="info_line flex" v-if="detailType != 2 && info.joinRole">
          <span class="small_title">学段</span>
          <span class="line_content text_overflow">{{
            notData(info.stageName)
          }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2 && info.joinRole">
          <span class="small_title">任教学科</span>
          <span class="line_content text_overflow">{{
            notData(info.subjectName)
          }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2">
          <span class="small_title">申报项目</span>
          <span class="line_content">{{ info.resourceName }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2 && info.joinRole">
          <span class="small_title">学习时间</span>
          <span class="line_content">{{ info.studyTime }}</span>
        </div>
        <div class="info_line flex" v-if="detailType != 2 && info.joinRole">
          <span class="small_title">学习地点</span>
          <span class="line_content">
            {{ info.studyAddress }}
          </span>
        </div>
        <div class="info_line flex">
          <span class="small_title">申报类型</span>
          <span class="line_content">{{
            info.declareType == 1 ? "灵活性" : "规范性"
          }}</span>
        </div>
        <div class="info_line flex">
          <span class="small_title"
            >认证{{ props.detailsType == 1 ? "学时" : "学分" }}</span
          >
          <span class="line_content">{{ notData(info.confirmTime) }}</span>
        </div>
        <div class="info_line flex">
          <span class="small_title flex_ac">资料</span>
          <span class="line_content w100">
            <div
              v-for="(item, i) in info.attachments"
              :key="i"
              class="flex_ac cursor w100"
              @click="handlePreview(item)"
            >
              <ysIcon
                type="iconfujian1"
                style="margin-right: 8px; color: #8c8c8c"
              />
              <span class="c07 text_overflow">{{ item.attachmentName }}</span>
            </div>
            <div v-if="!!!info.attachments.length">暂无</div>
          </span>
        </div>
        <div class="info_line flex" v-if="detailType == 2">
          <span class="small_title">备注</span>
          <span class="line_content"> {{ info.remark }}</span>
        </div>
      </section>
      <section
        v-if="props.footerBtnS && info.status == 0"
        class="footer_btns flex_ac"
      >
        <a-button
          type="primary"
          v-if="detailType == 2"
          class="mr8"
          @click="handleAuditOnePass1"
          >通过</a-button
        >
        <onePass
          v-else
          class="pass_btn flex_center mr8"
          @ok="handleAuditOnePass(1)"
        />
        <a-button @click="rejectVisible = true">拒绝</a-button>
      </section>
    </section>
    <RejectReason
      :visible="rejectVisible"
      :needReason="true"
      @close="rejectVisible = false"
      @define="handleDefine"
    />
    <Record :id="id" :visible="recordVisible" @close="recordVisible = false" />
    <Loading :loading="loading % 2 != 0" />
    <a-drawer
      v-if="showPersonNum"
      v-model:visible="showPersonNum"
      width="640"
      title="人员名单"
      :maskClosable="false"
      @close="handleClosePersonNum"
    >
      <div class="header">
        <span class="title">人员名单</span>
        <a-input
          @keyup.enter="handleSearchNum"
          placeholder="请输入姓名"
          v-model:value="searchName"
          style="width: 222px"
        >
          <template #suffix>
            <SearchOutlined
              @click="handleSearchNum"
              style="color: rgba(0, 0, 0, 0.45)"
            />
          </template>
        </a-input>
      </div>
      <div class="tableContent">
        <a-table :dataSource="list" :columns="columns">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'index'">{{
              record.index
            }}</template>
          </template>
        </a-table>
        <ysPagination
          :pageNo="pageNo"
          :pageSize="pageSize"
          :total="total"
          @change="handlePaginationChange"
        />
      </div>
    </a-drawer>
    <a-modal
      centered
      :width="448"
      wrapClassName="batch_declare_modal_li"
      v-model:visible="showParentCheck"
      title="请选择下一级审核部门"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="parentCheckBox">
        <div class="parentCheckBox-title">
          <span>下一级审核机构：</span>
          <span>{{ parentOrgName }}</span>
        </div>
        <div class="parentCheckBox-title1">
          <span style="color: red">*</span>
          <span>下一级审核部门：</span>
        </div>
        <div class="chooseParentOption">
          <a-select
            style="width: 368px"
            v-model:value="parentValue"
            :options="nextOptions"
          ></a-select>
        </div>
      </div>
    </a-modal>
    <checkModel
      detailType="2"
      v-if="showChecked"
      @close="closeCheckModel"
      @sure="sureChecked"
    />
  </a-drawer>
</template>

<script lang="ts" setup>
import checkModel from "@/components/checkModel.vue";
import { ysPagination } from "@ys/ui";
import { ref, reactive, watch, onMounted } from "vue";
import RejectReason from "@/components/examineDetails/rejectReason.vue";
import Record from "@/components/examineDetails/record.vue";
import { CloseCircleFilled, SearchOutlined } from "@ant-design/icons-vue";
import { ysIcon, ysShowPreview, ysShowPreviewH5 } from "@ys/ui";
import dayjs from "dayjs";
import Loading from "@/components/loading.vue";
import {
  getResourceDetail,
  auditRecord,
  applyUser,
  handleAudit,
  getNextNode,
} from "@/request";
import { common } from "@ys/tools";
import onePass from "@/components/onePass.vue";
import { message } from "ant-design-vue";
import { infoHidden } from "@/utils";
import { useRoute } from "vue-router";
const route = useRoute();
const { isCdn } = common;
const props = withDefaults(
  defineProps<{
    id: string;
    visible: boolean;
    resourceList: Array<any>;
    detailsType?: number;
    record?: boolean;
    showAlert?: boolean;
    title?: string;
    footerBtnS?: boolean;
    detailType?: string | number;
  }>(),
  {
    detailsType: 1,
    visible: false,
    record: false,
    showAlert: false,
    title: "详情",
    footerBtnS: false,
    detailType: "1",
  }
);
const emit = defineEmits(["close", "newStatus"]);
watch(
  () => props.visible,
  (bol: boolean) => {
    visible.value = bol;
    if (bol) {
      id.value = props.id;
      init();
    }
  }
);
interface infoType {
  gameSystemName: string;
  eduResourceId: string;
  status: number;
  reason: string | null | undefined;
  title: string;
  userName: string;
  personCode: string;
  orgId: string;
  orgName: string;
  year: {
    startTime: string;
    endTime: string;
  };
  trainLevel: string | null | undefined;
  joinRole: string | null | undefined | any;
  stageId: string | null | undefined;
  stageName: string | null | undefined;
  subjectId: string | null | undefined;
  subjectName: string | null | undefined;
  phone: string | null | undefined;
  studyTime: string;
  resourceName: string;
  declareType: number;
  confirmTime: string | number | null;
  attachments: Array<any>;
  studyAddress: string;
  trainWay: string | number | null;
  createTime: string | null | undefined;
  classifyName: string | null | undefined;
  startTime: string | null | undefined;
  endTime: string | null | undefined;
  joinCount: string | number | null;
  sourceType: string | number | null;
  remark: string | null | undefined;
}
/* --------------- data --------------- */
//#region

const visible = ref<boolean>(false);
const id = ref<string>("");
const info = reactive<infoType>({
  eduResourceId: "",
  status: 0,
  reason: "",
  title: "",
  userName: "",
  personCode: "",
  orgId: "",
  orgName: "",
  year: {
    startTime: "",
    endTime: "",
  },
  trainLevel: "",
  joinRole: "",
  stageId: "",
  stageName: "",
  subjectId: "",
  subjectName: "",
  phone: "",
  studyTime: "",
  resourceName: "",
  declareType: 1,
  confirmTime: "",
  studyAddress: "",
  gameSystemName: "",
  attachments: [],
  trainWay: "",
  createTime: "",
  classifyName: "",
  startTime: "",
  endTime: "",
  joinCount: "",
  sourceType: "",
  remark: "",
});
const list = ref<any>([]);
const columns = ref<any>([
  {
    title: "序号",
    dataIndex: "index",
  },
  {
    title: "姓名",
    dataIndex: "userName",
    ellipsis: true,
  },
  {
    title: "所属机构",
    dataIndex: "orgName",
    ellipsis: true,
  },
]);
const loading = ref<number>(2);
/* 审核记录drawer */
const recordVisible = ref<boolean>(false);

/* 拒绝modal */
const rejectVisible = ref<boolean>(false);
const showPersonNum = ref(false);
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchName = ref(null);
const nextOptions = ref<any>([]);
const nextOrgId = ref(null);
const showParentCheck = ref(false);
const parentValue = ref(null);
const parentOrgName = ref<any>(null);
const showChecked = ref(false);
//#endregion

/* --------------- methods --------------- */
//#region

const handleCancel = () => {
  parentValue.value = null;
  showParentCheck.value = false;
};
const handleOk = () => {
  if (!parentValue.value) {
    message.error("请选择");
    return;
  } else {
    showParentCheck.value = false;
    showChecked.value = true;
  }
};
const getNumList = () => {
  let params = {
    id: id.value,
    name: searchName.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
  };
  applyUser(params).then((res: any) => {
    if (res.data.code == 0) {
      console.log("res.data.data", res.data.data);
      list.value = res.data.data.map((item: any, index: any) => {
        return { ...item, index: index + 1 };
      });
      total.value = Number(res.data.totalDatas);
    }
  });
};
const handlePaginationChange = (page: number, pageS: number) => {
  // 清空下勾选项
  pageNo.value = page;
  pageSize.value = pageS;
  getNumList();
};
const handleSearchNum = () => {
  getNumList();
};
/* 获取数据 */
function getTrian(val: any) {
  let str = "";
  str =
    val == 1
      ? "国家级"
      : val == 2
        ? "省级"
        : val == 3
          ? "市级"
          : val == 4
            ? "县级"
            : val == 5
              ? "校级"
              : val == 6
                ? "其他"
                : "";
  return str;
}
function getTrianWay(val: any) {
  let str = "";
  str =
    val == 1
      ? "长期脱产研修（一个月以上）"
      : val == 2
        ? "短期面授培训"
        : val == 3
          ? "网络研修"
          : val == 4
            ? "面授培训和网络研修结合"
            : val == 5
              ? "其他"
              : "";
  return str;
}
const handleClosePersonNum = () => {
  showPersonNum.value = false;
  searchName.value = null;
};
const handlePersonDetails = (info: any) => {
  console.log("info", info);
  showPersonNum.value = true;
  getNumList();
};
function init() {
  loading.value++;
  getResourceDetail({ id: id.value, type: props.detailType }).then(
    (res: any) => {
      loading.value++;
      if (res.data.code == 0) {
        const data = res.data.data;
        info.eduResourceId = data.eduResourceId;
        info.status = data.status;
        info.reason = data.reason;
        info.title = data.resourceName;
        info.userName = data.userName;
        info.personCode = data.idCard;
        info.orgId = data.orgId;
        info.orgName = data.orgName;
        info.trainLevel = data.trainLevel;
        info.joinRole = data.joinRole;
        info.studyAddress = data.studyAddress;
        info.year = {
          startTime: dayjs(data.startTime).format("YYYY"),
          endTime: dayjs(data.endTime).format("YYYY"),
        };
        info.stageId = data.stageId;
        info.stageName = data.stageName;
        info.subjectId = data.subjectId;
        info.subjectName = data.subjectName;
        info.resourceName = data.resourceName;
        let start = data.studyTime ? data.studyTime.split(" ")[0] : null;
        let end = data.studyEndTime ? data.studyEndTime.split(" ")[0] : null;
        // info.studyTime = dayjs(data.studyTime).format("YYYY-MM-DD HH:mm");
        info.studyTime = `${start}~${end}`;
        info.declareType = data.resourceType;
        info.confirmTime =
          props.detailsType == 1 ? data.classHour : data.classScore;
        info.attachments = data.attachments;
        info.gameSystemName = data.gameSystemName;
        info.trainWay = data.trainWay;
        info.createTime = data.createTime;
        info.classifyName = data.classifyName;
        info.startTime = data.startTime;
        info.endTime = data.endTime;
        info.joinCount = data.joinCount;
        info.sourceType = data.sourceType;
        info.remark = data.remark;
      }
    }
  );
}

/* 切换上下个 */
function handleChangeLook(num: number) {
  let index = props.resourceList.findIndex(
    (v: any) => v.eduResourceId == info.eduResourceId
  );
  if (index == 0 && num == -1)
    return message.warning("已是当前页第一个,请先进行翻页操作");
  else if (index == props.resourceList.length - 1 && num == 1)
    return message.warning("已是当前页最后一个,请先进行翻页操作");
  else {
    id.value = props.resourceList[index + num].eduResourceId;
    init();
  }
}

/* 附件预览 */
function handlePreview(attr: any) {
  console.log(attr);
  let type = 1;
  if (route.query.jd == "1") {
    type = 3;
  } else {
    if (Number(sessionStorage.getItem("unitAttr")) >= 8) {
      type = 1;
    } else {
      type = 2;
    }
  }
  let options = {
    groupType: type,
    uniqueId: "eduCenter",
  };
  ysShowPreview(isCdn(attr.attachmentPath), attr.attachmentName, options);
}
const sureChecked = async () => {
  let params = {
    auditType: 1,
    departmentId: parentValue.value,
    id: info.eduResourceId,
    orgId: nextOrgId.value ? nextOrgId.value : null,
  };
  try {
    const response = await handleAudit(params);
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      message.success("审核成功");
      showParentCheck.value = false;
      showChecked.value = false;
      info.status = 1;
    }
  } catch (error) {
    message.error("审核失败");
  }
};
/* 确定拒绝 */
const handleDefine = async (text: string) => {
  rejectVisible.value = false;
  if (props.detailType == 2) {
    let params = {
      auditType: 2,
      id: info.eduResourceId,
      remark: text,
    };
    try {
      const response = await handleAudit(params);
      const res = response.data as any;
      if (res.code == 0 && res.success) {
        message.success("审核成功");
        info.status = 2;
      }
    } catch (error) {
      message.error("审核失败");
    }
  } else {
    handleAuditOnePass(2, text);
  }
};
const handleAuditOnePass1 = async () => {
  const responce = await getNextNode();
  const res: any = responce.data;
  if (res.data.hasNext) {
    nextOptions.value = res.data.departments.map((item: any) => {
      return { ...item, label: item.name, value: item.id };
    });
    parentOrgName.value = res.data.orgName;
    nextOrgId.value = res.data.orgId;
    showParentCheck.value = true;
  } else {
    showChecked.value = true;
  }
};
const closeCheckModel = () => {
  showChecked.value = false;
  // showParentCheck.value = true;
};
/* 审核 */
async function handleAuditOnePass(type: number, reason?: string) {
  console.log("detailType", props.detailType);
  let params: any = {
    ids: [info.eduResourceId],
    type: type,
  };
  if (!!reason) params.reason = reason;
  try {
    loading.value++;
    const response = await auditRecord(params);
    loading.value++;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      info.status = type;
      info.reason = reason;
      emit("newStatus", { id: info.eduResourceId, status: type });
      message.success("审核成功");
    }
  } catch (error) {
    message.error("审核失败");
  }
}

/* 无数据 */
function notData(info: any) {
  return !!info ? info : "暂无";
}
//#endregion
</script>

<style lang="scss" scoped>
.parentCheckBox {
  padding: 0 24px;
  .parentCheckBox-title {
    margin-bottom: 24px;
  }
  .chooseParentOption {
    margin-top: 8px;
  }
}
.header {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.tableContent {
  padding: 0 24px;
}
.ant-alert {
  margin-bottom: 24px;
  padding: 8px 20px;

  .ant-alert-icon {
    margin-right: 8px !important;
    padding-top: 6px !important;
  }
}

.header_line {
  .title {
    font-weight: bold;
    font-family: "黑体";

    min-width: 65%;
    max-width: 85%;
  }

  .top_btns {
    margin-left: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    .btns {
      width: 36px;
      height: 36px;
      border: 1px solid #d9d9d9;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .ant-btn {
      margin-left: 8px;
    }
  }
}

.container {
  border: 1px solid rgba(0, 0, 0, 0.06);

  .info_line {
    border-top: 1px solid rgba(0, 0, 0, 0.06);

    &:nth-child(1) {
      border-top: none;
    }

    .small_title {
      padding: 18px 20px;
      width: 104px;
      border-right: 1px solid rgba(0, 0, 0, 0.06);
      background: #fafafa;
    }

    .line_content {
      padding: 18px 20px;
      width: calc(100% - 104px);
    }
  }
}

.pass_btn {
  width: 64px;
  height: 36px;
  border-radius: 4px;
  color: #fff;
  background-color: #007aff;
  letter-spacing: 4px;
}
</style>
<style lang="scss">
.batch_declare_modal_li {
  .ant-modal-body {
    .num_line {
      padding: 9px 12px;
      background-color: #f5f5f5;
    }

    .small_title {
      width: 73px;
      text-align: right;
    }
  }

  .ant-modal-footer {
    padding: 0 30px 24px 0;
    border: none;
  }
}
</style>
