<template>
    <!-- <a-modal v-model:visible="visible" :title="`${props.type == '1' ? '学时' : '学分'}转化`" :maskClosable="false" width="450px"
        wrapClassName="time_change_score_li basic_modal_reset_li" centered @cancel="emit('close')" @ok="handleOk">
        <div class="mb24" style="padding:9px 12px;border-radius:2px;background-color: #F5F5F5;">
            已选取 <span class="c07">{{ props.num }}条</span> 认证信息
        </div>
        <div class="flex_js">
            <span class="num_box">
                <a-input-number style="width:100%" v-model:value="value" placeholder="请输入整数" :min="1" :max="10" :precision="0" />
                <span class="suffix">{{ props.type == '1' ? '学时' : '学分' }}</span>
            </span>
            <span>=</span>
            <span class="num_box">
                <a-input-number style="width:100%" v-model:value="value1" placeholder="请输入整数" :min="1" :max="10" :precision="0" />
                <span class="suffix">{{ props.type == '1' ? '学分' : '学时' }}</span>
            </span>
        </div>
    </a-modal> -->
    <div class="studyDrawer" ref="studyDrawer">
        <a-drawer :width="442" :getContainer="() => $refs.studyDrawer" v-model:visible="visible" @close="closeDrawer"
            :maskClosable="false" class="study-custom-class" title="学时转化" placement="right">
            <div class="drawerContainer">
                <div class="header">
                    <div class="head_left">
                        <span style="font-weight: bold;font-size: 16px;">学时转化</span>
                        <span style="margin-left: 7px;color:#8C8C8C">
                            <a-tooltip placement="bottom"  overlayClassName="timeChangeToolTip">
                                <template #title>
                                    确认后，后期的数据将按照这个配置进行自动转化,以前的数据将不改变，需要手动选择转化
                                </template>
                                <question-circle-outlined />
                            </a-tooltip>
                        </span>
                    </div>
                    <div class="right">
                        <close-outlined class="cursor" style="color:#8C8C8C" @click="closeDrawer"/>
                    </div>
                </div>
                <div class="content">
                    <div class="chooseButton">
                        <div class="buttonItem" @click='chooseType(item)' :class="[buttonActive == item ? 'blueColor' : '']"
                            v-for="(item, index) in 2" :key="index">
                            {{ item == 1 ? '规范性' : '灵活性' }}
                        </div>
                    </div>
                    <div class="chooseMode">
                        <span>选择模式：</span>
                        <a-radio-group v-model:value="modeValue" name="radioGroup">
                            <a-radio value="1">全部</a-radio>
                            <a-radio value="2">按级别配置</a-radio>
                        </a-radio-group>
                    </div>
                    <div class="info scrollbar">
                        <div class="modeContent" v-if="modeValue == '1'">
                            <div class="item">
                                <span>培训级别：</span>
                                <a-input style="width:280px" disabled v-model:value="contentValue" />
                            </div>
                            <div class="item1 item">
                                <span>配置规则：</span>
                                <div class="flex_js">
                                    <span class="num_box">
                                        <a-input-number style="width:132px" v-model:value="allStudy" placeholder="请输入"
                                            :min="0.1" :max="10" :precision="1" />
                                        <span class="suffix">学时</span>
                                    </span>
                                    <span style="margin: 0 4px;">=</span>
                                    <span class="num_box">
                                        <a-input-number style="width:132px" v-model:value="allGrade" placeholder="请输入"
                                            :min="0.1" :max="10" :precision="1" />
                                        <span class="suffix">学分</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="modeContent" v-else v-for="(item, index) in levelList" :key="index">
                            <div class="item">
                                <span>培训级别：</span>
                                <a-input style="width:280px" disabled v-model:value="item.name" />
                            </div>
                            <div class="item item1">
                                <span>配置规则：</span>
                                <div class="flex_js">
                                    <span class="num_box">
                                        <a-input-number style="width:132px" :precision="1"  v-model:value="item.studyNum" placeholder="请输入"
                                            :min="0.1" :max="10"  />
                                        <span class="suffix">学时</span>
                                    </span>
                                    <span style="margin: 0 4px;">=</span>
                                    <span class="num_box">
                                        <a-input-number style="width:132px" :precision="1"  v-model:value="item.gradeNum" placeholder="请输入"
                                            :min="0.1" :max="10"  />
                                        <span class="suffix">学分</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="footer">
                    <a-button style="margin-right:8px" @click="closeDrawer">取消</a-button>
                    <a-button type="primary" @click="handleOk">确定</a-button>
                </div>
            </div>
        </a-drawer>
    </div>
</template>

<script lang='ts' setup>
import { QuestionCircleOutlined,CloseOutlined } from '@ant-design/icons-vue';
import { convertRule, getConvertRule } from "@/request";
import { ref, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
const props = withDefaults(defineProps<{
    visible: boolean,
    num?: string | number
    type?: string
}>(), {
    type: '1',
    num: '1'
})
watch(
    () => props.visible,
    (bol: boolean) => {
        value.value = null
        value1.value = null
        visible.value = bol
        getInfo()
    }
)

const emit = defineEmits(['close', 'define'])
/* --------------- data --------------- */
//#region

const visible = ref<boolean>(false)
const value = ref<number | null>()
const value1 = ref<number | null>()
const buttonActive = ref(1)
const modeValue = ref('1')
const contentValue = ref('全部')
const allStudy = ref(null)
const allGrade = ref(null)
const levelList = ref([
    { id: 1, name: '国家级', studyNum: null, gradeNum: null },
    { id: 2, name: '省级', studyNum: null, gradeNum: null },
    { id: 3, name: '市级', studyNum: null, gradeNum: null },
    { id: 4, name: '县级', studyNum: null, gradeNum: null },
    { id: 5, name: '校级', studyNum: null, gradeNum: null },
])
//#endregion

/* --------------- methods --------------- */
//#region
function chooseType(val: any) {
    buttonActive.value = val
    modeValue.value = '1'
    allStudy.value = null
    allGrade.value = null
    levelList.value.forEach((item) => {
        item.studyNum = null;
        item.gradeNum = null
    })
    getInfo()
    // levelList.value.forEach((item) => {
    //     item.studyNum = null;
    //     item.gradeNum = null
    // })

}
function closeDrawer() {
    allStudy.value = null
    allGrade.value = null
    levelList.value.forEach((item) => {
        item.studyNum = null;
        item.gradeNum = null
    })
    emit('close')
}
function handleOk() {
    if (modeValue.value == '1') {
        if (allStudy.value == null || allGrade.value == null) {
            message.error('请填写完整')
            return
        }
    }
    else {
        let isAll = levelList.value.every(item => item.studyNum != null && item.gradeNum != null)
        if (isAll == false) {
            message.error('请填写完整')
            return
        }
    }
    let arr: any = []
    if (modeValue.value == '1') {
        arr = [
            {
                resourceType: buttonActive.value == 1 ? 2 : 1,
                trainLevel: 0,
                classHour: allStudy.value,
                classScore: allGrade.value
            }
        ]
    }
    else {
        levelList.value.forEach((item: any) => {
            arr.push({ resourceType: buttonActive.value == 1 ? 2 : 1, trainLevel: item.id, classHour: item.studyNum, classScore: item.gradeNum })
        })

    }
    // let params = {
    //     eduConvertRules: arr
    // }
    convertRule(arr).then((res: any) => {
        console.log(res)
        if (res.data.code == 0 && res.data.success) {
            message.success('转化成功')
            allStudy.value = null
            allGrade.value = null
            levelList.value.forEach((item) => {
                item.studyNum = null;
                item.gradeNum = null
            })
            emit('close')
            buttonActive.value = 1
        }
    })
    // if (!!!value.value || !!!value1.value) return message.warning('请正确填写转化参数')
    // else emit('define', [value.value, value1.value])
}
function getInfo() {
    let params = {
        type: buttonActive.value == 1 ? 2 : 1
    }
    getConvertRule(params).then((res: any) => {
        if (res.data.code == 0 && res.data.success) {
            console.log(res.data)
            if (res.data.data.length < 2) {
                modeValue.value = '1'
                allStudy.value = res.data.data.length > 0 ? res.data.data[0].classHour : null
                allGrade.value = res.data.data.length > 0 ? res.data.data[0].classScore : null
            }
            else {
                modeValue.value = '2'
                for (let i of res.data.data) {
                    for (let j of levelList.value) {
                        if (i.trainLevel == j.id) {
                            j.studyNum = i.classHour
                            j.gradeNum = i.classScore
                        }
                    }
                }
            }
        }
    })

}
//#endregion


</script>
<style lang="scss" scoped>
.drawerContainer {
    .header {
        height: 60px;
        border-bottom: 1px solid #e5e5e5;
        padding: 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    // height: 100%;

    .content {
        height: calc(100% - 60px);
        border-bottom: 1px solid #F0F0F0;
        padding: 24px;


        .chooseButton {
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            .buttonItem {
                cursor: pointer;
                width: 74px;
                height: 36px;
                background: #FFFFFF;
                box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
                border: 1px solid #D9D9D9;
                display: flex;
                justify-content: center;
                align-items: center;

                &:nth-child(1) {
                    border-radius: 4px 0px 0px 4px;
                }

                &:last-child {
                    border-radius: 0px 4px 4px 0px;
                }
            }

            .blueColor {
                border: 1px solid #007aff;
                color: #007aff
            }
        }

        .chooseMode {
            margin-bottom: 20px;
        }

        .info {
            overflow: auto;
            height: calc(100% - 98px);
        }

        .modeContent {
            background: #FAFAFA;
            border-radius: 4px 4px 4px 4px;
            min-height: 124px;
            padding: 16px;
            margin-bottom: 20px;

            .item {
                display: flex;
                align-items: center;
                // margin-bottom: 20px;

                .num_box {
                    position: relative;
                    width: 132px;

                    .suffix {
                        position: absolute;
                        right: 22px;
                        top: 8px;
                    }
                }
            }

            .item1 {
                margin-top: 20px;
            }
        }
    }

    .footer {
        height: 60px;
        display: flex;
        align-items: center;
        line-height: 60px;
        justify-content: flex-end;
        padding: 0 16px;
    }
}
</style>
<style lang='scss'>
.time_change_score_li {

    .ant-input-number-handler-wrap {
        display: none;
    }

    .num_box {
        position: relative;
        width: 168px;

        .suffix {
            position: absolute;
            right: 12px;
            top: 8px;
        }
    }
}

.studyDrawer {
    .ant-drawer-content {
        .ant-drawer-header {
            display: none;
        }
    }
}

.timeChangeToolTip {
    .ant-tooltip-inner {
        width: 338px;
    }
}
</style>