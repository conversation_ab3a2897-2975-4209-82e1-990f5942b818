<template>
  <div class="printDiv">
    <div class="registrationName">{{ registrationName }}</div>
    <div class="info">
      <div class="infoTop">
        <div class="item" v-for="(item, index) in itemList" :key="index">
          <span class="name">{{ item.name }}</span>
          <div :class="[index != 1 ? 'boderBottom' : '']">{{ item.value }}</div>
        </div>
      </div>
      <div class="infoBottom">
        <div class="name">姓名</div>
        <div class="name1">{{ info.userName }}</div>
        <div class="name">性别</div>
        <div class="name1">{{ info.sex }}</div>
        <div class="name">年龄</div>
        <div class="name1">{{ info.age }}</div>
        <div class="name">学科</div>
        <div class="name2 text-overflow">{{ info.subjects }}</div>
        <div class="name">职称</div>
        <div class="name2 text-overflow">{{ info.technicalTitleText }}</div>
      </div>
    </div>
    <div class="table">
      <a-table
        :columns="columns"
        :pagination="false"
        :dataSource="list"
        bordered
      >
        <template #headerCell="{ title, column }">
          <template v-if="column.key === 1">
            <div class="headerTrainingCategory">
              {{ title }}
            </div>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex == 'trainingCategory'">
            <div class="trainingCategory">
              {{ record.trainingCategory }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'resourceName'">
            {{ record.resourceName }}
          </template>
          <template v-if="column.dataIndex == 'studyAddress'">
            {{ record.studyAddress }}
          </template>
          <template v-if="column.dataIndex == 'trainLevel'">
            {{ getTrian(record.trainLevel) }}
          </template>
          <template v-if="column.dataIndex == 'joinRole'">
            {{
              record.joinRole == 1 ? "主讲" : record.joinRole == 2 ? "学员" : ""
            }}
          </template>
          <!-- <template v-if="column.dataIndex == 'studyTime'">
            {{ record.studyTime }}
          </template> -->
          <template v-if="column.dataIndex == 'studyTime'">
            <span v-if="record.studyTime && record.studyEndTime"
              >{{ record.studyTime }}~{{ record.studyEndTime }}</span
            >
          </template>
          <template v-if="column.dataIndex == 'classHour'">
            {{ record.classHour }}
          </template>
          <template v-if="column.dataIndex == 'classScore'">
            {{ record.classScore }}
          </template>
          <template v-if="column.dataIndex == 'totalNum'">
            <div class="totalNum">
              {{ record.totalNum }}
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <div class="sign" v-if="signSet == 1">
      <!-- <div class="signtitle">签字盖章</div> -->
      <div class="signAll">
        <div
          class="item"
          :class="getClass(index)"
          v-for="(item, index) in addList"
          :key="index"
        >
          <div>{{ item.name }}</div>
          <div class="gaizhang" v-if="index > 0">（盖章）</div>
          <div class="time">
            {{ endTime }}年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月
            <span style="display: inline-block; width: 20px"></span>
            日
          </div>
          <div class="imgChapter" v-if="item.imgUrl">
            <img :src="isCdn(item.imgUrl)" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { registrationForm } from "@/request";
import { getRegisterForm } from "@/request";
import { common } from "@ys/tools";
const { isCdn } = common;
const router = useRouter();
const props = defineProps<{
  searchType: any;
}>();
/* --------------- data --------------- */
//#region
const list = ref<any>([]);
const registrationName = ref("乐山市教师继续教育登记表");
const signSet = ref(0);
const itemList = ref<any>([
  // { id: 1, name: "编号", value: "" },
  { id: 1, name: "学年度", value: "" },
  { id: 1, name: "", value: "" },
  { id: 1, name: "学校", value: "" },
  { id: 1, name: "身份证", value: "" },
]);
const nowYear = new Date().getFullYear();
const nowMonth = new Date().getMonth();
const endTime = ref<any>(null);
//#endregion

/* --------------- methods --------------- */
//#region
const type = ref<any>("");
onMounted(() => {
  type.value = router.currentRoute.value.query.type;
  if (type.value == 2) {
    for (let i of columns.value) {
      if (i.key == 7) {
        i.title = "折算学分";
        i.dataIndex = "classScore";
      }
    }
  }
  getinfo();
  getregSetting();
});
// function changeType(val: any) {
//   for (let i of columns.value) {
//     if (i.dataIndex == "classHour") {
//       i.title = val == 2 ? "折算学分" : "折算学时";
//     }
//   }
// }
const getClass = (val: any) => {
  let className = "";
  if ((val + 1) % 3 == 0) {
    className = "addRight";
    if (val + 1 > 3) {
      className = "addRight delTop";
    }
  } else {
    if (val + 1 > 3) {
      className = "delTop";
    }
  }
  return className;
};
function getKindsNum(val: any) {
  let num = 0;
  for (let i of val) {
    for (let j of val) {
      if (i.trainingCategory == j.trainingCategory) {
        num += Number(type.value == 1 ? j.classHour : j.classScore);
        i.totalNum = num;
      } else {
        num = 0;
      }
    }
  }
}
function getAllNum(val: any) {
  let num = 0;
  for (let i of val) {
    num += Number(type.value == 1 ? i.classHour : i.classScore);
    // console.log(num)
    if (i.trainingCategory == "合计") {
      i.totalNum = num;
    }
  }
}
function getTrian(val: any) {
  let str = "";
  str =
    val == 1
      ? "国家级"
      : val == 2
        ? "省级"
        : val == 3
          ? "市级"
          : val == 4
            ? "县级"
            : val == 5
              ? "校级"
              : val == 6
                ? "其他"
                : "";
  return str;
}
// 基础信息
const info = ref<any>({
  schoolName: "",
  userName: "",
  sex: "",
  cardNumber: "",
  age: "",
  subjects: "",
  technicalTitleText: "",
});
async function getinfo() {
  list.value = [];
  const time = router.currentRoute.value.query.time as any;
  const start = time.split("~")[0];
  const end = time.split("~")[1];
  const start1 = start.slice(0, 4);
  const end1 = end.slice(0, 4);
  const start2 = start.slice(0, 7);
  const end2 = end.slice(0, 7);

  let params = {
    startTime: start2,
    endTime: end2,
    dataType: props.searchType ? props.searchType : null,
    sourceType: type.value == 1 ? null : 1,
  };
  const res = (await registrationForm(params)) as any;
  if (res.data.data) {
    endTime.value = res.data.data.year;
    info.value.schoolName = res.data.data.displayName;
    info.value.userName = res.data.data.userName;
    info.value.sex = res.data.data.sex == 0 ? "男" : "女";
    info.value.cardNumber = res.data.data.idCard;
    info.value.age = res.data.data.age > 0 ? res.data.data.age : "";
    info.value.subjects = res.data.data.subjects;
    info.value.technicalTitleText = res.data.data.technicalTitleText;
    let lh: any = [];
    let gf: any = [];
    if (res.data.data.lhList) {
      lh = res.data.data.lhList.map((item: any) => {
        return { trainingCategory: "灵活性培训", totalNum: "", ...item };
      });
    }
    if (res.data.data.gfList) {
      gf = res.data.data.gfList.map((item: any) => {
        return { trainingCategory: "规范性培训", totalNum: "", ...item };
      });
    }
    list.value = [...gf, ...lh];
    let a: any = "";
    for (let i of list.value) {
      // a = i.studyTime.split(" ")[1].split(":").splice(0, 2);
      // i.studyTime = i.studyTime.split(" ")[0] + " " + a.join(":");
      i.studyEndTime = i.studyEndTime.split(" ")[0];
    }
    let obj = {
      trainingCategory: "合计",
      resourceName: "",
      studyAddress: "",
      studyTime: "",
      classHour: "",
      classScore: "",
      totalNum: "",
    };
    list.value.push(obj);
    // itemList.value[0].value = "xxxx";
    itemList.value[0].value = router.currentRoute.value.query.yearName;
    itemList.value[2].value = info.value.schoolName;
    itemList.value[3].value = info.value.cardNumber;
    getKindsNum(list.value);
    getAllNum(list.value);
  }
}

const columns = ref<any>([
  {
    key: 1,
    title: "培训类别",
    dataIndex: "trainingCategory",
    width: "60px",
    customCell: (record: any, rowIndex: any, column: any) => {
      return mergeCell(record, rowIndex, column);
    },
    align: "center",
  },
  {
    key: 2,
    title: "培训项目",
    dataIndex: "resourceName",
    width: "250px",
    align: "center",
  },
  {
    key: 3,
    title: "学习地点",
    dataIndex: "studyAddress",
    width: "150px",
    align: "center",
  },
  {
    key: 4,
    title: "培训级别",
    dataIndex: "trainLevel",
    width: "70px",
    align: "center",
  },
  {
    key: 5,
    title: "角色参与",
    dataIndex: "joinRole",
    width: "60px",
    align: "center",
  },
  {
    key: 6,
    title: "学习时间",
    dataIndex: "studyTime",
    width: "220px",
    align: "center",
  },
  {
    key: 7,
    title: "折算学时",
    dataIndex: "classHour",
    width: "60px",
    align: "center",
  },
  {
    key: 8,
    title: "合计",
    dataIndex: "totalNum",
    width: "60px",
    customCell: (record: any, rowIndex: any, column: any) => {
      return mergeCell(record, rowIndex, column);
    },
    align: "center",
  },
]);

function mergeCell(record: any, rowIndex: any, column: any) {
  const temp_Index = list.value.findIndex(
    (item: any) => item.trainingCategory === record.trainingCategory
  );
  let rowSpan = list.value.filter(
    (item2: any) => item2.trainingCategory === record.trainingCategory
  ).length;
  if (rowSpan > 1) {
    if (temp_Index === rowIndex) {
      return {
        rowSpan: rowSpan,
      };
    } else {
      return {
        rowSpan: 0,
      };
    }
  }
  return column;
}
const regId = ref(null);
const addList = ref<any>([
  // { id: 1, name: "培训中心或学校登记员签字" },
  // { id: 1, name: "审核单位负责人签章" },
]);

async function getregSetting() {
  // addList.value=[]
  let res = (await getRegisterForm()) as any;
  console.log("res-ccc", res);
  regId.value = res.data.data ? res.data.data.eduRegisterFormId : "";
  registrationName.value = res.data.data ? res.data.data.lookUpName : "";
  signSet.value = res.data.data ? res.data.data.signSet : 0;
  let imgList = res.data.data.signUrl.split(",");
  let arr = [];
  if (res.data.data) {
    for (let i of res.data.data.signNames) {
      arr.push({
        id:
          i == "培训中心或学校登记员签字" || i == "审核单位负责人签章" ? 1 : 2,
        name: i,
        imgUrl: "",
      });
    }
    for (let j = 0; j < imgList.length; j++) {
      for (let k = 0; k < arr.length; k++) {
        if (j == k) {
          arr[j].imgUrl = imgList[k];
        }
      }
    }
    addList.value = [...arr];
  }

  console.log(addList.value);
}
//#endregion
</script>

<style lang="scss" scoped>
@page {
  size: landscape;
}

//横向
.printDiv {
  padding: 24px 40px;
  max-width: 880px;

  .registrationName {
    text-align: center;
    font-weight: bold;
    color: #262626;
    font-size: 17px;
  }

  .info {
    margin-top: 24px;

    .infoTop {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      justify-content: space-between;

      .item {
        width: 50%;
        height: 14px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
        margin-top: 30px;

        .name {
          display: inline-block;
          width: 50px;
        }

        .boderBottom {
          margin-left: 4px;
          text-indent: 4px;
          width: 248px;
          border-bottom: 1px solid #000000;
          opacity: 0.8;
        }
      }

      .item:nth-child(2n) {
        display: flex;
        justify-content: flex-end;
      }
    }

    .infoBottom {
      margin-left: 1px;
      margin-top: 21px;
      display: flex;
      align-items: center;

      .name {
        width: 64px;
        height: 20px;
        border: 1px solid rgba(0, 0, 0, 0.85);
        font-size: 12px;
        line-height: 20px;
        padding: 0 10px;
        text-align: center;
        border-bottom: none;
      }

      .name1 {
        padding: 0 4px;
        height: 20px;
        width: 100px;
        border: 1px solid rgba(0, 0, 0, 0.85);
        border-left: none;
        border-right: none;
        text-align: center;
        border-bottom: none;
      }

      .name2 {
        padding: 0 4px;
        text-align: center;
        height: 20px;
        width: 185px;
        border: 1px solid rgba(0, 0, 0, 0.85);
        border-left: none;
        border-right: none;
        border-bottom: none;
      }

      .name2:last-child {
        border-right: 1px solid rgba(0, 0, 0, 0.85);
      }
    }
  }

  .table {
    margin-top: -1px;

    .totalNum {
      // border: 1px solid red;
      display: flex;
      justify-content: center;
      align-items: center;
      width: calc(100% + 20px);
      position: relative;
      left: -10px;
      height: 100%;
      padding: 0 10px;
      border-right: 1px solid rgba(0, 0, 0, 0.85);
    }

    .trainingCategory {
      display: flex;
      justify-content: center;
      align-items: center;
      width: calc(100% - 10px);
      position: relative;
      left: 5px;
      height: 100%;
      // padding: 0 10px;
      // border-right: 1px solid rgba(0, 0, 0, 0.85);
    }
  }

  .sign {
    // margin-top: 24px;

    .signtitle {
      font-weight: bold;
    }

    .signAll {
      // margin-top: 8px;
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      // display: grid;
      // grid-template-columns:25%  25% 25% 25%;
      .item {
        // flex: 0 0 33%;
        width: 33.3%;
        padding: 4px 8px;
        // width: 265px;
        height: 150px;
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        border: 1px solid rgba(0, 0, 0, 0.85);
        border-top: none;
        border-right: none;
        position: relative;

        .time {
          position: absolute;
          right: 10px;
          bottom: 0;
        }

        .gaizhang {
          position: absolute;
          right: 110px;
          // margin-top: 5px;
          top: 70px;
          text-align: center;
        }

        .imgChapter {
          position: absolute;
          // left: 80px;
          top: -20px;
          left: -2px;
          width: 100%;
          height: 120%;
          padding: 10px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            height: 100%;
            // width: 100%;
          }
        }
      }

      .addRight {
        border-right: 1px solid rgba(0, 0, 0, 0.85);
      }

      .delTop {
        border-top: none;
      }

      .item:last-child {
        border-right: 1px solid rgba(0, 0, 0, 0.85);
      }
    }
  }
}
</style>
<style lang="scss">
.printDiv {
  .ant-table-cell {
    border: 1px solid rgba(0, 0, 0, 0.85);
    border-top: none;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    height: 23px !important;
  }

  .ant-table-thead {
    .ant-table-cell {
      border-top: 1px solid rgba(0, 0, 0, 0.85);
    }
  }

  .ant-table-thead > tr > th {
    font-size: 12px;
    text-align: center;

    // display: flex;
    // justify-content: center;
    // align-items: center;
    &:last-child {
      border-right: 1px solid rgba(0, 0, 0, 0.85) !important;
    }
  }

  .ant-table-tbody {
    font-size: 12px;
  }

  .ant-table-container table > thead > tr:first-child th:last-child {
    border-radius: 0 !important;
  }

  .ant-table-container table > thead > tr:first-child th:first-child {
    border-radius: 0 !important;
  }

  .ant-table-tbody > tr > td {
    &:last-child {
    }
  }
}
</style>
