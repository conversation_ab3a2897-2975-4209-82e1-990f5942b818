<template>
  <section class="header_container br4">
    <div class="item">
      <span>{{ oneTitle ? oneTitle : '搜索' }}:</span>
      <a-input style="flex: 1" v-model:value="name" :placeholder="oneText ? oneText : '姓名/身份证号/申报项目'"></a-input>
    </div>
    <div class="item">
      <span>申报类型:</span>
      <a-select style="flex: 1" v-model:value="type" :allowClear="true" placeholder="全部"
        :options="applyTypeOptions"></a-select>
    </div>
    <div class="item">
      <span>选择年度:</span>
      <!-- :disabledDate="disabledDate" -->
      <a-range-picker picker="month" @calendarChange="onCalendarChange" v-model:value="resourceDate"
        valueFormat="YYYY-MM" />
    </div>
    <div class="item">
      <span>审批状态:</span>
      <a-select style="flex: 1" v-model:value="status" :allowClear="true" placeholder="全部"
        :options="searchType == '1' ? applyStatusOptions2 : applyStatusOptions"></a-select>
    </div>
    <div class="item" v-if="addorg">
      <span>所属机构:</span>
      <!-- <a-input
        style="flex: 1"
        v-model:value="orgValue"
        placeholder="请输入"
      ></a-input> -->
      <!-- @search=" (value) => debounced.debouncedFc(() => handleSearch1(value),
      100) " -->
      <a-select v-model:value="orgValue" placeholder="请输入" style="flex: 1" :filter-option="false"
        optionFilterProp="userName" show-search @search="handleSearch1" :allowClear="true">
        <template v-if="loading % 2 != 0" #notFoundContent>
          <a-spin size="small" />
        </template>
        <a-select-option v-for="(item, i) in options" :value="item.id" :key="i">
          <div class="flex member_line">
            <span class="name text_overflow" v-html="item.displayName"></span>
          </div>
        </a-select-option>
      </a-select>
    </div>
    <div class="item1" v-if="!addorg"></div>
    <div class="item2"></div>
    <div class="item3"></div>
    <div style="text-align: right">
      <a-button type="primary" class="mr8" @click="handleSearch">查询</a-button>
      <a-button @click="handleReSet">重置</a-button>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import useResourceList from "@/hooks/useResourceList";
import { queryOrg } from "@/request";
import { debounced } from "@/utils/index";
const {
  disabledDate,
  onCalendarChange,
  applyTypeOptions,
  applyStatusOptions,
  applyStatusOptions2,
} = useResourceList();
// withDefaults(
//   defineProps<{
//     oneTitle?: string;
//     oneText?: string;
//     searchType?: string;
//     addorg?: boolean;
//     changeTab?:boolean;
//   }>(),
//   {
//     oneTitle: "搜索",
//     oneText: "姓名/身份证号/申报项目",
//     searchType: "",
//     addorg: false,
//     changeTab:false
//   }
// );
const props = defineProps<{
  oneTitle?: string;
  oneText?: string;
  searchType?: string;
  addorg?: boolean;
}>()

const emit = defineEmits(["search", "reSet"]);
/* --------------- data --------------- */
//#region
const loading = ref<number>(2);
const name = ref<string>("");
const type = ref<string | null>(null);
const resourceDate = ref<Array<any> | null>([]);
const status = ref<string | null>(null);
const orgValue = ref(null);
const key = ref<string>("");
const options = ref<Array<any>>([]);
const timer = ref<any>();
const searchValue = ref(null);
//#endregion

/* --------------- methods --------------- */
//#region
function handleSearch1(s: any) {
  options.value = [];
  searchValue.value = s;
  clearTimeout(timer.value);
  timer.value = null;
  timer.value = setTimeout(() => {
    search(s);
    clearTimeout(timer.value);
    timer.value = null;
  }, 400);
}
function search(s) {
  if (s != null || s != "") {
    loading.value++;
    let orgName = s;
    queryOrg(s).then((res: any) => {
      loading.value++;
      if (res.data.code == 0) {
        options.value = res.data.data;
      }
    });
  }
}
function handleSearch() {
  emit("search", {
    name: name.value,
    type: type.value,
    resourceDate: resourceDate.value,
    status: status.value,
    orgValue: orgValue.value,
  });
}

function handleReSet() {
  name.value = "";
  type.value = null;
  resourceDate.value = null;
  status.value = null;
  orgValue.value = null;
  emit("reSet");
}

//#endregion
</script>

<style lang="scss" scoped>
.header_container {
  padding: 20px 24px;
  background-color: #fff;
  display: grid;
  row-gap: 24px;

  .item {
    display: flex;
    align-items: center;

    &>span {
      padding-right: 8px;
      text-align: right;
      width: 104px;
    }
  }
}

@media screen and (min-width: 1280px) {
  .header_container {
    grid-template-columns: 25% 25% 25% 25%;
  }
}

@media screen and (max-width: 1280px) {
  .header_container {

    .item1,
    .item2 {
      display: none;
    }

    justify-content: space-between;
    grid-template-columns: 33% 33% 34%;
  }
}
</style>
