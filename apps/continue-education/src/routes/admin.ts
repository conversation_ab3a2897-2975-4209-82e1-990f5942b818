import { RouteRecordRaw } from 'vue-router'

const adminRouter: Array<RouteRecordRaw> = ([
    {
        path: '/admin',
        meta: {
        },
        component: () => import('@/pages/admin/index.vue'),
        children: [
            {
                path: 'declareSearch',
                redirect: '/admin/declareSearch/list',
                component: () => import('@/pages/admin/declareSearch/index.vue'),
                children: [
                    {
                        path: 'list',
                        component: () => import('@/pages/admin/declareSearch/list.vue'),
                    },
                    {
                        path: 'examineAdd',
                        component: () => import('@/pages/admin/declareSearch/declare.vue'),
                    },
                    {
                        path: 'examineEdit/:id',
                        component: () => import('@/pages/admin/declareSearch/declare.vue'),
                    },
                ]
            },
            {
                path: 'studyTime',
                component: () => import('@/pages/admin/studyTime/index.vue')
            },
            {
                path: 'standard',
                component: () => import('@/pages/admin/standard/index.vue')
            },
            {
                path: 'studyScore',
                component: () => import('@/pages/admin/studyScore/index.vue')
            },
            {
                path: 'examine',
                component: () => import('@/pages/admin/examine/index.vue'),
                children: [
                    {
                        path: 'examineSet',
                        component: () => import('@/pages/admin/examine/examineSet/index.vue')
                    },
                    {
                        path: 'confirmExamine',
                        component: () => import('@/pages/admin/examine/confirmExamine/index.vue')
                    },
                    {
                        path: 'wholeExamine',
                        component: () => import('@/pages/admin/examine/wholeExamine/index.vue')
                    }
                ]
            },
            {
                path: 'setting',
                component: () => import('@/pages/admin/setting/index.vue'),
                children: [
                    {
                        path: 'registrationSetting',
                        component: () => import('@/pages/admin/setting/registrationSetting/index.vue')
                    },
                    {
                        path: 'certificateRuleSetting',
                        component: () => import('@/pages/admin/setting/certificateRuleSetting/index.vue')
                    },
                    {
                        path: 'systemYearSetting',
                        component: () => import('@/pages/admin/setting/systemSetting/index.vue')
                    },
                    {
                        path: 'systemActivitySetting',
                        component: () => import('@/pages/admin/setting/systemActivitySetting/index.vue')
                    },
                    {
                        path: 'systemSetting',
                        component: () => import('@/pages/admin/setting/systemSetting1/index.vue')
                    }
                ]
            },
            {
                path: 'signProject',
                component: () => import('@/pages/admin/signProject/index.vue'),
                redirect: '/admin/signProject/signProjectList',
                children: [
                    {
                        path: 'signProjectList',
                        component: () => import('@/pages/admin/signProject/common/projectList.vue'),
                    },
                    {
                        path: 'signRecord',
                        component: () => import('@/pages/admin/signProject/common/signRecord.vue')
                    },
                    {
                        path: 'projectDetail',
                        component: () => import('@/pages/admin/signProject/common/projectDetail.vue')
                    }

                ]
            },

        ]
    }
])
export default adminRouter