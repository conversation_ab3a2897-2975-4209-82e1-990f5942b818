<template>
  <section class="study_score_container" ref="study_score_container">
    <div class="model_title p1624">
      <span>学分管理</span>
    </div>
    <SearchHeader :addorg="true" searchType="1" class="basic_header_search_"
      @search="(obj: any) => newSiftSearchForScore(obj)" @reSet="handleResSourceReset" />
    <section class="table_box bcf br4 p1624 scrollbars">
      <div class="flex_js mb16">
        <span>学分列表</span>
        <span class="tab_btns">
          <a-button :disabled="!!!selectedRowKeys.length" @click="handleDel(0, '')">删除</a-button>
          <a-button @click="importVisible = true" v-if="disabledUploadButton">导入</a-button>
          <a-button @click="handleExport(3)" type="primary">导出</a-button>
        </span>
      </div>
      <a-table @change="handleTabSort1" :dataSource="resourceListScore" :columns="columns"
        :rowSelection="{ selectedRowKeys, onChange: onSelectRowChange1, onSelect: onChooseChange1, onSelectAll: onChooseAllChange1 }"
        :pagination="false" row-key="eduResourceId">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'resourceName'">
            <span class="cursor c07" @click="handleOpenExamineDetail(record.eduResourceId)">
              {{ record.resourceName }}
            </span>
          </template>
          <template v-if="column.dataIndex == 'resourceType'">
            {{ record.resourceType == "1" ? "灵活性" : "规范性" }}
          </template>
          <template v-if="column.dataIndex == 'status'">
            <div class="flex_ac">
              <span class="spot mr8" :style="{
                backgroundColor: handleStatusSift(record.status).color,
              }"></span>
              {{ handleStatusSift(record.status).text }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'operation'">
            <div class="flex_js c07" style="padding-right: 16px">
              <a-popconfirm title="是否确认删除？" ok-text="确定" cancel-text="取消"
                :getPopupContainer="() => $refs.study_score_container" @confirm="handleDel(1, record.eduResourceId)">
                <span class="cursor" v-if="record.status != 1">删除</span>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </a-table>
      <ysPagination :pageNo="resourceParams.pageNo" :pageSize="resourceParams.pageSize" :total="resourceScoreTotal"
        @change="handlePaginationChange3" />
    </section>
    <ExamineDetails :record="true" :detailsType="2" :visible="examineDetailsVisible" :id="examineDetailsId"
      :resourceList="resourceListScore" @close="handleCloseExamineDetail" />
    <ImportModel :visible="importVisible" :type="4" @close="importVisible = false" @lookLog="
      importVisible = false;
    logVisible = true;
    " />
    <ImportLog :visible="logVisible" :id="examineDetailsId" @close="
      importVisible = true;
    logVisible = false;
    " />
    <Loading :loading="loading" />
  </section>
</template>

<script lang="ts" setup>
import { doUserLevel } from "@/request";
import { ref, onMounted } from "vue";
import Loading from "@/components/loading.vue";
import SearchHeader from "@/components/searchHeader.vue";
import ExamineDetails from "@/components/examineDetails/index.vue";
import ImportModel from "@/components/impoetModal.vue";
import ImportLog from "@/components/importLog.vue";
import { ysPagination } from "@ys/ui";
import { tableColumns } from "@/assets/types";
import useResourceList from "@/hooks/useResourceList";
import useExamineDetails from "@/hooks/useExamineDetails";
const {
  loading,
  resourceParams,
  resourceScoreTotal,
  resourceList,
  selectedRowKeys,
  onSelectRowChange1,
  onChooseChange1,
  onChooseAllChange1,
  resourceListScore,
  handleStatusSift,
  getScoreResourceList,
  handlePaginationChange3,
  handleResSourceReset,
  newSiftSearchForScore,
  handleExport,
  handleDeleteProject3,
  handleTabSort1
} = useResourceList();

const {
  examineDetailsVisible,
  examineDetailsId,
  examineDhowAlert,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();

onMounted(() => {
  resourceParams.sourceType = 1
  getScoreResourceList();
  getUploadPermission()
});
/* --------------- data --------------- */
//#region
const disabledUploadButton = ref<boolean>(false);
const columns = ref<Array<tableColumns>>([
  {
    title: "申报项目",
    dataIndex: "resourceName",
    ellipsis: true,
  },
  {
    title: "姓名",
    dataIndex: "userName",
    width: "134px",
    ellipsis: true,
  },
  {
    title: "身份证号",
    dataIndex: "idCard",
    width: "180px",
    ellipsis: true,
  },
  {
    title: "申报机构",
    dataIndex: "orgName",
    ellipsis: true,
  },
  {
    title: '学习地点',
    dataIndex: 'studyAddress',
    ellipsis: true
  },
  {
    title: "认证时间",
    dataIndex: "createTime",
    width: "180px",
    ellipsis: true,
    sorter: true,
  },
  {
    title: "申报类型",
    dataIndex: "resourceType",
    width: "104px",
  },
  {
    title: "学分",
    dataIndex: "classScore",
    width: "96px",
  },
  {
    title: "状态",
    dataIndex: "status",
    width: "114px",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: "74px",
  },
]);
const importVisible = ref<boolean>(false);
const logVisible = ref<boolean>(false);

//#endregion

/* --------------- methods --------------- */
//#region'

/* 删除 */
function handleDel(num: number, id: string) {
  if (!!!num) {
    handleDeleteProject3(selectedRowKeys.value);
  } else {
    examineDetailsId.value = id;
    handleDeleteProject3([examineDetailsId.value]);
  }
}
async function getUploadPermission() {
  let result = await doUserLevel() as any;
  if (result.data.data.isTopOrg == 1) {
    disabledUploadButton.value = true;
  }
}
//#endregion
</script>

<style lang="scss" scoped>
.study_score_container {
  height: 100%;
}

.table_box {
  width: calc(100% - 48px);
  height: calc(100% - 230px);
  margin: 0 auto;

  .tab_btns {
    .ant-btn {
      margin-left: 8px;
    }
  }
}
</style>
