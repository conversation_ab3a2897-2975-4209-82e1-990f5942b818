<template>
  <div class="standard">
    <a-dropdown v-if="yearList.length > 0">
      <a class="ant-dropdown-link" @click.prevent>
        {{ yearName }}
        <DownOutlined class="icon" />
      </a>
      <template #overlay v-show="yearList.length > 0">
        <a-menu style="max-height: 170px; overflow: auto">
          <a-menu-item
            v-for="(item, index) in yearList"
            :key="index"
            @click="handleChangeYear(item, index)"
          >
            <a href="javascript:;">{{ item.name }}</a>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <div class="searchInfo">
      <div class="item">
        <div class="title">搜索：</div>
        <div>
          <a-input
            style="width: 222px"
            placeholder="姓名"
            v-model:value="searchTxt"
          />
        </div>
      </div>
      <div class="item">
        <div class="title">所属机构：</div>
        <div>
          <a-select
            v-model:value="orgValue"
            placeholder="请输入"
            style="width: 222px"
            :filter-option="false"
            optionFilterProp="userName"
            show-search
            @search="handleSearch1"
            :allowClear="true"
          >
            <template v-if="loading % 2 != 0" #notFoundContent>
              <a-spin size="small" />
            </template>
            <a-select-option
              v-for="(item, i) in options"
              :value="item.id"
              :key="i"
            >
              <div class="flex member_line">
                <span
                  class="name text_overflow"
                  v-html="item.displayName"
                ></span>
              </div>
            </a-select-option>
          </a-select>
        </div>
      </div>
      <div class="item">
        <a-button type="primary" @click="hanldeSearch">查询</a-button>
        <a-button style="margin-left: 8px" @click="hanldeReset">重置</a-button>
      </div>
    </div>
    <div class="content">
      <div class="header">
        <span>教职工名单</span>
        <div class="right">
          <a-button @click="downLoadTable">导出筛选结果</a-button>
          <a-button
            v-if="topLevel"
            @click="openSetStandard"
            type="primary"
            style="margin-left: 8px"
            >设置学时达标要求</a-button
          >
        </div>
      </div>
      <div class="tableList">
        <a-table
          :columns="columns"
          :data-source="list"
          :pagination="false"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex == 'isStandard'">
              <div class="isStandard">
                <div
                  class="qureeBox"
                  :class="record.isStandard ? 'active' : 'noActive'"
                ></div>
                <div>{{ record.isStandard ? "已达标" : "未达标" }}</div>
              </div>
            </template>
            <template v-if="column.dataIndex == 'action'">
              <span class="cursor" @click="handleDetail(record, 1)"
                >查看达标明细</span
              >
              <span class="cursor" @click="handleDetail(record, 2)"
                >查看历年达标情况</span
              >
            </template>
          </template>
        </a-table>
        <ysPagination
          :pageNo="pageNo"
          :pageSize="pageSize"
          :total="total"
          @change="handlePaginationChange"
        />
      </div>
    </div>
  </div>
  <setStandard
    v-if="showSetstandard"
    :visible="showSetstandard"
    @close="showSetstandard = false"
  />
  <reviewdetails
    :recordItem="recordItem"
    :type="delailsType"
    v-if="showDetails"
    :visible="showDetails"
    @close="showDetails = false"
  />
</template>

<script lang="ts" setup>
import { ysPagination, ysIcon } from "@ys/ui";
import reviewdetails from "./details.vue";
import setStandard from "./setStandard.vue";
import { DownOutlined } from "@ant-design/icons-vue";
import { ref, onMounted } from "vue";
import {
  getSystemSet,
  queryOrg,
  exportComplianceTeacher,
  doUserOrgLevel,
} from "@/request";
import { complianceTeacher } from "@/request";
const yearList = ref<any>([]);
const yearName = ref("");
const isNowYearId = ref("");
const searchTxt = ref(null);
const orgValue = ref(null);
const options = ref<Array<any>>([]);
const timer = ref<any>();
const loading = ref<number>(2);
const list = ref<any>([1, 2, 3]);
const delailsType = ref(1);
const recordItem = ref(null);
const showDetails = ref(false);
const eduSystemId = ref(null);
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(0);
const columns = ref<any>([
  {
    title: "姓名",
    dataIndex: "userName",
    ellipsis: true,
  },
  {
    title: "所属机构",
    dataIndex: "orgName",
    ellipsis: true,
  },
  {
    title: "累计获取学分",
    dataIndex: "classHour",
  },
  {
    title: "整体达标情况",
    dataIndex: "isStandard",
  },
  {
    title: "操作",
    dataIndex: "action",
    ellipsis: true,
    width: "275px",
  },
]);
const showSetstandard = ref(false);
const topLevel = ref<any>(false);
const handlePaginationChange = (page: number, pageS: number) => {
  pageNo.value = page;
  pageSize.value = pageS;
  handleGetEduIndexData();
};
const downLoadTable = async () => {
  let params = {
    eduSystemId: eduSystemId.value,
    name: searchTxt.value,
    orgId: orgValue.value,
  };
  let name = "教职工名单.xlsx";
  let res: any;
  res = await exportComplianceTeacher(params);
  let blob = new Blob([res.data], { type: "application/vnd.ms-execl" });
  let link = window.URL.createObjectURL(blob);
  let a = document.createElement("a");
  document.body.appendChild(a);
  a.download = name;
  a.href = link;
  a.click();
  window.URL.revokeObjectURL(link);
  document.body.removeChild(a);
};
const handleDetail = (record: any, num: number) => {
  delailsType.value = num;
  recordItem.value = record;
  showDetails.value = true;
};
const openSetStandard = () => {
  showSetstandard.value = true;
};
const hanldeSearch = () => {
  handleGetEduIndexData();
};
const hanldeReset = () => {
  pageNo.value = 1;
  searchTxt.value = null;
  orgValue.value = null;
  handleGetEduIndexData();
};
function handleSearch1(s: any) {
  options.value = [];
  clearTimeout(timer.value);
  timer.value = null;
  timer.value = setTimeout(() => {
    search(s);
    clearTimeout(timer.value);
    timer.value = null;
  }, 400);
}
function search(s: any) {
  if (s != null || s != "") {
    loading.value++;
    let orgName = s;
    queryOrg(s).then((res: any) => {
      loading.value++;
      if (res.data.code == 0) {
        options.value = res.data.data;
      }
    });
  }
}
const handleChangeYear = (year: any, index: number) => {
  console.log("year", year);
  yearName.value = year.name;
  eduSystemId.value = year.eduSystemSetId;
  handleGetEduIndexData();
};
const getTime = async () => {
  try {
    const response = await getSystemSet();
    const res = response.data as any;
    yearList.value = res.data.map((item: any, key: number) => {
      item.index = key + 1;
      if (item.name == null) {
        item.name = `${item.startTime.split("-")[0]}-${
          item.endTime.split("-")[0]
        }`;
      }
      return item;
    });
    if (res.data && res.data.length > 0) {
      res.data.forEach((item: any) => {
        if (item.isNow == 1) {
          isNowYearId.value = item.eduSystemSetId;
        }
      });
    }
    console.log("yearList", yearList.value);

    let one = yearList.value.find((item: any) => {
      return item.isNow == 1;
    });
    eduSystemId.value = one.eduSystemSetId;
    let start1 = one.startTime;
    let end1 = one.endTime;
    yearName.value = `${one.name}`;
    handleGetEduIndexData();
  } catch (error) {}
};
const handleGetEduIndexData = () => {
  let params = {
    eduSystemId: eduSystemId.value,
    name: searchTxt.value,
    orgId: orgValue.value,
    pageIndex: pageNo.value,
    pageSize: pageSize.value,
  };
  complianceTeacher(params).then((res: any) => {
    if (res.data.code == 0) {
      list.value = res.data.data;
      total.value = Number(res.data.totalDatas);
    }
  });
};
const getTopLevel = () => {
  doUserOrgLevel().then((res: any) => {
    if (res.data.data == true) {
      topLevel.value = true;
    } else {
      topLevel.value = false;
    }
  });
};
onMounted(() => {
  getTime();
  getTopLevel();
});
</script>

<style lang="scss" scoped>
.cursor {
  margin-left: 16px;
  color: #007aff;
}
.standard {
  padding: 16px 24px;
  .searchInfo {
    margin-top: 16px;
    height: 84px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 24px;

    .item {
      display: flex;
      align-items: center;
      margin-right: 24px;
      .title {
        width: 104px;
        text-align: right;
      }
    }
  }
  .content {
    margin-top: 16px;
    // height: calc(100vh - 240px);
    background: #ffffff;
    border-radius: 4px;
    padding: 16px 24px;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .tableList {
      margin-top: 16px;
      .isStandard {
        display: flex;
        align-items: center;
        .qureeBox {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 8px;
        }
        .active {
          background: #17be6b;
        }
        .noActive {
          background: #f53f3f;
        }
      }
    }
  }
}
.ant-dropdown-link {
  // display: flex;
  // align-items: center;
  .icon {
    padding-top: 4px;
    margin-left: 4px;
    font-size: 16px;
  }
}
</style>
