<template>
  <a-drawer
    v-model:visible="props.visible"
    class="details-drawer"
    :title="props.type == 1 ? '查看达标明细' : '查看历年达标情况'"
    placement="right"
    width="560"
    @close="handleClose"
    :maskClosable="false"
  >
    <div class="content">
      <div class="teacher">
        <span>教师姓名：</span>
        <span>{{ props.recordItem.userName }}</span>
      </div>
      <div class="org">
        <span>所属机构：</span>
        <span>{{ props.recordItem.orgName }}</span>
      </div>
      <div class="tableList" v-if="props.type == 1">
        <a-table
          :columns="columns1"
          :data-source="props.recordItem.standardDetail"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex == 'classifyName'">
              <span>{{ record.classifyName }}</span>
              <span>大于等于</span>
              <span>{{ record.hour }}</span>
            </template>
            <template v-if="column.dataIndex == 'isStandard'">
              <div class="dabiaoBox">
                <div
                  class="squreBox"
                  :class="record.isStandard ? 'green' : 'red'"
                ></div>
                <span>{{ record.isStandard ? "已达标" : "未达标" }}</span>
              </div>
            </template>
          </template>
        </a-table>
      </div>
      <div class="tableList" v-else>
        <a-table :columns="columns2" :data-source="list2" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex == 'isStandard'">
              <div class="dabiaoBox">
                <div
                  class="squreBox"
                  :class="record.isStandard ? 'green' : 'red'"
                ></div>
                <span>{{ record.isStandard ? "已达标" : "未达标" }}</span>
              </div>
            </template>
          </template></a-table
        >
      </div>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { complianceHistory } from "@/request";
const props = defineProps<{
  visible: boolean;
  type: string | number;
  recordItem: any;
}>();
const emit = defineEmits(["close"]);
const columns1 = ref<any>([
  { title: "要求", dataIndex: "classifyName", ellipsis: true },
  { title: "实际获取学分", dataIndex: "realHour", width: "120px" },
  { title: "达标情况", dataIndex: "isStandard", width: "120px" },
]);
const list1 = ref<any>([1, 2, 3]);
const columns2 = ref<any>([
  { title: "学年", dataIndex: "eduSystemName" },
  { title: "达标情况", dataIndex: "isStandard", width: "120px" },
]);
const list2 = ref<any>([1, 2, 3]);
const handleClose = () => {
  emit("close");
};
const handleGetList = () => {
  console.log(props.recordItem);
  let params = {
    userId: props.recordItem.userId,
  };
  complianceHistory(params).then((res: any) => {
    if (res.data.code == 0) {
      list2.value = res.data.data;
    }
  });
};
onMounted(() => {
  handleGetList();
});
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.details-drawer {
  .content {
    padding: 24px;
    .teacher {
      margin-bottom: 12px;
    }
    .tableList {
      margin-top: 12px;
      .dabiaoBox {
        display: flex;
        align-items: center;
        .squreBox {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 8px;
        }
        .green {
          background: #17be6b;
        }
        .red {
          background: #f53f3f;
        }
      }
    }
  }
}
</style>
