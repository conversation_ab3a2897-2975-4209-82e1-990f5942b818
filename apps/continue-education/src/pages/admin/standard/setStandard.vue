<template>
  <a-drawer
    v-model:visible="props.visible"
    class="custom-class"
    title="设置学时达标要求"
    placement="right"
    width="560"
    @close="handleClose"
    :maskClosable="false"
  >
    <div class="setContent">
      <div class="header">
        <span>学时达标要求</span>
        <a-button type="primary" @click="addRequire" v-if="classification"
          >添加</a-button
        >
      </div>
      <div class="tableContent scrollbar">
        <a-table :columns="columns" :data-source="list" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex == 'classifyName'">
              <span>{{ record.classifyName }}</span>
              <span>大于等于</span>
              <span>{{ record.hour }}</span>
            </template>
            <template v-if="column.dataIndex == 'action'">
              <span
                class="cursor"
                @click="editRequire(record)"
                v-if="record.id != -1 && classification"
                >编辑</span
              >
              <span
                class="cursor"
                @click="delRequire(record)"
                v-if="record.id != -1 && classification"
                >删除</span
              >
            </template>
          </template>
        </a-table>
      </div>
      <!-- <div class="footer">
        <a-button @click="handleClose" class="btn">取消修改</a-button>
        <a-button type="primary" @click="handleSubmit" class="btn"
          >保存修改</a-button
        >
      </div> -->
    </div>
  </a-drawer>
  <a-modal
    v-model:visible="showAddModal"
    class="addModal"
    centered
    @ok="handleOk"
    @cancel="handleCancel"
    :width="448"
  >
    <div class="addContent">
      <div class="header">{{ modalType == 1 ? "添加" : "编辑" }}分类</div>
      <div class="chooseType">
        <!-- <a-select
          style="width: 368px"
          :options="options"
          placeholder="请选择分类"
          v-model:value="type"
        ></a-select> -->
        <a-cascader
          style="width: 368px"
          v-model:value="type"
          :options="typeList"
          placeholder="请选择分类"
          change-on-select
          @change="handleChangeCascader"
        />
      </div>
      <div class="hourInput">
        <span class="hourTitle">学时大于等于</span>
        <a-input-number
          v-model:value="hour"
          style="width: 276px"
          placeholder="请输入"
          :min="1"
          :max="999"
          :step="0"
          :precision="0"
        ></a-input-number>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, createVNode, onMounted } from "vue";
import { ysPagination } from "@ys/ui";
import { message, Modal } from "ant-design-vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import {
  complianceList,
  hanlleGetTree,
  saveCompliance,
  delCompliance,
  classifyCompliance,
  getConfig,
} from "@/request";
const props = defineProps<{
  visible: boolean;
}>();
const columns = ref<any>([
  { title: "要求", dataIndex: "classifyName", ellipsis: true },
  { title: "操作", dataIndex: "action", width: "120px" },
]);
const list = ref<any>([]);
const showAddModal = ref(false);
const modalType = ref(1);
const editId = ref(null);
const typeList = ref<any>([]);
const type = ref<any>([]);
const hour = ref(null);
const classification = ref(false);
const eduClassifyId = ref(null);
const isChange = ref(false);
const editCopyId = ref(null);
const init = () => {
  getConfig().then((res: any) => {
    if (res.data.code == 0) {
      // editId.value = res.data.data.id;
      classification.value = res.data.data.classifyApply;
    }
  });
};
const handleCancel = () => {
  type.value = [];
  hour.value = null;
  editId.value = null;
  showAddModal.value = false;
  editCopyId.value = null;
  isChange.value = false;
};
const editRequire = (record: any) => {
  eduClassifyId.value = record.eduClassifyId;
  editId.value = record.id;
  editCopyId.value = record.id;
  hour.value = record.hour;
  showAddModal.value = true;
  let arr = JSON.parse(JSON.stringify(record.cascaderPath));
  arr.splice(0, 1);
  type.value = arr;
  modalType.value = 2;
};
const handleChangeCascader = () => {
  classifyCompliance({ classifyId: type.value[type.value.length - 1] }).then(
    (res: any) => {
      if (res.data.code == 0) {
        if (res.data.data) {
          if (res.data.data.id) {
            isChange.value = true;
            editId.value = res.data.data.id;
            hour.value = res.data.data.hour;
            eduClassifyId.value = res.data.data.eduClassifyId;
          } else {
            isChange.value = false;
            hour.value = null;
            editId.value = null;
            eduClassifyId.value = null;
          }
        } else {
          isChange.value = false;
          hour.value = null;
          editId.value = null;
          eduClassifyId.value = null;
        }
      }
    }
  );
};

const delRequire = (record: any) => {
  Modal.confirm({
    title: "是否确认删除?",
    icon: createVNode(ExclamationCircleOutlined),
    centered: true,
    onOk() {
      delCompliance({ id: record.id }).then((res: any) => {
        if (res.data.code == 0) {
          message.success("删除成功");
          getList();
        }
      });
    },
    onCancel() {
      console.log("Cancel");
    },
    class: "test",
  });
};
const handleOk = () => {
  console.log("editId.value", editId.value);
  if (type.value.length == 0) {
    message.error("请选择分类");
    return;
  }
  if (!hour.value) {
    message.error("请填写学时");
    return;
  }
  let params = {
    eduClassifyId: isChange.value
      ? eduClassifyId.value
      : type.value[type.value.length - 1],
    hour: hour.value,
    id: isChange.value ? editId.value : editCopyId.value,
  };
  saveCompliance(params).then((res: any) => {
    if (res.data.code == 0) {
      let txt = editId.value ? "修改成功" : "添加成功";
      message.success(`${txt}`);
      showAddModal.value = false;
      getList();
      handleCancel();
    }
  });
  // showAddModal.value = false;
};
const addRequire = () => {
  showAddModal.value = true;
  modalType.value = 1;
};
const emit = defineEmits(["close"]);
const handleClose = () => {
  emit("close");
};
const handleSubmit = () => {};
const getList = () => {
  complianceList().then((res: any) => {
    if (res.data.code == 0) {
      let obj = {
        id: -1,
        classifyName: "总学时",
        hour: 72,
      };
      list.value = [obj, ...res.data.data];
    }
  });
};
const changeChildren = (tree: any) => {
  tree.forEach((node: any) => {
    node.children = node.chirldrenList;
    node.value = node.id;
    node.label = node.name;
    if (node.chirldrenList && node.chirldrenList.length > 0) {
      changeChildren(node.chirldrenList);
    }
  });
};
const handleGetTypeTree = async () => {
  let params = {
    status: 0,
  };
  const responce = await hanlleGetTree(params);
  const res: any = responce.data;
  typeList.value = res.data[0].chirldrenList || [];
  changeChildren(typeList.value);
};
onMounted(() => {
  init();
  handleGetTypeTree();
  getList();
});
</script>

<style lang="scss" scoped>
.setContent {
  //   padding: 0 24px;
  .header {
    padding: 0 24px;
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .tableContent {
    padding: 0 24px;
    margin-top: 12px;
    height: calc(100vh - 120px);
    overflow: auto;
    .cursor {
      color: #007aff;
      margin-right: 16px;
    }
  }
  .footer {
    padding: 0 24px;
    border-top: 1px solid #e5e5e5;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .btn {
      margin-left: 8px;
    }
  }
}
</style>
<style lang="scss">
.custom-class {
  .ant-drawer-content {
    // padding: 0 !important;
  }
}
.addModal {
  //   border: 1px solid red;
  .ant-modal-body {
    .addContent {
      .header {
        display: flex;
        justify-content: center;
        font-size: 16px;
      }
      .chooseType {
        display: flex;
        justify-content: center;
        margin-top: 24px;
      }
      .hourInput {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 24px;
        .hourTitle {
          margin-right: 12px;
        }
      }
    }
  }
  .ant-modal-footer {
    border: none;
    padding: 0 40px 24px 40px;
  }
}
</style>
