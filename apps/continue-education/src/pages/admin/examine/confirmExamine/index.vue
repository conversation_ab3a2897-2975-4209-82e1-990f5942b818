<template>
  <section class="confirm_examine_container" ref="confirm_examine_container">
    <div class="model_title p1624">
      <span>个人认证审核</span>
    </div>
    <SearchHeader
      :addorg="true"
      class="basic_header_search_"
      @search="(obj: any) => newSiftSearch(obj)"
      @reSet="handleResourceReset"
    />
    <section class="table_box bcf br4 p1624 scrollbars">
      <div class="flex_js mb16">
        <span
          >审核列表 <span class="c8c">（待审核{{ unAuditCount }}）</span>
        </span>
        <a-button
          type="primary"
          :disabled="!!!selectedRowKeys.length"
          @click="batchDeclareVisible = true"
        >
          批量审核
        </a-button>
      </div>
      <a-table
        :dataSource="resourceList"
        :columns="columns"
        @change="handleTabSort2"
        :rowSelection="{
          selectedRowKeys,
          onChange: onSelectRowChange,
          getCheckboxProps,
        }"
        :pagination="false"
        row-key="eduResourceId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'resourceName'">
            <span
              class="cursor c07"
              @click="handleOpenExamineDetail(record.eduResourceId)"
            >
              {{ record.resourceName }}
            </span>
          </template>
          <template v-if="column.dataIndex == 'resourceType'">
            {{ record.resourceType == "1" ? "灵活性" : "规范性" }}
          </template>
          <template v-if="column.dataIndex == 'status'">
            <div class="flex_ac">
              <span
                class="spot mr8"
                :style="{
                  backgroundColor: handleStatusSift(record.status).color,
                }"
              ></span>
              {{ handleStatusSift(record.status).text }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'operation'">
            <div
              v-if="record.status == 0"
              class="flex_js c07"
              style="padding-right: 16px"
            >
              <span
                class="cursor"
                @click="
                  examineDetailsId = record.eduResourceId;
                  openCheckModel(record);
                "
              >
                通过
              </span>
              <!-- <OnePass @click="examineDetailsId = record.eduResourceId" @ok="handleAuditOnePass(record)" /> -->
              <span class="cursor" @click="handleAuditOneRejectOpen(record)"
                >拒绝</span
              >
            </div>
          </template>
        </template>
      </a-table>
      <ysPagination
        :pageNo="resourceParams.pageNo"
        :pageSize="resourceParams.pageSize"
        :total="resourceTotal"
        @change="handlePaginationChange"
      />
    </section>
    <ExamineDetails
      :visible="examineDetailsVisible"
      :footerBtnS="true"
      :showAlert="true"
      :resourceList="resourceList"
      :id="examineDetailsId"
      @close="handleCloseDetails"
    />
    <BatchDeclare
      :visible="batchDeclareVisible"
      :num="selectedRowKeys.length"
      @close="batchDeclareVisible = false"
      @define="(data: any) => handleBatchDefine(data)"
    />
    <RejectReason
      :visible="rejectReasonVisible"
      :needReason="needReason"
      @close="handleAuditOneRejectCancel"
      @define="(reason: string) => handleAuditOneRejectConfirm(reason)"
    />
    <Loading :loading="loading" />
    <checkModel
      v-if="showChecked"
      @close="closeCheckModel"
      @sure="sureChecked"
    />
  </section>
</template>

<script lang="ts" setup>
import checkModel from "@/components/checkModel.vue";
import { ref, onMounted, nextTick } from "vue";
import Loading from "@/components/loading.vue";
import SearchHeader from "@/components/searchHeader.vue";
import ExamineDetails from "@/components/examineDetails/index.vue";
import BatchDeclare from "@/components/batchDeclare.vue";
import RejectReason from "@/components/examineDetails/rejectReason.vue";
import OnePass from "@/components/onePass.vue";
import { ysPagination } from "@ys/ui";
import { tableColumns } from "@/assets/types";
import useExamineDetails from "@/hooks/useExamineDetails";
import useResourceList from "@/hooks/useResourceList";
const {
  loading,
  resourceParams,
  resourceTotal,
  resourceList,
  selectedRowKeys,
  onSelectRowChange,
  batchDeclareVisible,
  rejectReasonVisible,
  unAuditCount,
  handleTabSort2,
  handleStatusSift,
  getResouceAuditList,
  handlePaginationChange,
  handleResourceReset,
  newSiftSearch,
  getCheckboxProps,
  handleBatchDefine,
  handleAuditOneRejectOpen,
  handleAuditOneRejectConfirm,
  handleAuditOneRejectCancel,
  handleAuditOnePass,
  getResouceUnAuditCount,
} = useResourceList();
const {
  examineDetailsVisible,
  examineDetailsId,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();
onMounted(() => {
  getResouceAuditList(true);
});

/* --------------- data --------------- */
//#region
const showChecked = ref(false);
const columns = ref<Array<tableColumns>>([
  {
    title: "申报项目",
    dataIndex: "resourceName",
    ellipsis: true,
  },
  {
    title: "所属机构",
    dataIndex: "orgName",
    ellipsis: true,
  },
  {
    title: "姓名",
    dataIndex: "userName",
    width: "134px",
    ellipsis: true,
  },
  {
    title: "身份证号",
    dataIndex: "idCard",
    width: "214px",
    ellipsis: true,
  },
  {
    title: "认证时间",
    dataIndex: "createTime",
    width: "212px",
    sorter: true,
    ellipsis: true,
  },
  {
    title: "申报类型",
    dataIndex: "resourceType",
    width: "120px",
  },
  {
    title: "申报分类",
    dataIndex: "classifyName",
    width: "200px",
    ellipsis: true,
  },
  {
    title: "学时",
    dataIndex: "classHour",
    width: "120px",
  },
  {
    title: "状态",
    dataIndex: "status",
    width: "120px",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: "112px",
  },
]);
const needReason = ref<boolean>(true);
const rowObj = ref<any>({});
const handleCloseDetails = () => {
  handleCloseExamineDetail();
  getResouceAuditList(true);
};
const openCheckModel = (val: any) => {
  showChecked.value = true;
  rowObj.value = val;
};
const closeCheckModel = () => {
  showChecked.value = false;
};
const sureChecked = () => {
  handleAuditOnePass(rowObj.value);
  nextTick(() => {
    showChecked.value = false;
  });
};
//#endregion
</script>

<style lang="scss" scoped>
.confirm_examine_container {
  height: 100%;
}

.table_box {
  width: calc(100% - 48px);
  height: calc(100% - 230px);
  margin: 0 auto;
}

::v-deep(.ant-popover-inner-content) {
  .ant-btn {
    height: 24px;
    border-radius: 2px;
  }
}
</style>
