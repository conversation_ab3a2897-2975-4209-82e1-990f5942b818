<template>
  <a-modal
    v-if="visible"
    v-model:visible="visible"
    centered
    width="448px"
    wrapClassName="basic_modal_reset_li add_examine_member_li"
    @cancel="emit('cancel')"
    @ok="handleOk"
  >
    <div class="flex_center mb24" style="font-size: 16px">
      添加{{
        props.type == 8
          ? "校"
          : props.type == 4
            ? "县"
            : props.type == 2 || props.type == 1
              ? "市"
              : ""
      }}审核人员
    </div>
    <a-select
      v-model:value="value"
      dropdownClassName="add_member_select_dorp_li"
      placeholder="姓名/手机号/身份证号"
      :filter-option="false"
      mode="multiple"
      optionFilterProp="userName"
      style="width: 100%"
      show-search
      @search="handleSearch"
    >
      <template v-if="loading % 2 != 0" #notFoundContent>
        <a-spin size="small" />
      </template>
      <a-select-option
        v-for="(item, i) in options"
        :value="item.userId"
        :key="i"
      >
        <div class="flex member_line">
          <span
            class="name text_overflow"
            v-html="brightenkeyword(item.userName, key)"
          ></span>
          <span
            v-if="!!item.phone"
            class="phone"
            v-html="`，${brightenkeyword(infoHidden(1, item.phone), key)}`"
          ></span>
          <span
            v-if="item.IdCard"
            class="personCode"
            v-html="`，${brightenkeyword(infoHidden(2, item.idCard), key)}`"
          ></span>
        </div>
      </a-select-option>
    </a-select>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { brightenkeyword, infoHidden } from "@/utils";
import { getAddAuditPerson, saveAuditPerson } from "@/request";
import { message } from "ant-design-vue";

export interface allPersonItem {
  eduResourceAuditPersonId: string | null;
  idCard: string | null;
  level: string | null;
  phone: string | null;
  userId: string | null;
  userIds: string | null;
  userName: string | null;
}

const props = withDefaults(
  defineProps<{
    visible: boolean;
    type: number;
    tabActive: any;
    checkTypeValue: any;
  }>(),
  {
    visible: false,
  }
);
watch(
  () => props.visible,
  (bol: boolean) => {
    visible.value = bol;
    value.value = [];
    if (bol) {
      search("");
    }
  }
);

const emit = defineEmits(["cancel", "ok"]);
/* --------------- data --------------- */
//#region
const loading = ref<number>(2);
const visible = ref<boolean>(false);
const value = ref<any>([]);
const key = ref<string>("");
const options = ref<Array<any>>([]);
const timer = ref<any>();
//#endregion

/* --------------- methods --------------- */
//#region
function search(s: string) {
  loading.value++;
  getAddAuditPerson({
    pageNo: 1,
    pageSize: 9999,
    search: s,
  }).then((res: any) => {
    loading.value++;
    if (res.data.code == 0) {
      options.value = res.data.data;
    }
  });
}
function handleSearch(s: any) {
  options.value = [];
  clearTimeout(timer.value);
  timer.value = null;
  timer.value = setTimeout(() => {
    key.value = !!s ? s : "";
    search(s);
    clearTimeout(timer.value);
    timer.value = null;
  }, 400);
}
function handleOk() {
  if (!!!value.value.length) return message.warning("请先选择审核人员");
  console.log("props.tabActive", props.tabActive);
  console.log("props.checkTypeValue", props.checkTypeValue);
  let params = {
    level: props.checkTypeValue == 0 ? props.type : null,
    userIds: value.value,
    type: props.tabActive,
    reviewMode: props.checkTypeValue,
  };
  saveAuditPerson(params).then((res: any) => {
    if (res.data.code == 0) {
      message.success("添加成功");
      emit("ok");
    }
  });
}
//#endregion
</script>
<style lang="scss" scoped>
.member_line {
  .phone,
  .personCode {
    display: none;
  }
}

::v-deep(.ant-select-selector:after) {
  display: none !important;
}
</style>
<style lang="scss">
.add_examine_member_li {
  .ant-select-selection-item {
    line-height: 24px !important;

    .ant-select-selection-item-remove {
      line-height: 20px !important;
    }
  }
}

.add_member_select_dorp_li {
  .ant-select-item {
    padding: 0;

    .member_line {
      padding: 5px 12px;

      .phone,
      .personCode {
        display: inline-block;
      }

      .name {
        max-width: calc(100% - 220px);
      }

      .phone {
        max-width: 101px;
      }

      .personCode {
        max-width: 119px;
      }
    }

    .ant-select-item-option-state {
      padding: 5px 12px 0 0;
    }
  }
}
</style>
