<template>
  <div
    v-for="(item, i) in switchS"
    :key="i"
    :class="[`switch_line ${i < switchS.length - 1 ? 'border_bottom' : ''}`]"
  >
    <div class="line_title">
      <span v-if="tabActive == 0"
        >{{ i == 0 ? "校" : i == 1 ? "县" : i == 2 ? "市" : "省" }}级审核</span
      >
      <span v-else>{{ i == 0 ? "县" : i == 1 ? "市" : "省" }}级审核</span>
    </div>
    <div class="flex">
      <span class="small_title">是否审核:</span>
      <a-switch
        v-model:checked="switchS[i]"
        :checkedValue="1"
        :unCheckedValue="0"
        @change="handleOpenChange(i)"
      />
    </div>
    <div v-if="tabActive == 0">
      <div
        v-if="
          (i == 0 && currentUserPermise == 8) ||
          (i == 1 && currentUserPermise == 4) ||
          (i == 2 && currentUserPermise == 2) ||
          (i == 3 && currentUserPermise == 1)
        "
        class="flex"
      >
        <span class="small_title">审核人员:</span>
        <a @click="handleSet(i)">设置</a>
      </div>
    </div>
    <div v-else>
      <div
        v-if="
          (i == 0 && currentUserPermise == 4) ||
          (i == 1 && currentUserPermise == 2) ||
          (i == 2 && currentUserPermise == 1)
        "
        class="flex"
      >
        <span class="small_title">审核人员:</span>
        <a @click="handleSet(i)">设置</a>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { message } from "ant-design-vue";
import { doUserLevel, getAuditSetting, editAuditSetting } from "@/request";
const props = withDefaults(
  defineProps<{
    tabActive?: string | number;
    type: any;
    reviewMode: any;
  }>(),
  {
    tabActive: "1",
    type: "0",
    reviewMode: "0",
  }
);
watch(
  () => props.tabActive,
  (val) => {
    getSet();
  },
  { immediate: true, deep: true }
);
watch(
  () => props.reviewMode,
  (val) => {
    if (val) {
      getSet();
    }
  },
  { immediate: true, deep: true }
);
const switchS = ref<Array<number>>([]);
const currentUserPermise = ref<number>(1);
const isTopOrg = ref<boolean>(true);
const drawerType = ref<number>(1);

const eduResourceSettingId = ref<string>("");
const emit = defineEmits(["openPersonVisible", ""]);
function handleOpenChange(i: number) {
  let permiseError: boolean = false;
  if (!isTopOrg.value) {
    switchS.value[i] = switchS.value[i] == 1 ? 0 : 1;
    permiseError = true;
  }
  if (permiseError) return message.warning("无权限操作其他审核");
  let params: any = {
    eduResourceSettingId: eduResourceSettingId.value,
    xianOpen: props.tabActive == 0 ? switchS.value[1] : switchS.value[0],
    xiaoOpen: props.tabActive == 0 ? switchS.value[0] : null,
    type: props.type,
    reviewMode: props.reviewMode,
  };
  // console.log("switchS.value", switchS.value);
  if (switchS.value.length == 3) {
    if (props.tabActive == 0) {
      params.shiOpen = switchS.value[2];
    } else {
      params.shiOpen = switchS.value[1];
      params.shengOpen = switchS.value[2];
    }
  }
  if (switchS.value.length == 4) {
    if (props.tabActive == 0) {
      params.shiOpen = switchS.value[2];
      params.shengOpen = switchS.value[3];
    } else {
      params.shiOpen = switchS.value[1];
      params.shengOpen = switchS.value[2];
    }
  }
  // console.log("params", params);
  // return;
  editAuditSetting(params).then((res: any) => {
    if (res.data.code == 0) {
      message.success("设置成功");
    } else getSet();
  });
}
function getSet() {
  getAuditSetting({ type: props.tabActive }).then((res: any) => {
    if (res.data.code == 0) {
      let data = res.data.data;
      eduResourceSettingId.value = data.eduResourceSettingId;
      if (props.tabActive == 0) {
        switchS.value = [data.xiaoOpen, data.xianOpen];
      } else {
        switchS.value = [data.xianOpen];
      }

      if (typeof data.shiOpen == "number") switchS.value.push(data.shiOpen);
      if (typeof data.shengOpen == "number") switchS.value.push(data.shengOpen);
    }
  });
}
function handleSet(i: number) {
  let obj = {
    currentUserPermise: currentUserPermise.value,
    show: true,
  };
  emit("openPersonVisible", obj);
  //   personVisible.value = true;
}

onMounted(async () => {
  let res: any = await doUserLevel();
  if (res.data.code == 0) {
    isTopOrg.value = res.data.data.isTopOrg != 1 ? false : true;
    currentUserPermise.value = res.data.data.currentUserPermise;
  } else currentUserPermise.value = 0;
  getSet();
});
defineExpose({
  switchS,
  getSet,
});
</script>

<style lang="scss" scoped>
.switch_line {
  padding-bottom: 32px;
  margin-bottom: 32px;
  margin-top: 24px;
  & > div {
    padding-left: 24px;
    margin-bottom: 24px;
  }

  & > div:first-child {
    padding: 0;
    margin-bottom: 12px;
  }

  & > div:last-child {
    margin-bottom: 0;
  }

  .line_title {
    font-weight: bold;
    font-family: "黑体";
  }

  .small_title {
    width: 80px;
  }
}
</style>
