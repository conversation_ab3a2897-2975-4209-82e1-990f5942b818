<template>
  <!-- <Transition name="slide-fade"> -->
  <section class="examine_container" ref="examine_container">
    <div class="model_title p1624">
      <span>审核设置</span>
    </div>
    <div
      v-if="currentUserPermise == 0"
      class="container bcf br4 flex_center fb"
    >
      暂无权限
    </div>
    <div v-else class="container bcf br4 scrollbars p24">
      <a-alert
        type="warning"
        v-if="alertShow"
        class="mb24"
        :message="msgText"
        show-icon
      ></a-alert>
      <div class="tabs">
        <div
          class="tabItem"
          :class="[item.id == tabActive ? 'blueBox' : '']"
          v-for="(item, index) in tabList"
          :key="item.id"
          @click="handleChangeTab(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="checkType" v-if="tabActive == 0">
        <span>审核模式：</span>
        <a-radio-group
          v-model:value="checkTypeValue"
          @change="changeGroup(0)"
          name="radioGroup"
        >
          <a-radio :value="0">自定义</a-radio>
          <a-radio :value="1" style="margin-left: 12px">仅所属机构审核</a-radio>
          <a-radio style="margin-left: 12px" :value="2"
            >所属机构+直属上级审核（顶级机构仅本机构审核）</a-radio
          >
        </a-radio-group>
      </div>
      <div class="checkType" v-else>
        <span>审核模式：</span>
        <a-radio-group
          v-model:value="checkTypeValue"
          @change="changeGroup(1)"
          name="radioGroup"
        >
          <a-radio :value="0">自定义</a-radio>
          <a-radio style="margin-left: 12px" :value="3"
            >仅同级审核（校级由上一级教育局审核）</a-radio
          >
        </a-radio-group>
      </div>
      <div class="checkPerson" v-if="checkTypeValue != 0">
        <span>本机构审核人员：</span>
        <span style="color: #007aff; cursor: pointer" @click="openSetPersion"
          >设置</span
        >
      </div>
      <div class="template" v-show="checkTypeValue == 0">
        <templateCheck
          :type="tabActive"
          :reviewMode="checkTypeValue"
          ref="templateCheckref"
          :tabActive="tabActive"
          @openPersonVisible="openPersonVisible"
        />
      </div>
    </div>
    <ExamineMember
      :tabActive="tabActive"
      :visible="personVisible"
      :type="drawerType"
      :checkTypeValue="checkTypeValue"
      @close="personVisible = false"
    />
    <Loading :loading="load % 2 !== 0" />
  </section>
  <!-- </Transition> -->
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import ExamineMember from "./examineMember.vue";
import templateCheck from "./templateCheck.vue";
import Loading from "@/components/loading.vue";
import { message } from "ant-design-vue";
import { doUserLevel, getAuditSetting, editAuditSetting } from "@/request";
onMounted(async () => {
  let res: any = await doUserLevel();
  if (res.data.code == 0) {
    console.log(res.data.data);
    isTopOrg.value = res.data.data.isTopOrg != 1 ? false : true;
    currentUserPermise.value = res.data.data.currentUserPermise;
  } else currentUserPermise.value = 0;
  getSet();
});

/* --------------- data --------------- */
//#region
const load = ref<number>(2);
const alertShow = ref<boolean>(false);
const msgText = ref<string>("调整审核设置后，可能会影响审核节点，请谨慎操作");
const eduResourceSettingId = ref<string>("");
const personVisible = ref<boolean>(false);
// 98为仅所属机构时类型，99为所属机构+直属上级
const drawerType = ref<number>(1);
const currentUserPermise = ref<number>(1);
const isTopOrg = ref<boolean>(true);
const tabList = ref<any>([
  { id: 0, name: "个人申报学时" },
  {
    id: 1,
    name: "整体申报学时",
  },
]);
const tabActive = ref<number>(0);
const checkTypeValue = ref<any>(0);
const templateCheckref = ref<any>();
//#endregion

/* --------------- methods --------------- */
//#region
const openSetPersion = () => {
  personVisible.value = true;
  drawerType.value = 98;
};
const openPersonVisible = (obj: any) => {
  personVisible.value = obj.show;
  drawerType.value = obj.currentUserPermise;
};
const changeGroup = (num: any) => {
  let groupSwitch = templateCheckref.value.switchS;
  // console.log("groupSwitch", groupSwitch);
  // console.log("num", num);
  let params: any = {
    type: tabActive.value,
    reviewMode: checkTypeValue.value,
    eduResourceSettingId: eduResourceSettingId.value,
    xianOpen: groupSwitch[1],
    xiaoOpen: groupSwitch[0],
  };
  if (groupSwitch.length == 3) params.shiOpen = groupSwitch[2];
  if (groupSwitch.length == 4) {
    params.shiOpen = groupSwitch[2];
    params.shengOpen = groupSwitch[3];
  }
  if (checkTypeValue.value != 0) {
    delete params.xianOpen;
    delete params.xiaoOpen;
    delete params.shiOpen;
    delete params.shengOpen;
  }
  editAuditSetting(params).then((res: any) => {
    if (res.data.code == 0) {
      getSet();
    }
  });
};
const handleChangeTab = (num: any) => {
  tabActive.value = num;
  // templateCheckref.value.getSet()
  // checkTypeValue.value = 0;
  // let params = {
  //   type: tabActive.value,
  //   eduResourceSettingId: eduResourceSettingId.value,
  // };
  // editAuditSetting(params).then((res: any) => {
  //   if (res.data.code == 0) {
  //     getSet();
  //   }
  // });
  getSet();
};
function getSet() {
  console.log(111);
  load.value++;
  getAuditSetting({ type: tabActive.value }).then((res: any) => {
    load.value++;
    if (res.data.code == 0) {
      let data = res.data.data;
      eduResourceSettingId.value = data.eduResourceSettingId;
      tabActive.value = data.type;
      checkTypeValue.value = data.reviewMode;
      alertShow.value = true;
    }
  });
}

//#endregion
</script>

<style lang="scss" scoped>
.examine_container {
  height: 100%;
  overflow-x: hidden;

  .container {
    margin: 0 auto;
    width: calc(100% - 48px);
    height: calc(100% - 78px);
    .tabs {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      .tabItem {
        margin-right: 32px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        cursor: pointer;
      }
      .blueBox {
        color: #007aff;
        border-bottom: 3px solid #007aff;
      }
    }
  }

  // .switch_line {
  //   padding-bottom: 32px;
  //   margin-bottom: 32px;

  //   & > div {
  //     padding-left: 24px;
  //     margin-bottom: 24px;
  //   }

  //   & > div:first-child {
  //     padding: 0;
  //     margin-bottom: 12px;
  //   }

  //   & > div:last-child {
  //     margin-bottom: 0;
  //   }

  //   .line_title {
  //     font-weight: bold;
  //     font-family: "黑体";
  //   }

  //   .small_title {
  //     width: 80px;
  //   }
  // }

  .border_bottom {
    border-bottom: 1px solid #f0f0f0;
  }
  .checkPerson {
    margin-top: 24px;
  }
}
</style>
