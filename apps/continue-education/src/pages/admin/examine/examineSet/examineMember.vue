<template>
  <a-drawer
    v-model:visible="visible"
    :maskClosable="false"
    :title="`${props.type == 8 ? '校级' : props.type == 4 ? '县级' : props.type == 2 ? '市级' : props.type == 1 ? '省级' : ''}审核人员设置`"
    width="720px"
    @close="emit('close')"
  >
    <section class="box" ref="person_examine_drawer">
      <div class="flex_js mb24">
        <span
          >{{
            props.type == 8
              ? "校"
              : props.type == 4
                ? "县"
                : props.type == 2
                  ? "市"
                  : props.type == 1
                    ? "省"
                    : ""
          }}审核人员</span
        >
        <span class="flex">
          <a-input-search
            v-model:value="value"
            placeholder="姓名/手机号/身份证号"
            style="width: 240px"
            @search="onSearch"
          />
          <a-button
            type="primary"
            style="margin-left: 16px"
            @click="addVisible = true"
          >
            <PlusOutlined />添加
          </a-button>
        </span>
      </div>
      <a-table :dataSource="data" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex == 'index'">
            {{ index + 1 + (pageNo - 1) * pageSize }}
          </template>
          <template v-if="column.dataIndex == 'phone'">
            {{ infoHidden(1, record.phone, "--") }}
          </template>
          <template v-if="column.dataIndex == 'idCard'">
            {{ infoHidden(2, record.idCard, "--") }}
          </template>
          <template v-if="column.dataIndex == 'operation'">
            <a-popconfirm
              placement="bottomRight"
              title="确认删除?"
              :getPopupContainer="() => $refs.person_examine_drawer"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDel(record.eduResourceAuditPersonId)"
            >
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
      <ysPagination
        :pageNo="pageNo"
        :pageSize="pageSize"
        :total="total"
        @change="handlePageChange"
      />
    </section>
    <AddMember
      :tabActive="props.tabActive"
      :checkTypeValue="props.checkTypeValue"
      :visible="addVisible"
      :type="props.type"
      @cancel="addVisible = false"
      @ok="handleAddSuccess"
    />
    <Loading :loading="loading % 2 != 0" />
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { tableColumns } from "@/assets/types";
import { ysPagination } from "@ys/ui";
import { PlusOutlined } from "@ant-design/icons-vue";
import { infoHidden } from "@/utils";
import AddMember from "./addMember.vue";
import { getAuditPerson, delAuditPerson } from "@/request";
import Loading from "@/components/loading.vue";
import { allPersonItem } from "@/pages/admin/examine/examineSet/addMember.vue";
import { message } from "ant-design-vue";
const props = defineProps<{
  visible: boolean;
  type: number;
  checkTypeValue: any;
  tabActive: any;
}>();
watch(
  () => props.visible,
  (bol) => {
    visible.value = bol;
    value.value = "";
    if (bol) init();
  }
);
const emit = defineEmits(["close"]);

/* --------------- data --------------- */
//#region
const loading = ref<number>(2);
const visible = ref<boolean>(false);
const value = ref<string>("");
const columns = ref<Array<tableColumns>>([
  { title: "序号", dataIndex: "index", width: "76px" },
  { title: "姓名", dataIndex: "userName", width: "143px", ellipsis: true },
  { title: "手机号", dataIndex: "phone", width: "180px" },
  { title: "身份证号", dataIndex: "idCard", width: "220px" },
  { title: "操作", dataIndex: "operation", width: "64px", ellipsis: true },
]);
const data = ref<Array<allPersonItem>>([]);
const pageNo = ref<number>(1);
const pageSize = ref<number>(10);
const total = ref<number>(0);
const addVisible = ref<boolean>(false);
//#endregion

/* --------------- methods --------------- */
//#region

function init() {
  loading.value++;
  console.log("props.reviewMode", props.checkTypeValue);
  let params = {
    level: props.checkTypeValue == 0 ? props.type : null,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    search: value.value,
    type: props.tabActive,
    reviewMode: props.checkTypeValue,
  };
  getAuditPerson(params).then((res: any) => {
    loading.value++;
    if (res.data.code == 0) {
      data.value = res.data.data;
      total.value = Number(res.data.totalDatas);
    }
  });
}

function onSearch() {
  pageNo.value = 1;
  init();
}

function handlePageChange(no: number, size: number) {
  pageNo.value = no;
  pageSize.value = size;
  init();
}
function handleDel(id: string) {
  delAuditPerson({ userId: id }).then((res: any) => {
    if (res.data.code == 0) {
      message.success("已删除");
      init();
    }
  });
}

function handleAddSuccess() {
  pageNo.value = 1;
  addVisible.value = false;
  init();
}

//#endregion
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 24px;
}

.line {
  .small_title {
    width: 80px;
  }

  .text {
    width: calc(100% - 80px);
  }
}

::v-deep(.ant-popover-inner-content) {
  .ant-btn {
    height: 24px;
    border-radius: 2px;
  }
}
</style>
