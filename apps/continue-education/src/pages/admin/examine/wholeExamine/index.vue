<template>
  <section class="confirm_examine_container" ref="confirm_examine_container">
    <div class="model_title p1624">
      <span>整体认证审核</span>
    </div>
    <div class="header_container br4">
      <div class="item">
        <span>搜索</span>
        <a-input
          style="flex: 1"
          v-model:value="searchName"
          placeholder="申报项目"
        ></a-input>
      </div>
      <div class="item">
        <span>申报时间:</span>
        <!-- :disabledDate="disabledDate" -->
        <a-range-picker
          picker="month"
          @calendarChange="onCalendarChange"
          v-model:value="resourceDate"
          valueFormat="YYYY-MM"
        />
      </div>
      <div class="item">
        <span>审批状态:</span>
        <a-select
          style="flex: 1"
          v-model:value="status"
          :allowClear="true"
          placeholder="全部"
          :options="applyStatusOptions"
        ></a-select>
      </div>
      <div class="item" style="margin-left: 12px">
        <a-button type="primary" class="mr8" @click="handleSearch"
          >查询</a-button
        >
        <a-button @click="handleReSet">重置</a-button>
      </div>
    </div>
    <!-- <SearchHeader
      :addorg="true"
      class="basic_header_search_"
      @search="(obj: any) => newSiftSearch(obj)"
      @reSet="handleResourceReset"
    /> -->
    <section class="table_box bcf br4 p1624 scrollbars">
      <div class="flex_js mb16">
        <span
          >审核列表 <span class="c8c">（待审核{{ unAuditCount }}）</span>
        </span>
      </div>
      <a-table
        :dataSource="resourceList"
        :columns="columns"
        @change="handleTabSort2"
        :pagination="false"
        row-key="eduResourceId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'name'">
            <span
              class="cursor c07"
              @click="handleOpenExamineDetail(record.eduResourceId)"
            >
              {{ record.name }}
            </span>
          </template>
          <template v-if="column.dataIndex == 'resourceType'">
            {{ record.resourceType == "1" ? "灵活性" : "规范性" }}
          </template>
          <template v-if="column.dataIndex == 'auditStatus'">
            <div class="flex_ac">
              <span
                class="spot mr8"
                :style="{
                  backgroundColor: handleStatusSift(record.auditStatus).color,
                }"
              ></span>
              {{ handleStatusSift(record.auditStatus).text }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'joinCount'">
            <span style="color: 007aff; cursor: pointer">{{
              record.joinCount
            }}</span>
          </template>
          <template v-if="column.dataIndex == 'operation'">
            <div
              v-if="record.auditStatus == 0"
              class="flex_js c07"
              style="padding-right: 16px"
            >
              <span
                class="cursor"
                @click="
                  examineDetailsId = record.eduResourceId;
                  openCheckModel(record);
                "
              >
                通过
              </span>
              <!-- <OnePass @click="examineDetailsId = record.eduResourceId" @ok="handleAuditOnePass(record)" /> -->
              <span class="cursor" @click="handleAuditOneRejectOpen(record)"
                >拒绝</span
              >
            </div>
          </template>
        </template>
      </a-table>
      <ysPagination
        :pageNo="pageNo"
        :pageSize="pageSize"
        :total="resourceTotal"
        @change="handlePaginationChange"
      />
    </section>
    <ExamineDetails
      detailType="2"
      :visible="examineDetailsVisible"
      :footerBtnS="true"
      :showAlert="true"
      :resourceList="resourceList"
      :id="examineDetailsId"
      @close="handleCloseExamineDetail"
    />
    <RejectReason
      :visible="rejectReasonVisible"
      :needReason="needReason"
      @close="handleAuditOneRejectCancel"
      @define="(reason: string) => handleAuditOneRejectConfirm(reason)"
    />
    <Loading :loading="loading" />
    <checkModel
      detailType="2"
      v-if="showChecked"
      @close="closeCheckModel"
      @sure="sureChecked"
    />
    <a-modal
      :width="448"
      wrapClassName="batch_declare_modal_li"
      v-model:visible="showParentCheck"
      title="请选择下一级审核部门"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="parentCheckBox">
        <div class="parentCheckBox-title">
          <span>下一级审核机构：</span>
          <span>{{ parentOrgName }}</span>
        </div>
        <div class="parentCheckBox-title1">
          <span style="color: red">*</span>
          <span>下一级审核部门：</span>
        </div>
        <div class="chooseParentOption">
          <a-select
            placeholder="请选择"
            style="width: 368px"
            v-model:value="parentValue"
            :options="nextOptions"
          ></a-select>
        </div>
      </div>
    </a-modal>
  </section>
</template>

<script lang="ts" setup>
import checkModel from "@/components/checkModel.vue";
import { ref, onMounted, nextTick } from "vue";
import Loading from "@/components/loading.vue";
import ExamineDetails from "@/components/examineDetails/index.vue";
import RejectReason from "@/components/examineDetails/rejectReason.vue";
import { ysPagination } from "@ys/ui";
import { tableColumns } from "@/assets/types";
import useResourceList from "@/hooks/useResourceList";
import { message } from "ant-design-vue";
import {
  getResourceList,
  auditRecord,
  searchCheckList,
  getcheckDetail,
  applyUser,
  getNextNode,
  handleAudit,
} from "@/request";
const {
  resourceParams,
  resourceList,
  unAuditCount,
  handleStatusSift,
  getResouceUnAuditCount,
} = useResourceList();
onMounted(() => {
  getResouceAuditList(true);
});

/* --------------- data --------------- */
//#region
const showChecked = ref(false);
const columns = ref<Array<tableColumns>>([
  {
    title: "申报项目",
    dataIndex: "name",
    ellipsis: true,
  },
  {
    title: "申报机构",
    dataIndex: "orgName",
    ellipsis: true,
  },
  {
    title: "申报时间",
    dataIndex: "createTime",
    ellipsis: true,
  },

  {
    title: "参与人数",
    dataIndex: "joinCount",
    width: "120px",
  },
  {
    title: "认证学时",
    dataIndex: "classHour",
    width: "120px",
  },
  {
    title: "审批状态",
    dataIndex: "auditStatus",
    width: "120px",
  },
  {
    title: "审批人",
    dataIndex: "auditUser",
    width: "120px",
  },
  {
    title: "审批时间",
    dataIndex: "auditTime",
    width: "212px",
    sorter: true,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: "112px",
  },
]);
const needReason = ref<boolean>(true);
const rowObj = ref<any>({});
const rejectReasonVisible = ref(false);

// 学时2.0
const searchName = ref(null);
const status = ref(null);
const resourceDate = ref([]);
const loading = ref(false);
const applyStatusOptions = [
  { label: "未提交", value: "3" },
  { label: "待审核", value: "0" },
  { label: "已通过", value: "1" },
  { label: "已拒绝", value: "2" },
];
const examineDetailsId = ref<string>("");
const examineDetailsVisible = ref(false);
const pageNo = ref(1);
const pageSize = ref(10);
const resourceTotal = ref(0);
const sortType = ref(2);
const showParentCheck = ref(false);
const nextOrgId = ref(null);
const departmentId = ref(null);
const nextOptions = ref<any>([]);
// 是否有下一级审核
const parentCheck = ref(true);
const parentOrgName = ref<any>(null);
const parentValue = ref(null);
const handleOk = () => {
  if (!parentValue.value) {
    message.error("请选择");
    return;
  } else {
    showChecked.value = true;
  }
};
const handleAuditOneRejectCancel = () => {
  rejectReasonVisible.value = false;
};
const handleAuditOneRejectOpen = (record: any) => {
  console.log(123);
  rowObj.value = record;
  rejectReasonVisible.value = true;
};
const handleCancel = () => {
  parentValue.value = null;
  showParentCheck.value = false;
};
// 分页
const handlePaginationChange = (page: number, pageS: number) => {
  // 清空下勾选项
  pageNo.value = page;
  pageSize.value = pageS;
  getResouceAuditList();
};
const handleCloseExamineDetail = () => {
  examineDetailsVisible.value = false;
  examineDetailsId.value = "";
  getResouceAuditList(true);
};
const handleOpenExamineDetail = (eduResourceId: string) => {
  examineDetailsVisible.value = true;
  examineDetailsId.value = eduResourceId;
};
const handleSearch = () => {
  getResouceAuditList();
};
const handleReSet = () => {
  searchName.value = null;
  resourceDate.value = [];
  status.value = null;
  getResouceAuditList();
};
const handleTabSort2 = (page: any, data: any, order: any) => {
  sortType.value = order.order == "ascend" ? 1 : 2;
  pageNo.value = 1;
  getResouceAuditList();
};
const getResouceAuditList = async (needCount: boolean = false) => {
  try {
    loading.value = true;
    let params = {
      name: searchName.value,
      startTime: resourceDate.value.length > 0 ? resourceDate.value[0] : null,
      endTime: resourceDate.value.length > 0 ? resourceDate.value[1] : null,
      pageIndex: pageNo.value,
      pageSize: pageSize.value,
      status: status.value,
      // sortType: sortType.value,
    };
    const response = await searchCheckList(params);
    loading.value = false;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      resourceTotal.value = Number(res.totalDatas);
      if (res.data) {
        resourceList.value = res.data.map((item: any) => {
          item.createTimestamp = new Date(item.createTime).getTime();
          return item;
        });
      } else {
        resourceList.value = [];
      }
      if (needCount) getResouceUnAuditCount(2);
    }
  } catch (error) {
    message.error(JSON.stringify(error));
  }
};
const onCalendarChange = () => {};
const openCheckModel = async (val: any) => {
  rowObj.value = val;
  const responce = await getNextNode();
  const res: any = responce.data;
  if (res.data.hasNext) {
    nextOptions.value = res.data.departments.map((item: any) => {
      return { ...item, label: item.name, value: item.id };
    });
    parentOrgName.value = res.data.orgName;
    nextOrgId.value = res.data.orgId;
    showParentCheck.value = true;
  } else {
    showChecked.value = true;
  }
};
const closeCheckModel = () => {
  showChecked.value = false;
};
const handleAuditOneRejectConfirm = async (value: string) => {
  let params = {
    auditType: 2,
    id: rowObj.value.eduResourceId,
    remark: value,
  };
  try {
    loading.value = true;
    const response = await handleAudit(params);
    loading.value = false;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      message.success("审核成功");
      handleAuditOneRejectCancel();
      getResouceAuditList(true);
    }
  } catch (error) {
    message.error("审核失败");
  }
};
// 单条审核通过
const handleAuditOnePass = async (record: any) => {
  let params = {
    auditType: 1,
    departmentId: parentValue.value,
    id: record.eduResourceId,
    orgId: nextOrgId.value ? nextOrgId.value : null,
  };
  try {
    loading.value = true;
    const response = await handleAudit(params);
    loading.value = false;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      message.success("审核成功");
      showParentCheck.value = false;
      showChecked.value = false;
      getResouceAuditList(true);
    }
  } catch (error) {
    message.error("审核失败");
  }
};
const sureChecked = () => {
  handleAuditOnePass(rowObj.value);
  // nextTick(() => {
  //   showChecked.value = false;
  // });
};
//#endregion
</script>

<style lang="scss" scoped>
.parentCheckBox {
  padding: 0 24px;
  .parentCheckBox-title {
    margin-bottom: 24px;
  }
  .chooseParentOption {
    margin-top: 8px;
  }
}
.confirm_examine_container {
  height: 100%;
  .header_container {
    width: calc(100% - 48px);
    height: 84px;
    margin: 0 auto;
    padding: 20px 24px;
    background-color: #fff;
    display: grid;
    row-gap: 24px;

    .item {
      display: flex;
      align-items: center;

      & > span {
        padding-right: 8px;
        text-align: right;
        width: 104px;
      }
    }
  }

  @media screen and (min-width: 1280px) {
    .header_container {
      grid-template-columns: 25% 25% 25% 25%;
    }
  }

  @media screen and (max-width: 1280px) {
    .header_container {
      .item1,
      .item2 {
        display: none;
      }

      justify-content: space-between;
      grid-template-columns: 33% 33% 34%;
    }
  }
}

.table_box {
  width: calc(100% - 48px);
  height: calc(100% - 230px);
  margin: 20px auto;
}

::v-deep(.ant-popover-inner-content) {
  .ant-btn {
    height: 24px;
    border-radius: 2px;
  }
}
</style>

<style lang="scss">
.batch_declare_modal_li {
  .ant-modal-body {
    .num_line {
      padding: 9px 12px;
      background-color: #f5f5f5;
    }

    .small_title {
      width: 73px;
      text-align: right;
    }
  }

  .ant-modal-footer {
    padding: 0 30px 24px 0;
    border: none;
  }
}
</style>
