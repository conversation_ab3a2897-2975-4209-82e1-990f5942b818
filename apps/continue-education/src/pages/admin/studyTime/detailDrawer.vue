<template>
  <a-drawer
    v-model:visible="visible"
    title="查看详情"
    :maskClosable="false"
    class="no_translate_X"
    width="1000px"
    @close="emit('close')"
  >
    <section class="box">
      <a-table
        :dataSource="data"
        :columns="columns"
        @change="handleChange"
        :pagination="false"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex == 'resourceType'">
            {{ record.resourceType == 1 ? "灵活性" : "规范性" }}
          </template>
          <template v-if="column.dataIndex == 'status'">
            <div class="flex_ac">
              <span
                class="spot mr8"
                :style="{
                  backgroundColor: handleStatusSift(record.status).color,
                }"
              ></span>
              {{ handleStatusSift(record.status).text }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'studyaddress'">
            <div class="studyaddress" :title="record.studyaddress">
              {{ record.studyaddress }}
            </div>
          </template>
        </template>
      </a-table>
      <ysPagination
        :pageNo="pageNo"
        :pageSize="pageSize"
        :total="total"
        @change="handlePageChange"
      />
    </section>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { tableColumns } from "@/assets/types";
import { ysPagination } from "@ys/ui";
import ImportResult from "@/components/importResult.vue";
import Loading from "@/components/loading.vue";
import { getResourceListHour, HourResourceList } from "@/request";
import dayjs from "dayjs";
const props = defineProps<{
  visible: boolean;
  id: any;
  searchObjList: any;
}>();
watch(
  () => props.visible,
  (bol) => {
    visible.value = bol;
    if (bol) init();
  }
);
const emit = defineEmits(["close"]);

/* --------------- data --------------- */
//#region
const loading = ref<number>(2);
const visible = ref<boolean>(false);
const columns = ref<any>([
  { title: "学习地点", dataIndex: "studyAddress", ellipsis: true },
  { title: "认证时间", dataIndex: "createTime", width: "250px", sorter: true },
  { title: "申报类型", dataIndex: "resourceType", width: "150px" },
  { title: "学时", dataIndex: "classHour", width: "150px" },
  { title: "状态", dataIndex: "status", width: "120px" },
]);
const data = ref<any>([]);
const lineData = ref<any>([]);
const sort = ref<number>(2);
const pageNo = ref<number>(1);
const pageSize = ref<number>(10);
const total = ref<number>(0);
const resultVisible = ref<boolean>(false);
function handleStatusSift(num: number) {
  let obj = {
    color: num == 0 ? "#FFAA00" : num == 1 ? "#17BE6B" : "#F53F3F",
    text: num == 0 ? "待审核" : num == 1 ? "已通过" : "已拒绝",
  };
  return obj;
}

function init() {
  console.log("searchObjList", props.searchObjList);
  loading.value++;
  let params: any = {
    staticUserId: props.id,
    sortType: sort.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
  };
  params.name = props.searchObjList ? props.searchObjList.name : null;
  params.type = props.searchObjList ? props.searchObjList.type : null;
  params.startTime = null;
  params.endTime = null;
  params.relationOrgid = props.searchObjList
    ? props.searchObjList.orgValue
    : null;
  if (props.searchObjList && props.searchObjList.resourceDate) {
    if (
      !!props.searchObjList.resourceDate &&
      props.searchObjList.resourceDate.length
    ) {
      params.startTime = props.searchObjList
        ? props.searchObjList.resourceDate[0]
        : null;
      params.endTime = props.searchObjList
        ? props.searchObjList.resourceDate[1]
        : null;
    }
  }
  params.status = props.searchObjList ? props.searchObjList.status : null;
  HourResourceList(params).then((res: any) => {
    loading.value++;
    if (res.data.code == 0) {
      data.value = res.data.data.map((v: any) => {
        v.createTime = dayjs(v.createTime).format("YYYY-MM-DD HH:mm");
        return v;
      });
      for (let i of data.value) {
        for (let j in i) {
          if (i[j] == null || i[j] == "") {
            i[j] = "--";
          }
        }
      }
      total.value = Number(res.data.totalDatas);
    }
  });
}

function handlePageChange(no: number, size: number) {
  pageNo.value = no;
  pageSize.value = size;
  init();
}
function handleChange(column: any, page: any, order: any) {
  sort.value = order.order == "ascend" ? 1 : order.order == "descend" ? 2 : 0;
  init();
}
function handleRecord(item: any) {
  lineData.value = [item];
  resultVisible.value = true;
}
//#endregion
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 24px;
}
</style>
<style lang="scss">
.no_translate_X {
  transform: translateX(0) !important;
}
</style>
