<template>
  <section class="study_time_container" ref="study_time_container">
    <div class="model_title p1624">
      <span>学时管理</span>
    </div>
    <SearchHeader searchType="1" :addorg="true" class="basic_header_search_" @search="(obj: any) => searchObj(obj)"
      @reSet="resetObj" />
    <!-- @search="(obj: any) => newSiftSearchForHours(obj)" @reSet="handleResourceHourReset" /> -->
    <section class="table_box bcf br4 p1624 scrollbars">
      <div class="flex_js mb16">
        <div class="left">
          <span>学时列表</span>
          <span class="left_right" v-if="isHavePermission">
            <ysIcon style="font-size: 20px" type="iconqiehuan2" />
            <span style="margin-left: 8px;" @click="handleChangeTab">{{ tabName }}</span>
          </span>
        </div>

        <span class="tab_btns">
          <a-button v-if="tabId == 1" :disabled="!!!selectedRowKeys.length" @click="handleDel(0, '')">删除</a-button>
          <a-button v-if="tabId == 1" @click="importVisible = true">导入</a-button>
          <a-button @click="handleExportType">导出筛选结果</a-button>
          <!-- <a-button @click="handleExport(1)">导出筛选结果</a-button> -->
          <a-button v-if="tabId == 1" :disabled="!!!selectedRowKeys.length" @click='sureZhuanhua'>学时转换</a-button>
          <a-button @click="changeVisible = true" type="primary">学时规则配置</a-button>
        </span>
      </div>
      <a-table v-if="tabId == 1" :dataSource="resourceListHour" :columns="columns" @change="handleTabSort"
        :rowSelection="{ selectedRowKeys, onChange: onSelectRowChange1, onSelect: onChooseChange1, onSelectAll: onChooseAllChange1 }"
        :pagination="false" row-key="eduResourceId">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'resourceName'">
            <span class="cursor c07" @click="handleOpenExamineDetail(record.eduResourceId)">
              {{ record.resourceName }}
            </span>
          </template>
          <template v-if="column.dataIndex == 'resourceType'">
            {{ record.resourceType == 1 ? '灵活性' : '规范性' }}
          </template>
          <template v-if="column.dataIndex == 'status'">
            <div class="flex_ac">
              <span class="spot mr8" :style="{ backgroundColor: handleStatusSift(record.status).color }"></span>
              {{ handleStatusSift(record.status).text }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'studyaddress'">
            <div class='studyaddress' :title="record.studyaddress">
              {{ record.studyaddress }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'operation'">
            <div class="flex_js c07" style="padding-right:16px">
              <a-popconfirm title="是否确认删除？" ok-text="确定" cancel-text="取消"
                :getPopupContainer="() => $refs.study_time_container" @confirm="handleDel(1, record.eduResourceId)">
                <span class="cursor" v-if="record.status != 1">删除</span>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </a-table>
      <a-table v-if="tabId == 2" :dataSource="dataList1" :columns="columns1" :pagination="false"
        row-key="eduResourceId">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'operation'">
            <span style="color:#007aff;cursor: pointer;" @click="openDetailsDrawer(record)">查看详情</span>
          </template>
        </template>
      </a-table>
      <ysPagination v-if="tabId == 1" :pageNo="resourceParams.pageNo" :pageSize="resourceParams.pageSize"
        :total="resourceHourTotal" @change="handlePaginationChange2" />
      <ysPagination v-if="tabId == 2" :pageNo="resourceParams1.pageNo" :pageSize="resourceParams1.pageSize"
        :total="resourceHourTotal1" @change="handlePaginationChange21" />
    </section>
    <ExamineDetails :record="true" :showAlert="examineDhowAlert" :visible="examineDetailsVisible" :id="examineDetailsId"
      :resourceList="resourceListHour" @close="handleCloseExamineDetail"
      @newStatus="(newData: any) => handleOneAuditNewStatus(newData.id, newData.status)" />
    <TimeChangeScore :visible="changeVisible" :num="selectedRowKeys.length" @close="changeVisible = false" />
    <ImportModel :visible="importVisible" :type="2" @lookLog="importVisible = false; logVisible = true"
      @close="importVisible = false" />
    <ImportLog :visible="logVisible" type="1" :id="examineDetailsId"
      @close="importVisible = true; logVisible = false" />
    <Loading :loading="loading" />
    <detailDrawer :visible="detailsVisible" :id="detailsId" @close="detailsVisible = false"
      :searchObjList="searchObjList" />
  </section>
</template>

<script lang='ts' setup>
import detailDrawer from './detailDrawer.vue'
import { ref, onMounted, nextTick } from 'vue'
import Loading from '@/components/loading.vue'
import SearchHeader from '@/components/searchHeader.vue'
import ExamineDetails from '@/components/examineDetails/index.vue'
import TimeChangeScore from '@/components/timeChangeScore.vue'
import ImportModel from '@/components/impoetModal.vue'
import ImportLog from '@/components/importLog.vue'
import { ysPagination, ysIcon } from "@ys/ui"
import { tableColumns } from '@/assets/types'
import { useRouter } from 'vue-router'
import { convertHourScore } from '@/request'
import useResourceList from '@/hooks/useResourceList'
import useExamineDetails from '@/hooks/useExamineDetails'
import { message } from 'ant-design-vue'
import { getHourStaticsResourceList, getConfig, doUserLevel, exportResource } from '@/request'
const {
  loading,
  resourceParams,
  resourceHourTotal,
  resourceListHour,
  selectedRowKeys,
  onSelectRowChange1,
  onChooseChange1,
  onChooseAllChange1,
  handleStatusSift,
  handleTabSort,
  getResouceAuditList,
  handlePaginationChange2,
  handleResourceHourReset,
  newSiftSearchForHours,
  handleExport,
  handleOneAuditNewStatus,
  handleDeleteProject2,
  getHourResourceList
} = useResourceList()
const router = useRouter()

const {
  examineDetailsVisible,
  examineDetailsId,
  examineDhowAlert,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();


onMounted(() => {
  getHourResourceList()
  handleGetSystem()
})
/* --------------- data --------------- */
//#region
const columns = ref<Array<tableColumns>>([
  {
    title: '申报项目',
    dataIndex: 'resourceName',
    ellipsis: true
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    width: '134px',
    ellipsis: true
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: '180px',
    ellipsis: true
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
    ellipsis: true
  },
  {
    title: '学习地点',
    dataIndex: 'studyAddress',
    ellipsis: true
  },
  {
    title: '认证时间',
    dataIndex: 'createTime',
    width: '180px',
    ellipsis: true,
    sorter: true,
  },
  {
    title: '申报类型',
    dataIndex: 'resourceType',
    width: '104px'
  },
  {
    title: '学时',
    dataIndex: 'classHour',
    width: '96px'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '114px'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '74px'
  },
])
const columns1 = ref<any>([
  {
    title: '姓名',
    dataIndex: 'userName',
    width: '134px',
    ellipsis: true
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: '180px',
    ellipsis: true
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
    ellipsis: true
  },
  {
    title: '规范性学时',
    dataIndex: 'gfHour',
    ellipsis: true
  },
  {
    title: '灵活性学时',
    dataIndex: 'lhHour',
    ellipsis: true
  }, ,
  {
    title: '总学时',
    dataIndex: 'totalHour',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '120px'
  },
])
const detailsVisible = ref(false)
const detailsId = ref(null)
const dataList1 = ref<any>([])
const tabName = ref<any>('学时明细')
const tabId = ref<any>(1)
const orgName = ref<any>('')
const resourceParams1 = ref<any>({
  name: null,
  startTime: null,
  endTime: null,
  month: null,
  type: null,
  pageNo: 1,
  pageSize: 10,
  status: null,
  sortType: 2,
  queryType: null,
  userId: null,
  dataSource: null,
  sourceType: null,
  relationOrgid: null,
})
const resourceHourTotal1 = ref(0);
const changeVisible = ref<boolean>(false)
// const openstudyModel=ref<boolean>(false)
const importVisible = ref<boolean>(false)
const logVisible = ref<boolean>(false)
const resourceDate = ref<any[]>([]);
const isHavePermission = ref(false)
const searchObjList = ref<any>(null)
const searchType = ref(1)
const openDetailsDrawer = (record: any) => {
  detailsVisible.value = true
  detailsId.value = record.userId || null
}
const handleGetSystem = async () => {
  let result = await getConfig() as any
  let result1 = await doUserLevel() as any;
  if (result1.data.data.currentUserPermise != 8) {
    isHavePermission.value = true
  }
  else {
    if (result.data.data.allowLook) {
      isHavePermission.value = true
    }
    else {
      isHavePermission.value = false
    }
  }
  if (isHavePermission.value == false) {
    tabId.value = 2
    getHourResourceList1()
  }
}
const searchObj = (obj: any) => {
  searchType.value = 2
  searchObjList.value = obj
  if (tabId.value == 1) {
    newSiftSearchForHours(obj)
  }
  else {
    orgName.value = obj.orgValue
    resourceParams1.value.name = obj.name;
    resourceParams1.value.type = obj.type;
    resourceParams1.value.startTime = null;
    resourceParams1.value.endTime = null;
    resourceParams1.value.relationOrgid = obj.orgValue
    if (!!obj.resourceDate && obj.resourceDate.length) {
      resourceParams1.value.startTime = obj.resourceDate[0];
      resourceParams1.value.endTime = obj.resourceDate[1];
    }
    resourceParams1.value.status = obj.status;
    nextTick(() => {
      getHourResourceList1()
    })

  }
}
const resetObj = () => {
  searchType.value = 1
  searchObjList.value = null
  if (tabId.value == 1) {
    handleResourceHourReset()
  }
  else {
    orgName.value = null
    resourceDate.value = [];
    resourceParams1.value.pageNo = 1;
    resourceParams1.value.pageSize = 10;
    resourceParams1.value.name = "";
    resourceParams1.value.startTime = null;
    resourceParams1.value.endTime = null;
    resourceParams1.value.type = null;
    resourceParams1.value.status = null;
    resourceParams1.value.sortType = 2;
    resourceParams1.value.relationOrgid = null;
    getHourResourceList1();
  }
}
// 获取学时统计数据
const getHourResourceList1 = () => {
  let params = { ...resourceParams1.value }
  getHourStaticsResourceList(params).then((res: any) => {
    if (res.data.code == 0) {

      dataList1.value = res.data.data;
      resourceHourTotal1.value = Number(res.data.totalDatas);
    }
  })
}
// 分页
const handlePaginationChange21 = (page: number, pageSize: number) => {
  resourceParams1.value.pageNo = page;
  resourceParams1.value.pageSize = pageSize;
  getHourResourceList1();
};
const handleExportType = async () => {
  if (tabId.value == 1) {
    handleExport(1)
  }
  else {
    let name = "";
    let res: any;
    name = "学时列表.xlsx";
    let obj: any = { ...resourceParams1.value }
    obj.relationOrgid = orgName.value
    res = await exportResource(obj);
    let blob = new Blob([res.data], { type: "application/vnd.ms-execl" });
    let link = window.URL.createObjectURL(blob);
    let a = document.createElement("a");
    document.body.appendChild(a);
    a.download = name;
    a.href = link;
    a.click();
    window.URL.revokeObjectURL(link);
    document.body.removeChild(a);
  }
}
// 切换tab
const handleChangeTab = () => {
  if (tabId.value == 1) {
    tabId.value = 2
    tabName.value = '学时统计'
  } else {
    tabId.value = 1
    tabName.value = '学时明细'
  }

  if (searchType.value == 2) {
    searchObj(searchObjList.value)
  }
  else {
    resetObj()
  }

}
/* 确定转化 */
const sureZhuanhua = () => {
  let params = {
    ids: selectedRowKeys.value,
  }
  convertHourScore(params).then((res: any) => {
    if (res.data.code == 0) {
      message.success(res.data.msg)
    }
  })
}
// function handleDefineChange(arr: Array<number>) {
//   let params = {
//     ids: selectedRowKeys.value,
//     classHour: arr[0],
//     classScore: arr[1],
//   }
//   convertHourScore(params).then((res: any) => {
//     if (res.data.code == 0) {
//       message.success('已成功设置转化比例')
//       changeVisible.value = false
//     }
//   })
// }
/* 编辑申报 */
function handleEdit(item: any) {
  router.push(`/admin/declareSearch/examineEdit/${item.eduResourceId}?containerType=2`)
}
/* 删除 */
function handleDel(num: number, id: string) {
  if (!!!num) {
    handleDeleteProject2(selectedRowKeys.value)
  } else {
    examineDetailsId.value = id
    handleDeleteProject2([examineDetailsId.value])
  }
}

//#endregion


</script>

<style lang='scss' scoped>
.study_time_container {
  height: 100%;
}

.table_box {
  width: calc(100% - 48px);
  height: calc(100% - 230px);
  margin: 0 auto;

  .left {
    display: flex;
    align-items: center;

    .left_right {
      display: flex;
      align-items: center;
      margin-left: 8px;
      color: #007aff;
      cursor: pointer;
    }
  }

  .tab_btns {
    .ant-btn {
      margin-left: 8px;
    }
  }
}

::v-deep(.ant-popover-inner-content) {
  .ant-btn {
    height: 24px;
    border-radius: 2px;
  }
}
</style>