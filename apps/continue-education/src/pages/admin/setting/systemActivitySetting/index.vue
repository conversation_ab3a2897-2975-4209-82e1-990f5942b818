<template>
  <div class="activitySetting">
    <h4>活动学时设置</h4>
    <div class="container scrollbar">
      <div class="header">
        <div class="title">分类申报：</div>
        <a-switch
          v-model:checked="classification"
          @change="classificationChange"
        />
        <div class="remark">
          开启后，教师仅可为此处设置的分类申报学时。类型层级一旦确定将无法修改，请谨慎操作。
        </div>
      </div>
      <div class="content">
        <div class="leftTree">
          <div class="searchInfo">
            <a-input-search
              v-model:value="searchValue"
              placeholder="请输入类型名称"
              style="width: 240px"
              @search="onSearch"
            />
          </div>
          <div class="treeInfo">
            <a-tree
              :tree-data="filteredTreeData"
              v-model:selectedKeys="selectedKeys"
              @select="onSelect"
              :field-names="{
                children: 'chirldrenList',
                title: 'name',
                key: 'id',
              }"
            >
            </a-tree>
            <div class="showStop">
              显示停用
              <a-switch v-model:checked="showStop" @change="changeShowStop" />
            </div>
          </div>
        </div>
        <div class="rightSetting">
          <div class="rightTitle">
            <div>节点信息</div>

            <div class="btn" v-if="!edit">
              <a-button @click="delItem" class="aBtn" v-if="!isTop"
                >删除</a-button
              >
              <a-button @click="stopItem" class="aBtn" v-if="!isTop">{{
                treeItemValue &&
                treeItemValue.status &&
                treeItemValue.status == 1
                  ? "启用"
                  : "停用"
              }}</a-button>
              <a-button @click="editItem" class="aBtn" v-if="!isTop"
                >编辑</a-button
              >
              <a-button
                type="primary"
                v-if="nodeLevel < 6"
                @click="addChildren"
                class="aBtn"
                >添加下级</a-button
              >
            </div>
          </div>
          <div>
            <div class="info" v-if="!edit">
              <div class="item">
                <div class="label">名称：</div>
                <div class="labelValue">{{ treeItemValue.name }}</div>
              </div>
              <div class="item">
                <div class="label">节点类型：</div>
                <div class="labelValue">
                  {{ treeItemValue.type == 1 ? "分组类型" : "活动类型" }}
                </div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">每年度认证学时上限：</div>
                <div class="labelValue">{{ treeItemValue.hourLimit }}</div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">学时类型：</div>
                <div class="labelValue">
                  {{ treeItemValue.resourceType == "1" ? "灵活性" : "规范性" }}
                </div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">可认证学时：</div>
                <div class="labelValue">
                  {{ treeItemValue.hour }}
                </div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">对应培训等级：</div>
                <div class="labelValue">
                  {{ getTrian(treeItemValue.level) }}
                </div>
              </div>
            </div>
            <div class="info" v-else>
              <div class="item">
                <div class="label">
                  <span class="star">*</span>
                  <span>名称：</span>
                </div>
                <div class="labelValue">
                  <a-input
                    v-model:value="treeItemValue.name"
                    placeholder="请输入名称"
                    show-count
                    :maxlength="20"
                    style="width: 320px"
                  />
                </div>
              </div>
              <div class="item">
                <div class="label">节点类型：</div>
                <div class="labelValue">
                  <a-radio-group
                    :disabled="edit"
                    @change="nodeTypeChange"
                    :options="nodeTypeOptions"
                    v-model:value="treeItemValue.type"
                  />
                </div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">每年度认证学时上限：</div>
                <div class="labelValue">
                  <a-input-number
                    style="width: 320px"
                    id="inputNumber"
                    v-model:value="treeItemValue.hourLimit"
                    :min="1"
                    :max="999"
                    placeholder="请输入"
                    :step="0"
                    :precision="0"
                  />
                </div>
              </div>
              <div class="item1" v-if="treeItemValue.type == '2'">
                <div class="label"></div>
                <div class="remark">若未设置上线，视为无上限</div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">学时类型：</div>
                <div class="labelValue">
                  <a-radio-group
                    :options="scoreTypeOptions"
                    v-model:value="treeItemValue.resourceType"
                  />
                </div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">可认证学时：</div>
                <div class="labelValue">
                  <a-input-number
                    style="width: 320px"
                    id="inputNumber"
                    v-model:value="treeItemValue.hour"
                    :min="1"
                    :max="999"
                    placeholder="请输入"
                    :step="0"
                    :precision="0"
                  />
                </div>
              </div>
              <div class="item1" v-if="treeItemValue.type == '2'">
                <div class="label"></div>
                <div class="remark">若未设置学时，则由教师申报时自行填写</div>
              </div>
              <div class="item" v-if="treeItemValue.type == '2'">
                <div class="label">对应培训等级：</div>
                <div class="labelValue">
                  <a-select
                    class="input"
                    ref="select"
                    placeholder="请选择"
                    v-model:value="treeItemValue.level"
                    style="width: 320px"
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in tringLevelList"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </div>
              </div>
              <div class="item1" v-if="treeItemValue.type == '2'">
                <div class="label"></div>
                <div class="remark">若未设置等级，则由教师申报时自行选择</div>
              </div>
              <div class="item">
                <div class="label"></div>
                <div>
                  <a-button type="primary" @click="handleSubmit">保存</a-button>
                  <a-button @click="cancalEdit" style="margin-left: 12px"
                    >取消</a-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <delBox
    v-if="showDel"
    :name="treeItemValue.name"
    @close="showDel = false"
    @sureDel="sureDel"
  />
  <a-modal
    centered
    v-model:visible="showAddChildren"
    wrapClassName="addChildrenModal"
    title="添加下级"
    @ok="handleOk"
    @cancel="handleCancel"
    width="500px"
  >
    <div class="item">
      <div class="label">
        <span class="star">*</span>
        <span>名称：</span>
      </div>
      <div class="labelValue">
        <a-input
          v-model:value="addItemValue.name"
          placeholder="请输入名称"
          show-count
          :maxlength="20"
          style="width: 272px"
        />
      </div>
    </div>
    <div class="item" v-if="addItemValue.nodeType == '2'">
      <div class="label">每年度认证学时上限：</div>
      <div class="labelValue">
        <a-input-number
          style="width: 272px"
          id="inputNumber"
          v-model:value="addItemValue.scoreLimit"
          :min="1"
          :max="999"
          placeholder="请输入"
          :step="0"
          :precision="0"
        />
      </div>
    </div>
    <div class="item1" v-if="addItemValue.nodeType == '2'">
      <div class="label"></div>
      <div class="remark">若未设置上线，视为无上限</div>
    </div>
    <div class="item">
      <div class="label">节点类型：</div>
      <div class="labelValue">
        <a-radio-group
          :options="nodeTypeOptions"
          v-model:value="addItemValue.nodeType"
        />
      </div>
    </div>

    <div class="item" v-if="addItemValue.nodeType == '2'">
      <div class="label">学时类型：</div>
      <div class="labelValue">
        <a-radio-group
          :options="scoreTypeOptions"
          v-model:value="addItemValue.scoreType"
        />
      </div>
    </div>
    <div class="item" v-if="addItemValue.nodeType == '2'">
      <div class="label">可认证学时：</div>
      <div class="labelValue">
        <a-input-number
          style="width: 272px"
          id="inputNumber"
          v-model:value="addItemValue.allowApproveScore"
          :min="1"
          :max="999"
          placeholder="请输入"
          :step="0"
          :precision="0"
        />
      </div>
    </div>
    <div class="item1" v-if="addItemValue.nodeType == '2'">
      <div class="label"></div>
      <div class="remark">若未设置学时，则由教师申报时自行填写</div>
    </div>
    <div class="item" v-if="addItemValue.nodeType == '2'">
      <div class="label">对应培训等级：</div>
      <div class="labelValue">
        <a-select
          class="input"
          ref="select"
          placeholder="请选择"
          v-model:value="addItemValue.trainLevel"
          style="width: 272px"
        >
          <a-select-option :value="item.value" v-for="item in tringLevelList">{{
            item.label
          }}</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="item2" v-if="addItemValue.nodeType == '2'">
      <div class="label"></div>
      <div class="remark">若未设置等级，则由教师申报时自行选择</div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import delBox from "./delBox.vue";
import {
  ref,
  computed,
  onMounted,
  createVNode,
  defineComponent,
  reactive,
  nextTick,
} from "vue";
import { message } from "ant-design-vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { Modal } from "ant-design-vue";
import {
  editConfig,
  getConfig,
  hanlleGetTree,
  enableNode,
  removeNode,
  saveClassification,
} from "@/request";
const classification = ref(false);
const searchValue = ref("");
const selectedKeys = ref<any>([]);
const showStop = ref(false);

const treeData = ref<any>([]);
const filteredTreeData = ref<any[]>([]);
const edit = ref(false);
const showAddChildren = ref(false);
const isTop = ref(true);
const treeItemValue = ref<any>({});
const treeItemCopyValue = ref<any>({});
const editId = ref(null);
const addItemValue = ref<any>({
  name: "",
  nodeType: 1,
  scoreLimit: null,
  scoreType: 2,
  allowApproveScore: null,
  trainLevel: null,
});
const nodeTypeOptions = [
  { label: "分组", value: 1 },
  { label: "活动类型", value: 2 },
];
const scoreTypeOptions = [
  { label: "规范性", value: 2 },
  { label: "灵活性", value: 1 },
];
const tringLevelList = ref([
  { value: 1, label: "国家级" },
  { value: 2, label: "省级" },
  { value: 3, label: "市级" },
  { value: 4, label: "县级" },
  { value: 5, label: "校级" },
  { value: 6, label: "其他" },
]);
const editTreeId = ref(null);
const showDel = ref(false);
const nodeLevel = ref(1);
function getTrian(val: any) {
  let str = "";
  str =
    val == 1
      ? "国家级"
      : val == 2
        ? "省级"
        : val == 3
          ? "市级"
          : val == 4
            ? "县级"
            : val == 5
              ? "校级"
              : val == 6
                ? "其他"
                : "";
  return str;
}
const handleCancel = () => {
  addItemValue.value.name = "";
  addItemValue.value.nodeType = 1;
  addItemValue.value.scoreLimit = null;
  addItemValue.value.scoreType = 1;
  addItemValue.value.allowApproveScore = null;
  addItemValue.value.trainLevel = null;
  showAddChildren.value = false;
};
const handleOk = () => {
  if (!addItemValue.value.name) {
    message.error("请输入名称");
    return;
  }
  let params = {
    hour:
      addItemValue.value.nodeType == 2
        ? addItemValue.value.allowApproveScore
        : null,
    hourLimit:
      addItemValue.value.nodeType == 2 ? addItemValue.value.scoreLimit : null,
    level:
      addItemValue.value.nodeType == 2 ? addItemValue.value.trainLevel : null,
    name: addItemValue.value.name,
    parentId: selectedKeys.value[0],
    resourceType:
      addItemValue.value.nodeType == 2 ? addItemValue.value.scoreType : null,
    type: addItemValue.value.nodeType,
    id: edit.value ? editTreeId.value : null,
  };
  saveClassification(params).then((res: any) => {
    if (res.data.code == 0) {
      message.success("新增成功");
      showAddChildren.value = false;
      hanledeGetTreeList();
      handleCancel();
    }
  });
};
const changeShowStop = () => {
  hanledeGetTreeList();
};
const hanledeGetTreeList = async () => {
  let params = {
    status: showStop.value == true ? null : 0,
  };
  const responce = await hanlleGetTree(params);
  const res: any = responce.data;
  treeData.value = res.data;
  nextTick(() => {
    addLevelToTree(treeData.value);
    filteredTreeData.value = treeData.value;

    selectedKeys.value = [res.data[0].id];
    treeItemValue.value = res.data[0];

    // console.log("res.data", res.data);
    // console.log("selectedKeys.value", selectedKeys.value);
    // console.log("treeItemValue.value", treeItemValue.value);
    treeItemCopyValue.value = res.data[0];
  });
};
const addLevelToTree = (tree: any, level = 1) => {
  tree.forEach((node: any) => {
    node.levelType = level;
    if (node.chirldrenList && node.chirldrenList.length > 0) {
      addLevelToTree(node.chirldrenList, level + 1);
    }
  });
};
const onSelect = (selectedKey: string[], e: any) => {
  selectedKeys.value = [e.node.id];
  edit.value = false;
  editId.value = e.node.id;
  nodeLevel.value = e.node.levelType;
  treeItemValue.value = e.node;
  // console.log(treeItemValue.value.status);
  treeItemCopyValue.value = e.node;
  isTop.value = e.node.parentId == "0" ? true : false;
  const key = e.node.eventKey;
  const index = expandedKeys.value.indexOf(key);

  if (index === -1) {
    expandedKeys.value.push(key);
  } else {
    expandedKeys.value.splice(index, 1);
  }
};
const nodeTypeChange = () => {
  if (treeItemValue.value.type == 1) {
    treeItemValue.value.hour = null;
    treeItemValue.value.level = null;
    treeItemValue.value.resourceType = null;
  }
};
const handleSubmit = () => {
  console.log("treeItemValue.value", treeItemValue.value);
  if (!treeItemValue.value.name) {
    message.error("请输入名称");
    return;
  }
  console.log("treeItemValue.value", treeItemValue.value);
  let params = {
    hour: treeItemValue.value.type == 2 ? treeItemValue.value.hour : null,
    hourLimit:
      treeItemValue.value.type == 2 ? treeItemValue.value.hourLimit : null,
    level: treeItemValue.value.type == 2 ? treeItemValue.value.level : null,
    name: treeItemValue.value.name,
    parentId: treeItemValue.value.parentId,
    resourceType:
      treeItemValue.value.type == 2 ? treeItemValue.value.resourceType : null,
    type: treeItemValue.value.type,
    id: treeItemValue.value.id,
  };
  console.log(params);
  saveClassification(params).then((res: any) => {
    if (res.data.code == 0) {
      message.success("编辑成功");
      edit.value = false;
      hanledeGetTreeList();
    }
  });
};
const cancalEdit = () => {
  treeItemValue.value = treeItemCopyValue.value;
  edit.value = false;
};
// 确认删除
const sureDel = () => {
  removeNode({ id: treeItemValue.value.id }).then((res: any) => {
    if (res.data.code == 0) {
      message.success("删除成功");
      showDel.value = false;
      hanledeGetTreeList();
    }
  });
};
// 删除
const delItem = () => {
  showDel.value = true;
};
// 停用
const stopItem = () => {
  Modal.confirm({
    centered: true,
    title: `是否确认${treeItemValue.value.status == 0 ? "停用" : "启用"}?`,
    icon: createVNode(ExclamationCircleOutlined),
    content:
      treeItemValue.value.status == 0
        ? "停用后教师将无法申报此类型及下级类型的活动"
        : null,
    onOk() {
      console.log("OK");
      console.log("treeItemValue.value", treeItemValue.value);
      enableNode({
        id: treeItemValue.value.id,
        status: treeItemValue.value.status == 0 ? 1 : 0,
      }).then((res: any) => {
        if (res.data.code == 0) {
          let txt = treeItemValue.value.status == 0 ? "停用成功" : "启用成功";
          message.success(`${txt}`);
          treeItemValue.value.status = treeItemValue.value.status == 0 ? 1 : 0;
          hanledeGetTreeList();
        }
      });
    },
    onCancel() {
      console.log("Cancel");
    },
  });
};
// 编辑
const editItem = () => {
  edit.value = true;
};
// 添加下级
const addChildren = () => {
  console.log("treeItemValue.value", treeItemValue.value);
  if (treeItemValue.value.type != 1) {
    message.warning("仅分组类型可添加下级");
    return;
  }
  if (nodeLevel.value == 6) {
    message.warning("最多添加六级");
    return;
  }
  showAddChildren.value = true;
};
const onSearch = () => {
  if (!searchValue.value) {
    // 如果没由输入内容，显示全部数据
    filteredTreeData.value = treeData.value;
    return;
  }

  const filterNodes = (nodes: any[], keyword: string) => {
    const matched: any[] = [];
    nodes.forEach((node) => {
      const hasChildren = node.chirldrenList?.length > 0;
      const isMatched = node.name.includes(keyword);

      if (isMatched) {
        matched.push(node);
      } else if (hasChildren) {
        const filteredChildren = filterNodes(node.chirldrenList, keyword);
        if (filteredChildren.length > 0) {
          matched.push({ ...node, chirldrenList: filteredChildren });
        }
      }
    });
    return matched;
  };

  filteredTreeData.value = filterNodes(treeData.value, searchValue.value);
};
const getAllKeys = (nodes: any[]): string[] => {
  let keys: string[] = [];
  nodes.forEach((node) => {
    keys.push(node.key);
    if (node.chirldrenList) {
      keys = keys.concat(getAllKeys(node.chirldrenList));
    }
  });
  return keys;
};
const expandedKeys = ref<string[]>([]);

const classificationChange = () => {
  editConfig({
    id: editId.value,
    classifyApply: classification.value,
  }).then((res: any) => {
    if (res.data.code == 0) {
      setTimeout(() => {
        message.success("修改成功");
        init();
      }, 1000);
    }
  });
};
const init = () => {
  getConfig().then((res: any) => {
    if (res.data.code == 0) {
      editId.value = res.data.data.id;
      classification.value = res.data.data.classifyApply;
    }
  });
};
onMounted(() => {
  init();
  hanledeGetTreeList();
});
</script>

<style lang="scss" scoped>
.activitySetting {
  margin: 16px 24px 0px 24px;
  .container {
    // margin-top: 24px;
    background: #ffffff;
    border-radius: 4px;
    max-height: 790px;
    overflow: auto;
    padding: 24px;
    overflow: auto;
    .header {
      display: flex;
      align-items: center;
      .title {
        font-size: 16px;
      }
      .remark {
        margin-left: 24px;
      }
    }
    .content {
      margin-top: 24px;
      display: flex;
      //   align-items: center;
      height: 671px;
      .leftTree {
        width: 400px;
        border-right: 1px solid #e5e5e5;
        .treeInfo {
          margin-top: 16px;
          .showStop {
            margin-top: 16px;
          }
        }
      }
      .rightSetting {
        flex: 1;
        padding: 0 24px;
        .rightTitle {
          font-weight: bold;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .btn {
            .aBtn {
              margin-left: 8px;
            }
          }
        }
        .empty {
          height: calc(100% - 24px);
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
        }
        .info {
          .item {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            .label {
              width: 140px;
              text-align: right;
              .star {
                color: red;
              }
            }
          }
          .item1 {
            display: flex;
            align-items: center;
            margin-top: -20px;
            margin-bottom: 24px;
            .label {
              width: 140px;
              text-align: right;
              .star {
                color: red;
              }
            }
            .remark {
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
}
.addChildrenModal {
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .label {
      width: 140px;
      text-align: right;
      .star {
        color: red;
      }
    }
  }
  .item1 {
    display: flex;
    align-items: center;
    margin-top: -20px;
    margin-bottom: 24px;
    .label {
      width: 140px;
      text-align: right;
      .star {
        color: red;
      }
    }
    .remark {
      color: #8c8c8c;
    }
  }
  .item2 {
    display: flex;
    align-items: center;
    margin-top: -20px;
    .label {
      width: 140px;
      text-align: right;
      .star {
        color: red;
      }
    }
    .remark {
      color: #8c8c8c;
    }
  }
}
</style>
