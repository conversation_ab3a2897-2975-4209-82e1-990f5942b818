<template>
  <div class="ruleSetting">
    <div class="nav">
      <div
        class="navItem"
        :class="[navActive == item.id ? 'chooseNav' : '']"
        @click="changeNav(item.id)"
        v-for="(item, index) in navList"
        :key="index"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="content">
      <div class="yearCertificate" v-if="navActive == 1">
        <div class="left">
          <!-- <div class="head">
            <span>证书规则</span>
          </div> -->
          <div class="createBtn" @click="createRule(1)">
            <div>
              <span>+</span>
              <span style="margin-left: 10px">新建</span>
            </div>
          </div>
          <div class="certificateRuleList scrollbar">
            <div class="isList" v-if="ceretificateList.length > 0">
              <div
                @click="createRule(2, item)"
                class="listItem"
                :class="[activeRule == item.eduCertifiRuleId ? 'blueList' : '']"
                v-for="(item, index) in ceretificateList"
                :key="index"
              >
                {{ item.name }}学年{{ item.isNow == 1 ? "(当前学年)" : "" }}
              </div>
            </div>
            <div v-else class="nolist">
              <img src="@/assets/image/npCertificate.png" alt="" />
            </div>
          </div>
        </div>
        <div class="right scrollbar" v-if="showCreateOrEditRule">
          <div class="head">
            {{ setType == 1 ? "新建" : "编辑-"
            }}{{ setType != 1 ? setTypeName : "" }}
          </div>
          <div class="formContent">
            <!-- <div class="item1">
                  <div class="label">
                    <span>证书发放：</span>
                  </div>
                  <a-switch v-model:checked="certificateChecked" />
                </div> -->
            <div class="item1">
              <div class="label">
                <i>*</i>
                <span> 证书ID：</span>
              </div>
              <a-input
                placeholder="请输入"
                @input="filterNumberInput"
                v-model:value="certificateId"
                style="width: 288px"
              />
              <!-- <div class="previewCertificate" @click="showCertificate">预览</div> -->
              <div class="maskInputTitle">请输入证书管理里对接颁发证书的ID</div>
            </div>
            <div class="item" style="margin-top: 36px">
              <div class="label">
                <i>*</i>
                <span>证书落款：</span>
              </div>
              <a-input
                style="width: 288px"
                v-model:value="signature"
                placeholder="请输入"
                maxlength="20"
              />
            </div>
            <div class="item">
              <div class="label">
                <i>*</i>
                <span>学年度：</span>
              </div>
              <a-select
                style="width: 288px"
                ref="select"
                placeholder="请选择"
                v-model:value="eduSystemSetId"
              >
                <a-select-option
                  :value="item.eduSystemSetId"
                  v-for="item in options1"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </div>
            <div class="item">
              <div class="label">
                <i>*</i>
                <span>发放条件：</span>
              </div>
              <a-radio-group
                :options="conditionsOptions"
                v-model:value="conditions"
              />
            </div>
            <div class="item3" v-if="conditions == '1'">
              <div class="label"></div>
              <div class="requirement">
                <div>要求：</div>
                <div v-for="item in requireList" :key="item.id">
                  <span>{{ item.classifyName }}</span>
                  <span>大于等于</span>
                  <span>{{ item.hour }}</span>
                </div>
              </div>
            </div>
            <div class="item4" v-if="conditions == '1'">
              <div class="label">
                <i>*</i>
                <span>学时统计范围：</span>
              </div>
              <div class="chooseRange">
                <a-radio-group
                  @change="typeValue = []"
                  :options="rangeOptions"
                  v-model:value="range"
                />
              </div>
              <div class="choosetype" v-if="range == '1'">
                <!-- <a-select
                  class="input"
                  ref="select"
                  placeholder="请选择分类"
                  v-model:value="typeValue"
                  style="width: 328px"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in typeList"
                    >{{ item.label }}</a-select-option
                  >
                </a-select> -->
                <a-cascader
                  style="width: 328px"
                  v-model:value="typeValue"
                  :options="typeList"
                  placeholder="请选择分类"
                  change-on-select
                />
              </div>
            </div>
            <div class="item" v-if="conditions == '0'">
              <div class="label">
                <i>*</i>
                <span>学时配置：</span>
              </div>
              <div class="itemBox">
                <div v-for="(item, index) in classHourList" :key="index">
                  <span>【{{ item.id }}】&nbsp;{{ item.name }}：</span>
                  <span style="margin-left: 12px">合格</span>
                  <span style="margin-left: 8px; margin-right: 8px">≥</span>
                  <a-input
                    @blur="changclassHourValue(item)"
                    :style="{ border: item.istrue ? '' : '1px solid red' }"
                    :controls="false"
                    placeholder="请输入"
                    v-model:value="item.value"
                    style="width: 168px"
                  >
                    <template #suffix>
                      <span>学时</span>
                    </template></a-input
                  >
                  <span style="margin-left: 8px; margin-right: 8px">＜</span>
                  <span>不合格</span>
                </div>
                <div class="title">
                  考核要求在满足
                  【1】【2】【3】都为合格的情况下判定为合格，颁发证书；否则为不合格，不给予颁发证书
                </div>
              </div>
            </div>
            <div class="item2" v-if="conditions == '0'">
              <div class="label2">
                <i>*</i>
                <span>学分配置：</span>
                <a-switch v-model:checked="checked" />
              </div>
              <div class="itemBox" v-if="checked">
                <div v-for="(item, index) in creditHourList" :key="index">
                  <span>【{{ item.id }}】&nbsp;{{ item.name }}：</span>
                  <span style="margin-left: 12px">合格</span>
                  <span style="margin-left: 8px; margin-right: 8px">≥</span>
                  <a-input
                    @blur="changcreditHour(item)"
                    :style="{ border: item.istrue ? '' : '1px solid red' }"
                    placeholder="请输入"
                    v-model:value="item.value"
                    style="width: 168px"
                  >
                    <template #suffix>
                      <span>学分</span>
                    </template></a-input
                  >
                  <span style="margin-left: 8px; margin-right: 8px">＜</span>
                  <span>不合格</span>
                </div>
                <div class="title">
                  考核要求在满足
                  【1】【2】【3】都为合格的情况下判定为合格，颁发证书；否则为不合格，不给予颁发证书
                </div>
              </div>
            </div>
          </div>
          <div class="save">
            <a-button type="primary" @click="save">保存</a-button>
            <!-- <a-button style="margin-left: 8px" @click="Cancel" v-if="setType != 1">取消</a-button> -->
          </div>
        </div>
      </div>
      <div v-else-if="navActive == 2" class="oneCertificate">
        <div class="item1">
          <div class="label">
            <div>证书ID：</div>
          </div>
          <a-input
            placeholder="请输入"
            @input="filterNumberInput1"
            v-model:value="oneCertificateId"
            style="width: 288px"
          />
        </div>
        <div class="yulan">
          <span style="color: #8c8c8c" class="oneCertificateTitle"
            >(请输入证书管理里对接颁发证书的ID)</span
          >
        </div>
        <div class="item1">
          <div class="label">
            <div>证书落款：</div>
          </div>
          <a-input
            placeholder="请输入"
            v-model:value="certificateSignature"
            style="width: 288px"
            maxlength="20"
          />
        </div>
        <div class="saveOne">
          <a-button type="primary" @click="saveOne">保存</a-button>
        </div>
        <!-- <div class="showCercificate">
          <iframe class="iframeBox" type="application/x-google-chrome-pdf" frameborder="0"
            style="width: 375px; height: 545px" :src="iframeUrl"></iframe>
        </div> -->
      </div>
      <div v-else class="oneCertificate">
        <div class="item1">
          <div class="label">
            <span>证书ID：</span>
          </div>
          <a-input
            placeholder="请输入"
            @input="filterNumberInput2"
            v-model:value="yearCertificateId"
            style="width: 288px"
          />
        </div>
        <div class="yulan">
          <span style="color: #8c8c8c" class="oneCertificateTitle"
            >(请输入证书管理里对接颁发证书的ID)</span
          >
        </div>
        <div class="saveOne">
          <a-button type="primary" @click="saveYear">保存</a-button>
        </div>
      </div>
    </div>
  </div>
  <!-- <a-modal v-model:visible="open" title="证书预览" :footer="null">
    <div style="
        width: 100%;
        min-height: 590px;
        display: flex;
        align-items: center;
        justify-content: center;
      ">
      <iframe class="iframeBox" type="application/x-google-chrome-pdf" frameborder="0"
        style="width: 375px; height: 545px" :src="iframeUrl"></iframe>
    </div>
  </a-modal> -->
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from "vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import {
  editCertifyRule,
  getCertifyRule,
  getSystemSet,
  editPersonalCertifyRule,
  getPersonalCertifyRule,
  complianceList,
  hanlleGetTree,
  getConfig,
} from "@/request";
/* --------------- data --------------- */
//#region
const navList = ref<any>([
  { id: 1, name: "学年证书规则" },
  { id: 2, name: "单个活动证书规则" },
  { id: 3, name: "跨年证书规则" },
]);
const time = ref<Array<any> | null>();
interface types {
  id: number;
  name: string;
  value: number | null;
  istrue: boolean;
}
const open = ref(false);
const iframeUrl = ref("");
const checked = ref(true);
const certificateChecked = ref(true);
const certificateId = ref("");
const eduSystemSetId = ref("");
const options1 = ref<any[]>([]);
const classHourList = ref<types[]>([
  { id: 1, name: "规范性", value: null, istrue: true },
  { id: 2, name: "灵活性", value: null, istrue: true },
  { id: 3, name: "总学时", value: null, istrue: true },
]);
const creditHourList = ref<types[]>([
  { id: 1, name: "规范性", value: null, istrue: true },
  { id: 2, name: "灵活性", value: null, istrue: true },
  { id: 3, name: "总学时", value: null, istrue: true },
]);
const ceretificateList = ref<any>([]);
const setType = ref(1);
const setTypeName = ref();
const showCreateOrEditRule = ref(false);
const activeRule = ref(1);
const navActive = ref(1);
const oneCertificateId = ref(null);
const eduPersonalCertifiRuleId = ref<any>(null);
const eduYearCertifiRuleId = ref<any>(null);
const yearCertificateId = ref(null);
const signature = ref(null);
const certificateSignature = ref(null);
//#endregion
const conditionsOptions = ref([
  { label: "手动设置", value: "0" },
  { label: "年度学时达标", value: "1" },
]);
const rangeOptions = ref([
  { label: "全部", value: "0" },
  { label: "指定分类", value: "1" },
]);
const range = ref<any>("0");
const conditions = ref<any>("0");
const typeValue = ref([]);
const typeList = ref([]);
const requireList = ref<any>([]);
const personalCertify = ref(null);
const teamCertify = ref<any>(null);
watch(
  () => checked.value,
  (newV) => {
    if (!newV) {
      creditHourList.value = creditHourList.value.map((item) => {
        item.value = null;
        return item;
      });
    }
  }
);

/* --------------- methods --------------- */
//#region
const handleGetRequireList = () => {
  complianceList().then((res: any) => {
    if (res.data.code == 0) {
      let obj = {
        id: -1,
        classifyName: "总学时",
        hour: 72,
      };
      requireList.value = [obj, ...res.data.data];
    }
  });
};
const changeChildren = (tree: any) => {
  tree.forEach((node: any) => {
    node.children = node.chirldrenList;
    node.value = node.id;
    node.label = node.name;
    if (node.chirldrenList && node.chirldrenList.length > 0) {
      changeChildren(node.chirldrenList);
    }
  });
};
const handleGetTypeTree = async () => {
  let params = {
    status: 0,
  };
  const responce = await hanlleGetTree(params);
  const res: any = responce.data;
  typeList.value = res.data[0].chirldrenList || [];
  changeChildren(typeList.value);
};
const init = () => {
  getConfig().then((res: any) => {
    if (res.data.code == 0) {
      personalCertify.value = res.data.data.personalCertify;
      console.log("res.data.data", res.data.data);
      // teamCertify.value = res.data.data.teamCertify;
      if (
        res.data.data.teamCertify == 1 &&
        res.data.data.personalCertify == 1
      ) {
        teamCertify.value = 1;
      } else {
        teamCertify.value = 2;
      }
    }
  });
};
onMounted(async () => {
  await init();
  await handleGetTypeTree();
  await handleGetRequireList();
  await getSystemSetData();
  await getRuleSetting();
});
const filterNumberInput = (val: any) => {
  certificateId.value = val.target.value.replace(/\D/g, "");
};
const filterNumberInput1 = (val: any) => {
  oneCertificateId.value = val.target.value.replace(/\D/g, "");
};
const filterNumberInput2 = (val: any) => {
  yearCertificateId.value = val.target.value.replace(/\D/g, "");
};
const changeNav = (val: any) => {
  navActive.value = val;
  if (val == 1) {
    getSystemSetData();
    getRuleSetting();
  } else if (val == 2) {
    showCreateOrEditRule.value = false;
    getOneCertificateRule(1);
  } else {
    showCreateOrEditRule.value = false;
    getOneCertificateRule(2);
  }
};
// 获取单个证书规则
const getOneCertificateRule = async (num: any) => {
  const res = (await getPersonalCertifyRule({
    type: num,
    teamType: teamCertify.value,
  })) as any;
  if (res.data.code == 0) {
    if (navActive.value == 2) {
      eduPersonalCertifiRuleId.value = res.data.data.eduPersonalCertifiRuleId;
      oneCertificateId.value = res.data.data.personalCertificateId;
      certificateSignature.value = res.data.data.companyName;
    } else {
      eduYearCertifiRuleId.value = res.data.data.eduPersonalCertifiRuleId;
      yearCertificateId.value = res.data.data.personalCertificateId;
    }
  }
};
// 单个证书保存
const saveOne = async () => {
  if (!oneCertificateId.value) {
    message.error("请输入证书编号");
    return;
  }
  if (!certificateSignature.value) {
    message.error("请输入证书落款");
    return;
  }
  let params = {
    eduPersonalCertifiRuleId: eduPersonalCertifiRuleId.value,
    personalCertificateId: oneCertificateId.value,
    companyName: certificateSignature.value,
    type: 1,
  };
  const res = (await editPersonalCertifyRule(params)) as any;
  console.log(res.data);
  if (res.data.code == 0 && res.data.success) {
    message.success("保存成功");
    getOneCertificateRule(1);
  }
};
const saveYear = async () => {
  if (!yearCertificateId.value) {
    message.error("请输入证书编号");
  } else {
    let params = {
      eduPersonalCertifiRuleId: eduYearCertifiRuleId.value,
      personalCertificateId: yearCertificateId.value,
      type: 2,
    };
    const res = (await editPersonalCertifyRule(params)) as any;
    console.log(res.data);
    if (res.data.code == 0 && res.data.success) {
      message.success("保存成功");
      getOneCertificateRule(2);
    }
    // else {
    //   message.error(res.data.msg);
    // }
  }
};
// 证书预览,暂无
// const showCertificate = () => {
//   if (!certificateId.value) {
//     message.error("请输入证书ID");
//   }
//   else {
//     if (navActive.value == 1) {
//      '学年证书预览'
//     }
//     else {
//       '单个活动证书预览'
//     }

//   }
// }
// 新建或编辑证书规则
const createRule = (val: any, item?: any) => {
  // 1为新建2为编辑
  if (val == 2) {
    checked.value = item.scoreSwitch == 1 ? true : false;
    ruleSettingId.value = item.eduCertifiRuleId;
    eduSystemSetId.value = item.eduSystemSetId;
    certificateId.value = item.certificateId;
    classHourList.value[0].value = item.gfHour;
    classHourList.value[1].value = item.lhHour;
    classHourList.value[2].value = item.totalHour;
    creditHourList.value[0].value = item.gfScore;
    creditHourList.value[1].value = item.lhScore;
    creditHourList.value[2].value = item.totalScore;
    setTypeName.value = item.name;
    activeRule.value = item.eduCertifiRuleId;
    conditions.value = String(item.publishType);
    range.value = item.staticsRange == "0" ? "0" : "1";
    signature.value = item.companyName;
    if (range.value == "1") {
      let arr = JSON.parse(JSON.stringify(item.cascaderPath));
      arr.splice(0, 1);
      typeValue.value = arr;
    }
  } else {
    ruleSettingId.value = null;
    eduSystemSetId.value = "";
    certificateId.value = "";
    classHourList.value[0].value = null;
    classHourList.value[1].value = null;
    classHourList.value[2].value = null;
    creditHourList.value[0].value = null;
    creditHourList.value[1].value = null;
    creditHourList.value[2].value = null;
    setTypeName.value = "";
    activeRule.value = 1;
    conditions.value = "0";
    range.value = "0";
    typeValue.value = [];
    signature.value = null;
  }
  showCreateOrEditRule.value = true;
  setType.value = val;
};
const getSystemSetData = async () => {
  const response = (await getSystemSet()) as any;
  const res = response.data;
  if (res.code === 0) {
    options1.value = res.data.map((item: any) => {
      if (item.name == null) {
        item.name = `${item.startTime.split("-")[0]}-${
          item.endTime.split("-")[0]
        }`;
      }
      return item;
    });
  }
};

const ruleSettingId = ref(null);
async function getRuleSetting() {
  let params = {
    type: personalCertify.value,
  };
  let res = (await getCertifyRule(params)) as any;
  ceretificateList.value = res.data.data;
  // checked.value = res.data.data.scoreSwitch == 1 ? true : false;
  // certificateId.value = res.data.data.certificateId;
  // classHourList.value[0].value = res.data.data.gfHour;
  // classHourList.value[1].value = res.data.data.lhHour;
  // classHourList.value[2].value = res.data.data.totalHour;
  // creditHourList.value[0].value = res.data.data.gfScore;
  // creditHourList.value[1].value = res.data.data.lhScore;
  // creditHourList.value[2].value = res.data.data.totalScore;
  // ruleSettingId.value = res.data.data.eduCertifiRuleId;
  // eduSystemSetId.value = res.data.data.eduSystemSetId;
  // time.value = [res.data.data.eduSystemSet.startTime, res.data.data.eduSystemSet.endTime]
}
function isNumber(val: any) {
  if (/^\d+$/.test(val.value)) {
    val.istrue = true;
  } else {
    val.istrue = false;
  }
  return val.istrue;
}
const changcreditHour = (val: any) => {
  isNumber(val);
};
const changclassHourValue = (val: any) => {
  isNumber(val);
};
// 保存
const save = () => {
  let classHour = 0;
  if (!certificateId.value) {
    message.error("请输入证书id");
    return;
  }
  if (!signature.value) {
    message.error("请输入证书落款");
    return;
  }
  for (let i of classHourList.value) {
    if (isNumber(i)) {
      classHour += 1;
    }
  }
  for (let i of creditHourList.value) {
    if (isNumber(i)) {
      classHour += 1;
    }
  }
  if (!eduSystemSetId.value) {
    message.error("请选择学年度");
    return;
  }
  if (conditions.value == "0") {
    console.log(123);
    if (classHour < (checked.value ? 6 : 3)) {
      message.error("输入错误,请检查后重新输入");
    } else {
      saveRuleSetting();
    }
  } else {
    saveRuleSetting();
  }
};

async function saveRuleSetting() {
  console.log(111);
  let id = null;
  id =
    typeValue.value.length > 0
      ? typeValue.value[typeValue.value.length - 1]
      : null;
  let params = {
    certificateId: certificateId.value,
    eduCertifiRuleId: ruleSettingId.value != null ? ruleSettingId.value : null,
    eduSystemSetId: eduSystemSetId.value,
    gfHour: classHourList.value[0].value,
    gfScore: creditHourList.value[0].value,
    lhHour: classHourList.value[1].value,
    lhScore: creditHourList.value[1].value,
    scoreSwitch: checked.value ? 1 : 0,
    totalHour: classHourList.value[2].value,
    totalScore: creditHourList.value[2].value,
    companyName: signature.value,
    publishType: conditions.value,
    staticsRange: range.value == 0 ? 0 : id,
  };
  const res = (await editCertifyRule(params)) as any;
  if (res.data.code == 0 && res.data.success) {
    message.success("设置成功");

    getRuleSetting();
  }
}
const Cancel = () => {
  time.value = [];
  classHourList.value.forEach((item: any) => {
    item.value = "";
    item.istrue = true;
  });
  creditHourList.value.forEach((item: any) => {
    item.value = "";
    item.istrue = true;
  });
};

//#endregion
</script>

<style lang="scss" scoped>
.head {
  font-weight: bold;
  margin-bottom: 24px;
}

.ruleSetting {
  padding: 16px 24px;
  height: calc(100vh - 60px);

  .nav {
    display: flex;
    align-items: center;
    margin-left: -1px;

    .navItem {
      cursor: pointer;
      height: 40px;
      line-height: 40px;
      padding: 0 20px;
      text-align: center;
      background: #fafafa;
      border: 1px solid #f0f0f0;
      margin-right: 4px;
    }

    .chooseNav {
      height: 43px !important;
      color: #007aff;
      font-weight: 400;
      font-size: 14px;
      line-height: 41px;
      background: #ffffff;
      border-bottom: none;
    }
  }

  .content {
    padding: 16px 24px 0px 24px;
    // margin-top: 1px;
    height: 90%;
    background: #ffffff;

    .yearCertificate {
      display: flex;
      width: 100%;
      height: 100%;

      .left {
        height: 100%;
        width: 32%;
        padding: 0 24px 24px 0;
        border-right: 1px solid #e5e5e5;

        .createBtn {
          height: 48px;
          background: #ffffff;
          box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
          border-radius: 4px 4px 4px 4px;
          border: 1px dashed #d9d9d9;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #262626;
          font-size: 14px;
          cursor: pointer;
        }

        .certificateRuleList {
          margin-top: 16px;
          height: calc(100% - 100px);
          overflow: auto;

          .isList {
            .listItem {
              height: 48px;
              background: #f5f5f5;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #f0f0f0;
              line-height: 48px;
              padding: 0 16px;
              margin-bottom: 16px;
              cursor: pointer;

              &:hover {
                background: #e6f2ff;
              }
            }

            .blueList {
              background: #e6f2ff;
            }
          }

          .nolist {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      .right {
        flex: 1;
        height: 100%;
        overflow: auto;
        padding: 0 24px 24px 24px;

        .formContent {
          .item1 {
            display: flex;
            margin-bottom: 24px;
            align-items: center;
            position: relative;

            .label {
              width: 120px;
              text-align: right;
              line-height: 36px;
              i {
                font-style: normal;
                color: #f53f3f;
                margin-right: 3px;
              }
            }

            .maskInputTitle {
              position: absolute;
              bottom: -25px;
              left: 120px;
              color: #8c8c8c;
            }

            .previewCertificate {
              margin-left: 8px;
              width: 64px;
              height: 36px;
              background: #ffffff;
              box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #007aff;
              display: flex;
              justify-content: center;
              align-items: center;
              color: #007aff;
              cursor: pointer;
            }
          }

          .item2 {
            // display: flex;
            // align-items: center;
            margin-bottom: 24px;
            // justify-content: flex-start;

            .label2 {
              width: 120px;
              display: flex;
              align-items: center;
              width: auto;
              text-align: right;

              i {
                font-style: normal;
                color: #f53f3f;
                margin-right: 3px;
              }
            }

            .times {
              background: #f5f5f5;
              width: 328px;
            }

            .itemBox {
              // height: 218px;
              background: #f5f5f5;
              border-radius: 4px 4px 4px 4px;
              padding: 20px 16px;
              width: 705px;
              margin-left: 120px;
              margin-top: 20px;

              div {
                color: #262626;
                margin-bottom: 16px;
              }
            }
          }
          .item3 {
            margin-top: -12px;
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            .label {
              width: 120px;
              text-align: right;

              i {
                color: #f53f3f;
                margin-right: 3px;
              }
            }
            .requirement {
              color: #8c8c8c;
            }
          }
          .item {
            display: flex;
            // align-items: center;
            margin-bottom: 24px;
            // justify-content: flex-start;

            .label {
              width: 120px;
              text-align: right;

              i {
                color: #f53f3f;
                margin-right: 3px;
              }
            }

            .times {
              background: #f5f5f5;
              width: 328px;
            }

            .itemBox {
              // height: 218px;
              background: #f5f5f5;
              border-radius: 4px 4px 4px 4px;
              padding: 20px 16px;
              width: 705px;

              div {
                color: #262626;
                margin-bottom: 16px;
              }
            }
          }
          .item4 {
            display: flex;
            align-items: center;
            .label {
              width: 120px;
              text-align: right;
              line-height: 36px;
              i {
                font-style: normal;
                color: #f53f3f;
                margin-right: 3px;
              }
            }
          }
        }

        .save {
          padding-left: 80px;
        }
      }
    }

    .oneCertificate {
      .item1 {
        display: flex;
        align-items: center;
        .label {
          width: 80px;
          text-align: right;
          // i {
          //   color: #f53f3f;
          //   margin-right: 3px;
          // }
        }
        // .oneCertificateTitle {
        //   border: 1px solid red;
        // }
        // display: flex;
        // align-items: center;
      }
      .yulan {
        margin-left: 80px;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        .previewCertificate {
          margin-left: 8px;
          width: 64px;
          height: 36px;
          background: #ffffff;
          box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.02);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #007aff;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #007aff;
          cursor: pointer;
        }
      }
      .saveOne {
        margin-top: 24px;
        margin-left: 55px;
      }

      .showCercificate {
        margin-top: 24px;
      }
    }
  }
}
</style>
