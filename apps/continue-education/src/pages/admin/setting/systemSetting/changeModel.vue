<template>
  <div class="opa" v-if="show">
    <div class="changeBox">
      <div class="closeIcon" @click="close">
        <ysIcon type="icon-close" />
      </div>
      <div class="title">修改当前学年</div>
      <div class="searchYear">
        <a-select
          ref="select"
          placeholder="请选择"
          v-model:value="eduSystemSetId"
          style="width: 368px"
        >
          <a-select-option
            :value="item.eduSystemSetId"
            v-for="item in dateList"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </div>
      <div class="footer">
        <a-button @click="close">取消</a-button>
        <a-button type="primary" @click="sureChange" style="margin-left: 8px"
          >确定</a-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { ysIcon } from "@ys/ui";
import { message } from "ant-design-vue";
/* --------------- data --------------- */
//#region
const eduSystemSetId = ref<any>(null);
const props = defineProps<{
  show: any;
  dateList: any;
  editInfo: any;
}>();
//#endregion
watch(
  () => props.show,
  (val) => {},
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => props.editInfo,
  (val) => {
    if (val) {
      console.log("val", val);
      eduSystemSetId.value = props.editInfo.eduSystemSetId;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
const emit = defineEmits(["close", "ok"]);
/* --------------- methods --------------- */
//#region
const close = () => {
  emit("close");
};
const sureChange = () => {
  if (!eduSystemSetId.value) {
    message.error("请选择要修改的学年");
    return false;
  }
  console.log("eduSystemSetId.value", eduSystemSetId.value);
  emit("ok", eduSystemSetId.value);
};

//#endregion
</script>

<style lang="scss" scoped>
.opa {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .changeBox {
    width: 448px;
    // height: 202px;
    background: #ffffff;
    box-shadow: 0px 6px 12px 0px rgba(51, 51, 51, 0.16);
    border-radius: 4px 4px 4px 4px;
    padding: 28px 40px;
    position: relative;

    .closeIcon {
      position: absolute;
      top: 12px;
      right: 20px;
      font-size: 16px;
      cursor: pointer;
    }

    .title {
      text-align: center;
      font-size: 16px;
      color: #262626;
    }

    .searchYear {
      margin-top: 24px;
    }

    .footer {
      margin-top: 32px;
      text-align: right;
    }
  }
}
</style>
