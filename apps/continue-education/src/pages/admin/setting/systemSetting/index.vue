<template>
  <div class="systemSetting" ref="systemSetting">
    <span>系统设置</span>
    <div class="content">
      <!-- <div class="head">
        系统设置
      </div>
      <div class="systemForm">
        <div class="item">
          <div class="label">
            <i>*</i>
            <span>学年度：</span>
          </div>
          <a-range-picker :style="{ border: istrue ? '' : '1px solid red' }" @calendarChange="onCalendarChange"
            @blur="changeTime"  class="times" v-model:value="time" 
            format="YYYY-MM" picker="month" valueFormat="YYYY-MM"  />
          <question-circle-outlined style="color: #8C8C8C;margin-left: 9px;" />
          <span style="margin-left:8px;color:#8C8C8C">学年度时间为一年，请设置当前需要申报的学年度</span>
        </div>
        <div class="sureSave">
          <a-button type="primary" @click="sure">保存</a-button>
        </div>
      </div> -->
      <div class="nowYear">
        <span class="nowTime"> 当前学年：{{ currentDate?.name }} </span>
        <span class="changeTime" @click="changeCurrentDate">修改</span>
      </div>
      <div class="timeList">
        <div class="head">
          <span>学年列表</span>
          <a-button type="primary" @click="addTime">
            <ysIcon type="icontianjia" />
            新增
          </a-button>
        </div>
        <div class="listTable">
          <a-table
            :dataSource="dateList"
            :columns="columns"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'operation'">
                <span
                  style="color: #007aff; cursor: pointer"
                  @click="onEdit(record)"
                  >编辑</span
                >
                <a-popconfirm
                  :getPopupContainer="() => $refs.systemSetting"
                  title="确定删除吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="onconfirmDel(record)"
                >
                  <span
                    style="color: #007aff; margin-left: 8px; cursor: pointer"
                    >删除</span
                  >
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </div>
        <!-- <div class="pagations">
          <ysPagination
            :pageNo="pageNo"
            :pageSize="pageSize"
            :total="total"
            @change="handlePageChange"
          />
        </div> -->
      </div>
    </div>
  </div>
  <addModel
    :visible="showAddBox"
    :editInfo="editInfo"
    @close="onAddClose"
    @define="onAddDefine"
  />

  <changeModel
    :show="showChangeModel"
    :dateList="dateList"
    :editInfo="editInfo"
    @close="oncloseChangeModel"
    @ok="onOkChangeModel"
  />
</template>

<script lang="ts" setup>
import changeModel from "./changeModel.vue";
import addModel from "./addModel.vue";
import { ysIcon, ysPagination } from "@ys/ui";
import dayjs, { Dayjs } from "dayjs";
import { ref, onMounted } from "vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import { tableColumns } from "@/assets/types";
import { delSystemSet, editSystemSet, getSystemSet } from "@/request";
type RangeValue = [Dayjs, Dayjs];
/* --------------- data --------------- */
//#region
// const time = ref<any>(null);
const time = ref<any>();
// const value2 = ref<RangeValue>();
// const istrue = ref(true)
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(1);
const showAddBox = ref(false);
const editInfo = ref<any>(null);
const showChangeModel = ref(false);
//#endregion
const columns = ref<Array<tableColumns>>([
  { title: "序号", dataIndex: "index", width: "80px" },
  { title: "学年度", dataIndex: "name" },
  { title: "开始时间", dataIndex: "startTime" },
  { title: "结束时间", dataIndex: "endTime" },
  { title: "操作", dataIndex: "operation", width: "120px" },
]);
const dateList = ref<any>([]);
const currentDate = ref<any>(null);
/* --------------- methods --------------- */
//#region

// const handlePageChange = (number: number, size: number) => {
//   pageNo.value = number;
//   pageSize.value = size;
// };
// const sure = () => {
//   if (time.value == null) {
//     // istrue.value = false
//     message.error('请选择学年度')
//   }
//   else {
//     console.log('保存')
//     changeSystemSetting()
//   }
// }
// const onCalendarChange = (val: RangeValue) => {
//   time.value = val;
// };
// const getTime = () => {
//   let year = null;
//   let month = null;
//   let nowTime = null;
//   month = new Date().getMonth() + 1;
//   year = new Date().getFullYear();
//   console.log(month);
//   console.log(year);
//   if (month > 8) {
//     nowTime = `${year}~${year + 1}`;
//   } else {
//     nowTime = `${year - 1}~${year}`;
//   }
//   return nowTime;
// };
// async function getSystemSetting() {
//   const res = await getSystemSet() as any
//   // console.log(res.data.data)
//   console.log('res', res)
//   let { startTime, endTime } = res.data.data
//   time.value = [startTime, endTime]
//   systemSettingId.value = res.data.data.eduSystemSetId
// }
// async function changeSystemSetting() {
//   let params = {
//     eduSystemSetId: systemSettingId.value != null ? systemSettingId.value : null,
//     startTime: time.value[0],
//     endTime: time.value[1]
//   }
//   const res = await editSystemSet(params) as any
//   if (res.data.code == 0) {
//     message.success('设置成功')
//     getSystemSetting()
//   }
// }

const getSystemSetData = async () => {
  try {
    const response = await getSystemSet();
    const res = response.data as any;
    dateList.value = res.data.map((item: any, key: number) => {
      item.index = key + 1;
      if (item.name == null) {
        item.name = `${item.startTime.split("-")[0]}-${item.endTime.split("-")[0]}`;
      }
      return item;
    });

    currentDate.value = dateList.value.find((item: any) => {
      return item.isNow == 1;
    });

    console.log("dateList", dateList.value);
    console.log("currentDate", currentDate.value);
  } catch (error) {
    console.error(error);
  }
};

const addTime = () => {
  showAddBox.value = true;
};
const onAddClose = () => {
  showAddBox.value = false;
  editInfo.value = null;
};
const onAddDefine = async (params: any) => {
  const { studyTime, startTime, endTime } = params;
  try {
    const result = Number(dayjs(endTime).diff(dayjs(startTime), "days"));
    // if (result < 366) {
    //   message.error("年度范围必须大于一年");
    //   return false;
    // }
    const response = await editSystemSet({
      eduSystemSetId: editInfo.value ? editInfo.value.eduSystemSetId : null,
      name: studyTime,
      startTime,
      endTime,
      isNow: editInfo.value ? editInfo.value.isNow : null,
    });
    const res = response.data as any;
    if (res.code == 0) {
      message.success(`${editInfo.value ? "编辑" : "新增"}成功`);
      onAddClose();
      getSystemSetData();
    }
  } catch (error) {
    console.error("editSystemSet", error);
  }
};

const onEdit = (record: any) => {
  addTime();
  editInfo.value = record;
};

const onconfirmDel = async (item: any) => {
  try {
    const response = await delSystemSet({
      systemSetId: item.eduSystemSetId,
    });
    const res = response.data as any;
    if (res.code == 0) {
      message.success("删除成功");
      getSystemSetData();
    }
  } catch (error) {}
};

const changeCurrentDate = () => {
  showChangeModel.value = true;
  editInfo.value = currentDate.value;
};

const oncloseChangeModel = () => {
  showChangeModel.value = false;
  editInfo.value = null;
};

const onOkChangeModel = async (value: any) => {
  let find = dateList.value.find((item: any) => {
    return item.eduSystemSetId == value;
  });

  const response = await editSystemSet({
    eduSystemSetId: value,
    name: find.name,
    startTime: find.startTime,
    endTime: find.endTime,
    isNow: 1,
  });
  const res = response.data as any;
  if (res.code == 0) {
    message.success(`编辑成功`);
    oncloseChangeModel();
    getSystemSetData();
  }
};

onMounted(() => {
  // time.value = getTime();
  // timeList.value = timeList.value.map((item: any, index: any) => {
  //   return { ...item, index: index + 1 };
  // });
  getSystemSetData();
});
//#endregion
</script>

<style lang="scss" scoped>
.systemSetting {
  padding: 16px 24px;
  height: calc(100vh - 60px);

  .content {
    padding: 16px 24px 0px 24px;
    margin-top: 16px;
    height: 90%;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    padding: 24px;
    overflow: auto;

    // .head {
    //   font-size: 16px;
    //   font-weight: 400;
    //   color: #262626;
    // }

    // .systemForm {
    //   margin-top: 24px;

    //   .item {
    //     display: flex;
    //     align-items: center;

    //     .label {
    //       i {
    //         color: red
    //       }
    //     }

    //     .times {
    //       width: 328px;
    //     }
    //   }

    //   .sureSave {
    //     margin-top: 24px;
    //     padding-left: 60px;
    //   }
    // }

    .nowYear {
      height: 68px;
      background: #fafafa;
      display: flex;
      justify-content: center;
      align-items: center;

      .nowTime {
        display: inline-block;
        height: 25px;
        line-height: 25px;
        font-size: 20px;
        font-weight: bold;
      }

      .changeTime {
        cursor: pointer;
        margin-left: 8px;
        margin-top: 5px;
        display: inline-block;
        height: 25px;
        color: #007aff;
        display: flex;
        align-items: flex-end;
      }
    }

    .timeList {
      margin-top: 16px;

      .head {
        display: flex;
        justify-content: space-between;
      }

      .listTable {
        margin-top: 16px;
      }

      .pagations {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}
</style>

<style lang="scss">
.systemSetting {
  .ant-popover-buttons {
    .ant-btn {
      height: 24px;
    }
  }
}
</style>
