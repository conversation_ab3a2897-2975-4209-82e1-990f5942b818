<template>
  <a-modal
    v-model:visible="props.visible"
    :maskClosable="false"
    :title="`${editInfo ? '编辑学年' : '新增学年'}`"
    :width="448"
    wrapClassName="addTimeModel"
    centered
    @ok="handleDefine"
    @cancel="close"
  >
    <div class="studyYear">
      <div class="txt">学年：</div>
      <a-select
        ref="select"
        placeholder="请选择"
        v-model:value="studyTime"
        style="width: 328px"
        :options="studyTimeList"
      ></a-select>
    </div>
    <div class="startTime">
      <div class="txt">开始时间：</div>
      <a-date-picker
        style="width: 328px"
        picker="month"
        placeholder="选择时间"
        v-model:value="startTime"
        value-format="YYYY-MM"
        format="YYYY-MM"
      >
        <template #suffixIcon>
          <ClockCircleOutlined />
        </template>
      </a-date-picker>
    </div>
    <div class="startTime">
      <div class="txt">结束时间：</div>
      <a-date-picker
        style="width: 328px"
        picker="month"
        placeholder="选择时间"
        v-model:value="endTime"
        value-format="YYYY-MM"
        format="YYYY-MM"
      >
        <template #suffixIcon>
          <ClockCircleOutlined />
        </template>
      </a-date-picker>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import useStudyTime from "@/hooks/useStudyTime";
import { ClockCircleOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
/* --------------- data --------------- */
//#region
const { studyTimeList } = useStudyTime();
const emit = defineEmits(["define", "close"]);
const props = defineProps<{
  visible: boolean;
  editInfo: any;
}>();
const studyTime = ref(null);
const startTime = ref(null);
const endTime = ref(null);

//#endregion

/* --------------- methods --------------- */
//#region
const handleDefine = () => {
  if (!studyTime.value) {
    message.error("请选择学年度");
    return;
  }
  if (!startTime.value) {
    message.error("请选择开始时间");
    return;
  }
  if (!endTime.value) {
    message.error("请选择结束时间");
    return;
  }
  emit("define", {
    studyTime: studyTime.value,
    startTime: startTime.value,
    endTime: endTime.value,
  });
};
const clear = () => {
  studyTime.value = null;
  startTime.value = null;
  endTime.value = null;
};
const close = () => {
  clear();
  emit("close");
};

watch(
  () => props.visible,
  (newV) => {
    if (!newV) {
      clear();
    }
  }
);

watch(
  () => props.editInfo,
  (newV) => {
    if (newV) {
      studyTime.value = newV.name;
      startTime.value = newV.startTime;
      endTime.value = newV.endTime;
    }
    console.log("newV", newV);
  }
);

//#endregion
</script>

<style lang="scss" scoped>
.studyYear {
  height: 36px;
  display: flex;
  align-items: center;

  .txt {
    width: 70px;
    text-align: right;
  }
}

.startTime {
  margin-top: 24px;
  height: 36px;
  display: flex;
  align-items: center;

  .txt {
    width: 70px;
    text-align: right;
  }
}
</style>
<style lang="scss">
.addTimeModel {
  // width: 478px;
  .ant-modal-content {
    width: 478px;
  }

  .ant-modal-body {
    padding: 24px 40px;
    width: 478px;
  }

  .ant-modal-footer {
    padding: 8px 40px 24px 40px;
    border: none;
    // margin-bottom: 24px;
  }
}
</style>
