<template>
  <div class="systemSetting">
    <h4>系统设置</h4>
    <div class="content">
      <div class="header">
        <div class="blueBox"></div>
        <span class="title">系统设置</span>
      </div>
      <div class="container">
        <div class="title1">校级查看学时明细：</div>
        <a-radio-group
          v-model:value="value"
          :disabled="!isAdmin"
          name="radioGroup"
        >
          <a-radio value="1">允许</a-radio>
          <a-radio value="2">不允许</a-radio>
        </a-radio-group>
      </div>
      <div class="container">
        <div class="title1">教师申报非培训学时：</div>
        <a-radio-group
          v-model:value="value1"
          :disabled="!isAdmin"
          name="radioGroup"
        >
          <a-radio value="1">允许</a-radio>
          <a-radio value="2">不允许</a-radio>
        </a-radio-group>
      </div>
      <div class="container">
        <div class="title1">个人申报证书发放单位：</div>
        <a-radio-group
          v-model:value="value2"
          :disabled="!isAdmin"
          name="radioGroup"
        >
          <a-radio value="1">{{ topName }}</a-radio>
          <a-radio value="2">直属上级教育局</a-radio>
        </a-radio-group>
      </div>
      <div class="container">
        <div class="title1">整体申报证书发放单位：</div>
        <a-radio-group
          v-model:value="value3"
          :disabled="!isAdmin"
          name="radioGroup"
        >
          <a-radio value="1">{{ topName }}</a-radio>
          <a-radio value="2">同级教育局</a-radio>
        </a-radio-group>
      </div>
      <div class="submit">
        <a-button type="primary" @click="saveSet" :disabled="!isAdmin">
          <LoadingOutlined v-if="loading" /> 保存
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoadingOutlined } from "@ant-design/icons-vue";
import {
  editConfig,
  getConfig,
  doUserOrgLevel,
  getTopOrgName,
} from "@/request";
import { message } from "ant-design-vue";
const value = ref<any>("1");
const value1 = ref<any>("1");
const value2 = ref<any>("1");
const value3 = ref<any>("1");
const editId = ref<any>(null);
const isAdmin = ref(true);
const loading = ref(false);
const topName = ref("教育局");
const saveSet = () => {
  if (loading.value == true) {
    return;
  } else {
    loading.value = true;
    editConfig({
      id: editId.value,
      allowNotTraining: value1.value == "1" ? true : false,
      personalCertify: Number(value2.value),
      teamCertify: Number(value3.value),
      allowLook: value.value == "1" ? true : false,
    }).then((res: any) => {
      if (res.data.code == 0) {
        setTimeout(() => {
          loading.value = false;
          message.success("修改成功");
          init();
        }, 1000);
      }
    });
  }
};
const init = () => {
  getConfig().then((res: any) => {
    if (res.data.code == 0) {
      editId.value = res.data.data.id;
      value1.value = res.data.data.allowNotTraining == false ? "2" : "1";
      value.value = res.data.data.allowLook == false ? "2" : "1";
      value2.value = String(res.data.data.personalCertify);
      value3.value = String(res.data.data.teamCertify);
    }
  });
};
async function getUploadPermission() {
  let result = (await doUserOrgLevel()) as any;
  console.log("result", result.data.data);
  if (result.data.code == 0) {
    isAdmin.value = result.data.data;
  }
}
const handleGetName = () => {
  getTopOrgName().then((res: any) => {
    if (res.data.code == 0) {
      console.log("res.data.data", res.data.data);
      topName.value = res.data.data;
    }
  });
};
onMounted(() => {
  handleGetName();
  init();
  getUploadPermission();
});
</script>

<style lang="scss" scoped>
.systemSetting {
  padding: 16px 24px;

  .content {
    margin-top: 24px;
    background: #ffffff;
    border-radius: 6px;
    height: calc(100vh - 160px);
    padding: 0 12px;

    .header {
      display: flex;
      align-items: center;
      height: 54px;
      border-radius: 0px 0px 0px 0px;
      border-bottom: 1px solid #f0f0f0;

      .blueBox {
        width: 6px;
        height: 22px;
        background: #007aff;
        border-radius: 1px 1px 1px 1px;
        margin-right: 8px;
      }

      .title {
        font-weight: bold;
      }
    }

    .container {
      padding: 30px 24px;
      display: flex;
      align-items: center;
      .title1 {
        width: 154px;
        text-align: right;
      }
    }
    .submit {
      padding: 0 24px;
    }
  }
}
</style>
