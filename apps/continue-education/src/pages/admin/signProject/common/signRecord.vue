<template>
    <div class="signRecord">
        <iframe :src="signUrl" frameborder="0" width="100%" height="100%"></iframe>
    </div>
    <confrim :visible="deleteConfirmVisible" text_title="汇总结算" text_info="一旦结算后，不可发起第二次结算，请谨慎点击"
        @cancel="deleteConfirmVisible = false" @Ok="handleConfirmExport"></confrim>
</template>

<script lang='ts' setup>
import { ref, onMounted,onUnmounted } from 'vue'
import { useRouter } from 'vue-router';
import confrim from '@/components/confirm.vue'
/* --------------- data --------------- */
//#region
const router = useRouter()
const signUrl = ref<any>(null)
const baseId = ref<any>(null)
const signType = ref<any>(null)
const signName = ref<any>(null)
const deleteConfirmVisible = ref<any>(false)
const eduSignResourceId = ref<any>(null)
const status = ref<any>(null)
const isScore = ref<any>(null)
//#endregion
/* --------------- methods --------------- */
const handleConfirmExport = () => {
    router.push(`projectDetail?id=${eduSignResourceId.value}&status=${status.value}&isScore=${isScore.value}&name=${signName.value}`)
}

const getMsg = (e: any) => {
    if (e.data == 'index') {
        router.push(`signProjectList`)
    }
    else if (e.data.includes('export')) {
        deleteConfirmVisible.value = true
    }
    else if (e.data.includes('alreadyCleared')) {
        console.log(eduSignResourceId.value)
        router.push(`projectDetail?id=${eduSignResourceId.value}&status=${status.value}&isScore=${isScore.value}&name=${signName.value}`)
    }
}

//#region

onMounted(() => {
    baseId.value = router.currentRoute.value.query.baseId
    signType.value = router.currentRoute.value.query.signType
    signName.value = router.currentRoute.value.query.signName
    eduSignResourceId.value = router.currentRoute.value.query.eduSignResourceId
    status.value = router.currentRoute.value.query.status
    isScore.value = router.currentRoute.value.query.isScore
    // status为1代表已结算
    signUrl.value = `${window.location.origin}/yskt/ys/sign/#/sign_xueshi?baseId=${baseId.value}&signType=${signType.value}&signName=${signName.value}&bureauId=${router.currentRoute.value.query.bureauId}&jd=${router.currentRoute.value.query.jd}&status=${status.value}`
    window.addEventListener("message", getMsg);
})

onUnmounted(() => {
    window.removeEventListener("message", getMsg);

})

//#endregion


</script>

<style lang='scss' scoped>
.signRecord {
    width: 100%;
    height: 100%;
    padding: 20px
}
</style>