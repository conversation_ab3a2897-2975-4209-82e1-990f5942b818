<template>
  <div class="signProject" ref="signProject">
    <div class="topHead">
      <h4>签到项目</h4>
      <div class="head_right" @click="goSignHome" v-if="isHaveSign">
        <ysIcon type="iconrili2" style="font-size: 14px" />
        <span>签到任务</span>
      </div>
    </div>

    <div class="header_search">
      <div class="searchItem">
        <span>项目名称：</span>
        <a-input style="width:222px" v-model:value="searchName" placeholder="请输入" />
      </div>
      <div class="searchItem">
        <span>创建日期：</span>
        <a-range-picker style="width:222px" v-model:value="time" :show-time="{ format: 'HH:mm' }"
          format="YYYY-MM-DD HH:mm" valueFormat="YYYY-MM-DD HH:mm" :placeholder="['开始日期', '结束日期']" />
      </div>
      <div class="searchItem1">
        <a-button type="primary" @click="search">查询</a-button>
        <a-button style="margin-left:8px" @click="reset">重置</a-button>
      </div>
    </div>
    <div class="projectList" :class="[styleType == 1 ? 'projectList1' : 'projectList']">
      <div class="create" v-if="pageNo == 1" @click="createSign(1, null)">
        <ysIcon type="icontianjia" style="font-size:18px;font-weight: bold" />
        <span style="margin-top: 8px">创建项目</span>
      </div>
      <div class="listItem " v-for="(item, index) in list">
        <div class="type" :class="[item.signType == 1 ? 'blueType' :item.signType == null?'': 'yellowType']">
          {{ item.signType == 1 ? '指定时间' :item.signType == null?'': '长期循环' }}
        </div>
        <div class="signName text_overflow" :title="item.resourceName">{{ item.resourceName }}</div>
        <div class="signDate text_overflow">
          <span style="color:#8C8C8C">签到日期：</span>
          <span v-if="item.signType == 1">{{ item.signTaskDates[0].signDate }}</span>
          <a-popover :getPopupContainer="() => $refs.signProject" trigger="click" placement="bottomRight"
            overlayClassName="popoverRule">
            <template #content>
              <div class="ruleItem" v-for="(item1, index) in item.signTaskDates" :key="index">
                <span>{{ item1.signDate }}</span>
              </div>
            </template>
            <span v-if="item.signType == 1 && item.signTaskDates.length > 1" style="color:#007aff;margin-left:8px">等{{
              item.signTaskDates.length }}个</span>
          </a-popover>
          <!-- <span v-if="item.signType == 2">{{ getSignDate(item.signDate) }}</span> -->
          <span v-if="item.signType == 2">{{ getSignDate(item.signTaskDates[0].signWeek) }}</span>
        </div>
        <div class="rules" ref="rules" >
          <span style="color:#8C8C8C">学时规则：</span>
          <span v-if="item.studentRules">签到{{ item.studentRules[0].signTime }}次得到{{ item.studentRules[0].classHour }}个学时</span>
          <a-popover :getPopupContainer="() => $refs.signProject" trigger="click" placement="bottomRight"
            overlayClassName="popoverRule">
            <template #content>
              <div class="ruleItem" v-for="(item1, index) in item.studentRules" :key="index">
                <span>签到{{ item1.signTime }}次得到{{ item1.classHour }}个{{ item1.scoreType == 1 ? '学时' : '学分' }}</span>
              </div>
            </template>
            <span style="color:#007aff;margin-left:8px" v-if="item.studentRules&&item.studentRules.length > 1">等{{ item.studentRules.length
              }}个</span>
          </a-popover>

        </div>
        <div class="signTime">
          <span style="color:#8C8C8C">签到时段：</span>
          <div class="flexTime" v-if="item.signTaskDates&&item.signTaskDates.length>0">
            <span>{{ item.signTaskDates[0].signTaskDateTimes[0].startTime
              }}~{{ item.signTaskDates[0].signTaskDateTimes[0].endTime }}
              <a-popover :getPopupContainer="() => $refs.signProject" trigger="click" placement="bottomRight"
                overlayClassName="popoverRule">
                <template #content>
                  <div class="ruleItem" v-for="(item2, index) in item.signTaskDates[0].signTaskDateTimes" :key="index">
                    <span>{{ item2.startTime }}~{{ item2.endTime }}</span>
                  </div>
                </template>
                <span style="color:#007aff;margin-left:8px"
                  v-if="item.signTaskDates[0].signTaskDateTimes.length > 1">等{{
                    item.signTaskDates[0].signTaskDateTimes.length
                  }}个</span>
              </a-popover>
            </span>
            <span>止{{ item.endDate }}</span>
          </div>
        </div>
        <div class="userInfo">
          <div class="left text_overflow">
            <div class="userName text_overflow">{{ item.userName }}</div>
            <div class="createTime">{{ getTime(item.createTime) }}&nbsp;创建</div>
          </div>
          <div class="right" :style="{ background: item.showClick ? '#E0EFFF' : '' }">
            <a-popover v-model:visible="item.visible" placement="bottomRight" @visibleChange="visibleChange(item)"
              overlayClassName="popoverSetBtn" :getPopupContainer="() => $refs.signProject" trigger="click">
              <template #content>
                <a-popover placement="leftTop" :getPopupContainer="() => $refs.signProject" trigger="click"
                  overlayClassName="popoverShare">
                  <template #content>
                    <div class="shareBox">
                      <div class="leftQr">
                        <a-image id="qrcode_img" :src="qrcode" alt=""></a-image>
                      </div>
                      <div class="rightSet">
                        <div class="title">签到链接&二维码</div>
                        <div class="signLink">
                          <a-input-search v-model:value="textVal" disabled size="large">
                            <template #enterButton>
                              <a-button type="primary" @click="handle2Copy">复制</a-button>
                            </template>
                          </a-input-search>
                        </div>
                        <div class="btnSetBox">
                          <a-button @click="handleCopyImg">复制二维码</a-button>
                          <a-button @click="handleDownQrCode">下载二维码</a-button>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="setBtn" v-if="item.signTaskDates">
                    <span>分享</span>
                  </div>
                </a-popover>
                <div class="setBtn" @click="goRecord(item)" v-if="item.signTaskDates">
                  签到记录
                </div>
                <div class="setBtn" @click="createSign(2, item)" v-if="!item.isEnd">
                  编辑
                </div>
                <div class="setBtn" @click="deleteSign(item)">
                  删除
                </div>
                <div class="setBtn" @click="settlement(item)" v-if="item.isEnd && item.signTaskDates">
                  结算汇总
                </div>
              </template>
              <ysIcon type="icon-more1" :style="{ color: item.showClick ? '#007aff' : '' }" />
            </a-popover>
          </div>
        </div>
      </div>
    </div>
    <div class="pagation">
      <a-pagination :pageSizeOptions="['12', '24', '36', '48']" :show-total="(total: any) => `共 ${total}条`"
        :pageSize="pageSize" :current="pageNo" show-quick-jumper :total="total" @change="onChange" />
    </div>
    <createSignProject :isHaveSign="isHaveSign" v-if="showAddDrawer" :editSignId="editSignId" :createType="createType"
      :show="showAddDrawer" @close="closeddDrawer" />
  </div>
</template>

<script lang='ts' setup>
import createSignProject from './createSignProject.vue'
import { ref, onMounted, createVNode } from 'vue'
import { ysIcon } from '@ys/ui';
import { useClipboard } from '@vueuse/core'
import { message, Modal } from "ant-design-vue";
import { useQRCode } from '@vueuse/integrations/useQRCode'
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { useRouter } from 'vue-router';
import { SignProjectList, delSignProject, refresh_get } from "@/request"
import { common } from "@ys/tools";
import dayjs from 'dayjs';
/* --------------- data --------------- */
//#region
const { bureauBlankOpen } = common
const router = useRouter()
const searchName = ref<any>(null)
const time = ref<any>(null);
const list = ref<any>([])
const pageNo = ref<any>(1)
const pageSize = ref<any>(12)
const total = ref<any>(1)
const weekdays = ref<any>([
  { id: 1, name: '周一' },
  { id: 2, name: '周二' },
  { id: 3, name: '周三' },
  { id: 4, name: '周四' },
  { id: 5, name: '周五' },
  { id: 6, name: '周六' },
  { id: 7, name: '周天' }
])
const copyValue = ref<any>('')
const styleType = ref(1)
const textVal = ref<any>('')
let qrcode = useQRCode(textVal.value, {
  margin: 0,
  width: 128,
  height: 128
});
const showAddDrawer = ref(false)
const { copy, isSupported, copied } = useClipboard({ source: copyValue, legacy: true })
const createType = ref<any>(1)
const jd = ref<any>(null)
const bureauId = ref<any>(null)
const tenantID = ref<any>(0)
const editSignId = ref<any>(null)
const isHaveSign = ref(true)
//#endregion

/* --------------- methods --------------- */
//#regio

const getTime = (val: any) => {
  let time = ''
  time = dayjs(new Date(val)).format("YYYY-MM-DD HH:mm");
  return time
}
const goSignHome = () => {
  bureauBlankOpen(`/yskt/ys/sign/#/signIndex?objectType=2000`)
}
// 签到记录
const goRecord = (val: any) => {
  router.push(`/admin/signProject/signRecord?baseId=${val.singId}&signType=${val.signType}&signName=${val.resourceName}&status=${val.isEndSettlement}&eduSignResourceId=${val.eduSignResourceId}&isScore=${val.hasScore}&name=${val.resourceName}`);
}
const closeddDrawer = () => {
  showAddDrawer.value = false
  getList()
}
const deleteSign = (val: any) => {
  val.visible = false
  Modal.confirm({
    centered: true,
    title: createVNode(
      "div",
      { style: { "font-weight": "bold" } },
      "删除签到任务?"
    ),
    content: "一旦删除签到任务后，将无法恢复，请确认",
    icon: createVNode(ExclamationCircleOutlined),
    okText: "确认",
    cancelText: "取消",
    onCancel() {
      val.showClick = !val.showClick
    },
    onOk: () => {
      delSignProject({ id: val.eduSignResourceId }).then((res: any) => {
        if (res.data.code == 0 && res.data.success) {
          message.success('已删除')
          getList()
        }
      })
    },
  });
}
// 汇总结算
const settlement = (val: any) => {
  if (val.isEndSettlement == 1) {
    router.push(`projectDetail?id=${val.eduSignResourceId}&status=${val.isEndSettlement}&isScore=${val.hasScore}&name=${val.resourceName}`)
  }
  else {
    val.visible = false
    Modal.confirm({
      centered: true,
      title: createVNode(
        "div",
        { style: { "font-weight": "bold" } },
        "汇总结算"
      ),
      content: "一旦结算后，不可发起第二次结算，请谨慎点击",
      icon: createVNode(ExclamationCircleOutlined),
      okText: "确认",
      cancelText: "取消",
      onCancel() {
        val.showClick = !val.showClick
      },
      onOk: () => {
        router.push(`projectDetail?id=${val.eduSignResourceId}&status=${val.isEndSettlement}&isScore=${val.hasScore}&name=${val.resourceName}`)
      },
    });
  }

}
const onChange = (page: any, pageS: any) => {
  pageNo.value = page
  pageSize.value = pageS
  getList()

}
// 下载二维码
function handleDownQrCode() {
  console.log(qrcode.value)
  let link = document.createElement("a")
  link.href = qrcode.value
  link.download = '签到二维码'
  link.style.display = "none"
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
function handle2Copy() {
  if (isSupported) {
    copy(textVal.value);
    if (copied) {
      message.success('复制成功')
    } else {
      message.error('复制失败')
    }
  } else {
    message.error('复制失败')
  }
}
function handleCopyImg() {
  handleCopy(qrcode.value)
}
async function handleCopy(e: any) {
  const data = await fetch(e);
  const blob = await data.blob();
  await navigator.clipboard
    .write([
      new ClipboardItem({
        [blob.type]: blob,
      }),
    ])
    .then(() => {
      message.success('复制成功')
    })
    .catch(() => {
      message.error('复制失败')
    });
}
const visibleChange = (val: any) => {
  val.showClick = !val.showClick
  if (val.signTaskDates) {
    if (val.showClick) {
      refresh_get({ baseId: val.signTaskBaseId }).then((res: any) => {
        if (res.data.code == 0 && res.data.success) {
          textVal.value = window.location.origin + '/yskt/ys/sign/#/H5/index?' + 'baseId=' + val.singId + '&jd=' + jd.value + '&bureauId=' + bureauId.value + '&publishOrgid=' + val.publishOrgid + '&time=' + res.data.data.refreshTime + '&tenantId=' + tenantID.value
          qrcode = useQRCode(textVal.value, {
            margin: 0,
            width: 116,
            height: 116
          });
        }
      })
    }
  }
}
const search = () => {
  pageNo.value = 1
  getList()
}
const reset = () => {
  searchName.value = null
  time.value = null
  getList()
}
const createSign = (num: any, val: any) => {
  console.log(val)
  if (num == 2) {
    val.visible = false
    editSignId.value = val.eduSignResourceId
  }
  else {
    editSignId.value = null
  }
  createType.value = num
  showAddDrawer.value = true
}
const getSignDate = (val: any) => {
  let str = ''
  let date = val.split(',')
  if (!val) {
    str = ''
  }
  else {
    for (let i of weekdays.value) {
      for (let j of date) {
        if (i.id == j) {
          if (date.indexOf(j) < date.length - 1) {
            str += i.name + "、"
          } else {
            str += i.name;
          }
        }
      }
    }
  }
  return str
}
const getList = async () => {
  let startTime = ''
  let endTime = ''
  console.log(time.value)
  if (time.value && time.value.length > 0) {
    startTime = time.value[0]
    endTime = time.value[1]
  }
  let params = {
    pageNo: pageNo.value,
    pageSize: pageNo.value == 1 ? 11 : pageSize.value,
    name: searchName.value,
    startTime: startTime,
    endTime: endTime,
  }
  const res = await SignProjectList(params) as any
  if (res.data.code == 0 && res.data.success) {
    console.log(res.data)
    list.value = res.data.data.map((item: any) => {
      return { ...item, endDate: item.endTime.split(' ')[0] }
    })
    total.value = Number(res.data.totalDatas)
  }
}
onMounted(() => {
  let permission = JSON.parse((sessionStorage.getItem('userInfo') as any))
  let permissionList = permission.permissions
  isHaveSign.value = permissionList.includes('sign_task_center')
  let screenSize: any = ''
  screenSize = document.body.clientWidth
  jd.value = router.currentRoute.value.query.jd
  bureauId.value = router.currentRoute.value.query.bureauId
  let teneantId = JSON.parse(localStorage.getItem('tempTenant') as any)
  if (teneantId) {
    tenantID.value = (JSON.parse(localStorage.getItem('tempTenant') as any)).id || 0
  }
  if (screenSize > 1680) {
    styleType.value = 1
  }
  else {
    styleType.value = 2
  }
  getList()
})

//#endregion


</script>

<style lang='scss' scoped>
.signProject {
  padding: 16px 24px;
  min-height: calc(100vh - 60px);

  .topHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .head_right {
      color: #007aff;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 14px;

      span {
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }

  .popoverRule {
    .ruleItem {
      height: 36px;
      line-height: 36px;
    }
  }

  .header_search {
    height: 84px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    display: flex;
    align-items: center;

    .searchItem {
      margin-left: 58px
    }

    .searchItem1 {
      margin-left: 20px;

    }
  }

  .projectList {
    margin-top: 24px;
    display: grid;
    grid-template-columns: calc(33% - 16px) calc(33% - 16px) calc(34% - 16px);
    grid-gap: 24px;

    .create {
      border: 1px solid #D9D9D9;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      background: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      height: 222px;
      cursor: pointer;
    }

    .listItem {
      background: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      height: 222px;
      cursor: pointer;
      position: relative;

      .type {
        position: absolute;
        right: 0;
        top: 0;
        width: 68px;
        height: 24px;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 0px 4px 0px 8px;
      }

      .blueType {
        background: #007AFF;
      }

      .yellowType {
        background: #FEAB2B
      }

      .signName {
        width: 60%;
        font-weight: bold;
        font-size: 16px;
        padding: 20px 20px 0 20px;
      }

      .signDate {
        margin-top: 16px;
        padding: 0 20px;
      }

      .rules {
        margin-top: 12px;
        padding: 0 20px;


      }

      .signTime {
        margin-top: 12px;
        display: flex;
        align-items: center;
        padding: 0 20px;

        .flexTime {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }

      .userInfo {
        margin-top: 17px;
        height: 54px;
        background: #FFFFFF;
        border-top: 1px solid #e5e5e5;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          width: 54%;
          display: flex;
          align-items: center;

          .userName {
            width: 38%
          }
        }

        .right {
          width: 20px;
          height: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px
        }
      }
    }
  }

  .projectList1 {
    display: grid;
    grid-template-columns: calc(25% - 18px) calc(25% - 18px) calc(25% - 18px) calc(25% - 18px);
    grid-gap: 24px;
  }

  .pagation {
    margin-top: 24px;
    width: 100%;
    text-align: center;
  }
}
</style>
<style lang="scss">
.signProject {
  .popoverSetBtn {
    .ant-popover-inner-content {
      padding: 0;

      .setBtn {
        height: 36px;
        padding: 7px 12px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          background: #F5F5F5;
        }
      }

    }
  }

  .popoverShare {
    .ant-popover-inner-content {
      padding: 0;

      .shareBox {
        background: #FFFFFF;
        box-shadow: 0px 4px 8px 0px rgba(51, 51, 51, 0.12);
        border-radius: 4px 4px 4px 4px;
        padding: 24px;
        display: flex;
        align-items: flex-start;

        .leftQr {
          // width: 128px;
          // height: 128px;
          // border-radius: 4px 4px 4px 4px;
          // border: 1px solid #E5E5E5;
          // display: flex;
          // justify-content: center;
          // align-items: center
          border: 1px solid #E5E5E5;
          border-radius: 4px 4px 4px 4px;
          padding: 6px;
        }

        .rightSet {
          margin-left: 16px;
          width: 460px;

          .title {
            font-weight: bold;
            font-size: 14px
          }

          .signLink {
            margin-top: 15px
          }

          .btnSetBox {
            margin-top: 15px;

            button {
              &:last-child {
                margin-left: 8px
              }
            }
          }
        }
      }
    }

  }
}

.ant-image-preview-mask {
  z-index: 5000 !important;
}

.ant-image-preview-wrap {
  z-index: 6000 !important
}
</style>
