<template>
    <a-drawer :visible="show" width="720px" class="createDrawer" placement="right">
        <div class="header">
            <span>{{ editSignId ? '编辑培训项目' : '创建培训项目' }}</span>
            <ysIcon type="icon-close" class="cursor" @click="close" style="font-size:16px" />
        </div>
        <div class="content scrollbar">
            <div class="steps">
                <a-steps :current="homePage">
                    <a-step v-for="item in steps" :key="item.title" :title="item.title" />
                </a-steps>
            </div>
            <div class="step-one" v-show="homePage == 0">
                <!-- <onePage /> -->
                <div class="form-item">
                    <div class="labels">
                        <div class="star">*</div>
                        <div>项目名称：</div>
                    </div>
                    <a-input v-model:value="projectName" placeholder="请输入" style="width:440px" />
                </div>
                <div class="form-item">
                    <div class="labels">
                        <div class="star">*</div>
                        <div>项目时间段：</div>
                    </div>
                    <a-range-picker valueFormat="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" v-model:value="projectTime"
                        show-time style="width:440px">
                        <template #suffixIcon>
                            <SmileOutlined />
                            <ClockCircleOutlined />
                        </template>
                    </a-range-picker>
                </div>
                <div class="form-item">
                    <div class="labels">
                        <div class="star">*</div>
                        <div>项目类型：</div>
                    </div>
                    <a-radio-group v-model:value="projectType" name="radioGroup">
                        <a-radio value="1">活动</a-radio>
                        <a-radio value="2">培训</a-radio>
                        <a-radio value="3">会议</a-radio>
                    </a-radio-group>
                </div>
                <div class="form-item">
                    <div class=labels>
                        <div>是否开放报名：</div>
                    </div>
                    <a-radio-group v-model:value="isSignUp" name="radioGroup">
                        <a-radio value="1">开放</a-radio>
                        <a-radio value="0">关闭</a-radio>
                    </a-radio-group>
                </div>

                <div class="form-item" v-if="isSignUp == '1'">
                    <div class="labels">
                        <div class="star">*</div>
                        <div>截止报名时间：</div>
                    </div>
                    <a-date-picker v-model:value="endSignTime" style="width:316px" valueFormat="YYYY-MM-DD HH:mm"
                        format="YYYY-MM-DD HH:mm" show-time placeholder="请选择报名截止时间" />
                </div>

            </div>
            <div class="step-two" v-show="homePage == 1">
                <div class="form-item">
                    <div class="labels labels1">
                        <div class="star">*</div>
                        <div>关联签到任务：</div>
                    </div>
                    <a-select v-model:value="signTask" @select="onSelectTask" show-search placeholder="请选择"
                        style="width: 316px" :filter-option="filterOption">
                        <a-select-option :value="item.signTaskBaseId" :label="item.taskName" v-for="item in options">{{
                            item.taskName }}</a-select-option>
                    </a-select>
                    <a-button style="margin-left:8px" @click="goSignWork" v-if="isHaveSign">创建签到项目</a-button>
                    <div class="signTitle">关联之前一定要在签到工具里完成创建</div>
                </div>
                <div class="form-item form-item2">
                    <div class="labels">
                        <div>主讲教师：</div>
                    </div>
                    <a-button type="dashed" @click="openAddTeacher">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        添加</a-button>
                    <a-drawer :visible="showTeacher" class="teacherDrawer" width="1080">
                        <div class="header">
                            <span>添加教师</span>
                            <ysIcon type="icon-close" class="cursor" @click="closeTeacherDrawer"
                                style="font-size:16px" />
                        </div>
                        <div class="content scrollbar">
                            <div class="content_left">
                                <div class="content_header">
                                    <a-input-search allowClear v-model:value="searchTeacherName"
                                        placeholder="姓名/手机号/身份证号" style="width: 240px" @search="onSearch" />
                                </div>
                                <div class="content_table">
                                    <a-table :columns="columns" row-key="coreUserInfoId" :data-source="teacherList"
                                        :pagination="false"
                                        :rowSelection="{ selectedRowKeys, onChange: onSelectRowChange, onSelect: onChooseChange, onSelectAll: onChooseAllChange }">
                                        <template #bodyCell="{ column, text, record }">
                                            <template v-if="column.dataIndex === 'userName'">
                                                <span :style="{ color: record.sex == 1 ? '#FF56AF' : '#007AFF' }">{{
                                                    record.userName }}</span>
                                            </template>
                                            <template v-if="column.dataIndex == 'phone'">
                                                <span>
                                                    {{ record.phone ? infoHidden(1, record.phone) : '--' }}
                                                </span>
                                            </template>
                                            <template v-if="column.dataIndex == 'idCard'">
                                                <span>
                                                    {{ record.idCard ? infoHidden(2, record.idCard) : '--' }}
                                                </span>
                                            </template>
                                            <template v-if="column.dataIndex == 'userType'">
                                                <span>
                                                    {{ record.userType == '1002' ? '教师' : '学生' }}
                                                </span>
                                            </template>
                                            <template v-if="column.dataIndex == 'orgName'">
                                                <span>
                                                    {{ record.orgName ? record.orgName : '--' }}
                                                </span>
                                            </template>
                                        </template>
                                    </a-table>
                                    <ysPagination :pageNo="pageNo" :pageSize="pageSize" :total="total"
                                        @change="handlePaginationChange" />
                                </div>
                            </div>
                            <div class="content_right scrollbar">
                                <div class="right_header">
                                    <span style="font-weight:bold">已选&nbsp;{{ selectedRowKeys.length > 0 ?
                                        selectedRowKeys.length : null
                                        }}</span>
                                    <span class="cursor" @click="delAll">清空</span>
                                </div>
                                <div class="choose">
                                    <div class="teacherItem" v-for="(item, index) in chooseTeacherList" :key="index">
                                        <span class="name text-overflow"
                                            :style="{ color: item.sex == 1 ? '#FF56AF' : '#007AFF' }">{{
                                                item.userName
                                            }}</span>
                                        <span class="delItem" @click="delItem(index)">X</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=teacherFooter>
                            <a-button @click="closeTeacherDrawer">取消</a-button>
                            <a-button type="primary" style="margin-left:8px" @click="sureAddTeacher">确定</a-button>
                        </div>
                    </a-drawer>
                </div>
                <div class="form-item addPersonBox" v-if="alreadyTeacher.length > 0">
                    <div class="teacherItem" v-for="(item, index) in alreadyTeacher" :key="index">
                        <span>{{ item.userName }}</span>
                        <span style="margin-left: 6px" class="cursor" @click="delTeacherItem(index)">
                            <ysIcon type="icon-close" />
                        </span>
                    </div>
                </div>
                <div class="form-item">
                    <div class="labels">
                        <div class="star">*</div>
                        <div>培训级别：</div>
                    </div>
                    <a-select v-model:value="level" placeholder="请选择" style="width: 316px">
                        <a-select-option :value="item.id" v-for="item in tringLevelList">{{ item.name
                            }}</a-select-option>
                    </a-select>
                </div>
            </div>
            <div class="step-three" v-show="homePage == 2">
                <twoPage ref="twoPageRef" :topLevel='topLevel' :projectType="type" :xueyuanxueshi="xueyuanxueshi"
                    :xueyuandefer="xueyuandefer" :zhujiangrendefer="zhujiangrendefer" :xueyuanxuefen="xueyuanxuefen" :zhujiangrenxueshi="zhujiangrenxueshi"
                    :zhujiangrenxuefen="zhujiangrenxuefen" :speaker="alreadyTeacher.length > 0 ? true : false" />
            </div>
        </div>
        <div class='footer'>
            <a-button @click="close">取消</a-button>
            <a-button v-if="homePage != 0" @click="preHomePage">上一步</a-button>
            <a-button v-if="homePage == 0 || homePage == 1" type="primary" @click="nextHomePage">下一步</a-button>
            <a-button type="primary" v-if="homePage > 1" @click="finish">完成</a-button>
        </div>
    </a-drawer>
</template>

<script lang='ts' setup>
import { ysPagination } from "@ys/ui"
import { infoHidden } from "@/utils";
import onePage from "./onePage.vue"
import twoPage from "./twoPage.vue"
import { ysIcon } from '@ys/ui';
import { ref, nextTick, onMounted } from 'vue'
import { useRouter } from "vue-router";
import { common } from "@ys/tools";
import { PlusOutlined, ClockCircleOutlined } from "@ant-design/icons-vue"
import { message } from "ant-design-vue";
import { getTeacher, eduSignList, editSignProject, eduSignDetail, doUserOrgLevel } from "@/request"
/* --------------- data --------------- */
//#region
const { bureauBlankOpen } = common
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    createType: {
        type: String || Number,
        default: 1
    },
    editSignId: {
        type: String,
        default: null
    },
    isHaveSign: {
        type: Boolean,
        default: true
    }
})

const router = useRouter()
const steps = ref<any>([
    {
        title: '创建培训项目',
    },
    {
        title: '关联签到项目'
    },
    {
        title: '制定学时规则',
    }
])
const isSignUp = ref<any>('1')
const projectType = ref<any>('1')
const projectTime = ref<any>([])
const searchTeacherName = ref<any>(null)
const signlist = ref<any>([])
const projectName = ref<any>(null)
const homePage = ref<any>(0)
const emit = defineEmits(['close'])
const signTask = ref<any>(null)
const options = ref<any>([])
const teacherList = ref<any>([])
const tringLevelList = ref([
    { id: 1, name: "国家级" },
    { id: 2, name: "省级" },
    { id: 3, name: "市级" },
    { id: 4, name: "县级" },
    { id: 5, name: "校级" },
]);
const level = ref<any>(null)
const twoPageRef = ref<any>()
const type = ref(1)
const showTeacher = ref(false)
const selectedRowKeys = ref<any>([])
const pageNo = ref(1)
const pageSize = ref(10)
const total = ref(1)
const columns = ref<any>([
    {
        title: '姓名',
        dataIndex: 'userName',
        width: '140px',
        ellipsis: true
    },
    {
        title: '手机号',
        dataIndex: 'phone',
    },
    {
        title: '身份证号',
        dataIndex: 'idCard',
    },
    {
        title: '人员类型',
        dataIndex: 'userType',
    },
    {
        title: '组织机构',
        dataIndex: 'orgName',
        ellipsis: true
    },
])
const chooseTeacherList = ref<any>([])
const alreadyTeacher = ref<any>([])
const chooseTaskId = ref<any>(null)
const xueyuanxueshi = ref<any>([])
const xueyuanxuefen = ref<any>([])
const xueyuandefer = ref<any>([])
const zhujiangrendefer = ref<any>([])
const zhujiangrenxueshi = ref<any>([])
const zhujiangrenxuefen = ref<any>([])
const endSignTime = ref<any>(null)
const topLevel = ref<any>(true)
//#endregion

/* --------------- methods --------------- */
//#region
const onSelectTask = (val: any) => {
    chooseTaskId.value = val
    for (let i of options.value) {
        if (i.signTaskBaseId == val) {
            type.value = i.signType
        }
    }
}
const delAll = () => {
    selectedRowKeys.value = []
    chooseTeacherList.value = []
}
const delItem = (num: any) => {
    selectedRowKeys.value.splice(num, 1)
    chooseTeacherList.value.splice(num, 1)
}
const delTeacherItem = (num: any) => {
    alreadyTeacher.value.splice(num, 1)
}
const handlePaginationChange = (paNo: any, paSize: any) => {
    pageNo.value = paNo
    pageSize.value = paSize
    getTeacherList()

}
const uniqueArray = (arr: any) => {
    return [...new Set(arr)];
}
// 手动选中/取消所有列
const onChooseAllChange = (selected: any, selectedRows: any, changeRows: any) => {
    if (!selected) {
        for (let i = 0; i < selectedRowKeys.value.length; i++) {
            for (let j = 0; j < changeRows.length; j++) {
                if (selectedRowKeys.value[i] == changeRows[j].coreUserInfoId) {
                    selectedRowKeys.value.splice(i, 1)
                    chooseTeacherList.value.splice(i, 1)
                    i--
                }
            }
        }
    }
    else {
        for (let i = 0; i < selectedRows.length; i++) {
            for (let j = 0; j < changeRows.length; j++) {
                if (selectedRows[i] != undefined) {
                    if (selectedRows[i].coreUserInfoId == changeRows[j].coreUserInfoId) {
                        selectedRowKeys.value.push(selectedRows[i].coreUserInfoId)
                        chooseTeacherList.value.push(selectedRows[i])
                    }

                }
            }
        }
    }
}

// 手动选中/取消单列
const onChooseChange = (rows: any, boo: any) => {
    if (!boo) {
        for (let i = 0; i < selectedRowKeys.value.length; i++) {
            if (selectedRowKeys.value[i] == rows.coreUserInfoId) {
                selectedRowKeys.value.splice(i, 1)
                chooseTeacherList.value.splice(i, 1)
            }
        }

    }
    else {
        selectedRowKeys.value.push(rows.coreUserInfoId)
        chooseTeacherList.value.push(rows)
    }

}
// 表格勾选事件
const onSelectRowChange = (data: string[], rows: any) => {
    // data.forEach(item => selectedRowKeys.value.push(item))
    // selectedRowKeys.value = uniqueArray(selectedRowKeys.value)

};
const sureAddTeacher = () => {
    if (chooseTeacherList.value.length > 30) {
        message.error('最多选择30位主讲人')
        return false
    }
    else {
        alreadyTeacher.value = chooseTeacherList.value
        showTeacher.value = false
        nextTick(() => {
            chooseTeacherList.value = []
            selectedRowKeys.value = []
        })
    }


}
const closeTeacherDrawer = () => {
    showTeacher.value = false
    chooseTeacherList.value = []
    selectedRowKeys.value = []
}
const getTeacherList = async () => {
    let params = {
        keyword: searchTeacherName.value,
        pageIndex: pageNo.value,
        pageSize: pageSize.value,
        orgId: null,
        keywordType: searchTeacherName.value ? 4 : null
    }
    const res = await getTeacher(params) as any
    if (res.data.code == 0 && res.data.success) {
        teacherList.value = res.data.data
        total.value = Number(res.data.totalDatas)
    }

}
const openAddTeacher = () => {
    showTeacher.value = true
    alreadyTeacher.value.forEach((item: any) => selectedRowKeys.value.push(item.coreUserInfoId))
    selectedRowKeys.value = uniqueArray(selectedRowKeys.value)
    alreadyTeacher.value.forEach((item: any) => chooseTeacherList.value.push(item))
    chooseTeacherList.value = uniqueArray(chooseTeacherList.value)
    getTeacherList()

}
const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
async function onSearch() {
    pageNo.value = 1
    getTeacherList()

}
const preHomePage = () => {
    homePage.value -= 1
}
const nextHomePage = () => {
    if (homePage.value == 0) {
        if (!projectName.value) {
            message.error('请输入项目名称')
            return false
        }

        else if (projectTime.value.length == 0) {
            message.error('请选择项目时间段')
            return false
        }
        else if (isSignUp.value == '1' && !endSignTime.value) {
            message.error('请填写截止报名时间')
            return false
        }
        else {
            console.log(333)
            homePage.value += 1
        }
    }
    else if (homePage.value == 1) {
        console.log('关联签到项目')
        if (!signTask.value) {
            message.error('请关联签到任务')
            return false
        }
        else if (!level.value) {
            message.error('请选择培训级别')
            return false
        }
        else {
            homePage.value += 1
        }
    }


}

const close = () => {
    projectName.value = null
    signTask.value = null
    teacherList.value = []
    level.value = null
    emit('close')
}
const checkSignTimeDuplicates = (val: any) => {
    let status = true
    const seenSignTimes = new Map();
    const hasDuplicates = val.some((item: any) => {
        if (seenSignTimes.has(item.signTime)) {
            console.log('重复的 signTime 值:', item.signTime);
            return true;
        } else {
            seenSignTimes.set(item.signTime, true);
            return false;
        }
    });
    if (hasDuplicates) {
        status = false
    } else {
        status = true
    }
    return status
}
const finish = () => {
    let list1 = twoPageRef.value.list1
    let list3 = twoPageRef.value.list3
    let list2 = twoPageRef.value.list2
    let list4 = twoPageRef.value.list4
    let studyRuleType = twoPageRef.value.studyRuleType
    let studyRuleType1 = twoPageRef.value.studyRuleType1
    let gradeRuleType = twoPageRef.value.gradeRuleType
    let gradeRuleType1 = twoPageRef.value.gradeRuleType1
    let studentCheck = twoPageRef.value.studentCheck
    let teacherCheck = twoPageRef.value.teacherCheck
    let type2List1 = []
    let type2List2 = []
    let type2List3 = []
    let type2List4 = []

    let type2StudentCheck = 1
    let type2teacherCheck = 1
    let noChooseCycle = false
    let type2studyRuleType = 1
    let type2studyRuleType1 = 1
    let type2gradeRuleType = 1
    let type2gradeRuleType1 = 1
    let deferRuleCheck1 = twoPageRef.value.deferCheck1
    let deferList1 = twoPageRef.value.deferList1
    let mainDecisionTime1 = twoPageRef.value.mainDecisionTime1
    let deferRuleCheck2 = twoPageRef.value.deferCheck2
    let deferList2 = twoPageRef.value.deferList2
    let mainDecisionTime2 = twoPageRef.value.mainDecisionTime2
    let type2deferRuleCheck1 = false
    let type2deferList1 = []
    let type2mainDecisionTime1 = null
    let type2deferRuleCheck2 = false
    let type2deferList2 = []
    let type2mainDecisionTime2 = null
    if (twoPageRef.value.cyclicSignRef) {
        type2List1 = twoPageRef.value.cyclicSignRef.list1.map((item: any) => {
            return { ...item, zhouqi: [] }
        })
        type2List2 = twoPageRef.value.cyclicSignRef.list2.map((item: any) => {
            return { ...item, zhouqi: [] }
        })
        type2List3 = twoPageRef.value.cyclicSignRef.list3.map((item: any) => {
            return { ...item, zhouqi: [] }
        })
        type2List4 = twoPageRef.value.cyclicSignRef.list4.map((item: any) => {
            return { ...item, zhouqi: [] }
        })
        type2StudentCheck = twoPageRef.value.cyclicSignRef.studentCheck
        type2teacherCheck = twoPageRef.value.cyclicSignRef.teacherCheck
        type2studyRuleType = twoPageRef.value.cyclicSignRef.studyRuleType
        type2studyRuleType1 = twoPageRef.value.cyclicSignRef.studyRuleType1
        type2gradeRuleType = twoPageRef.value.cyclicSignRef.gradeRuleType
        type2gradeRuleType1 = twoPageRef.value.cyclicSignRef.gradeRuleType1
        type2deferList1 = twoPageRef.value.cyclicSignRef.deferList1
        type2deferList2 = twoPageRef.value.cyclicSignRef.deferList2
        type2deferRuleCheck1 = twoPageRef.value.cyclicSignRef.deferCheck1
        type2deferRuleCheck2 = twoPageRef.value.cyclicSignRef.deferCheck2
        type2mainDecisionTime1 = twoPageRef.value.cyclicSignRef.mainDecisionTime1
        type2mainDecisionTime2 = twoPageRef.value.cyclicSignRef.mainDecisionTime2

    }
    if (type.value == 1) {
        if (list1.length == 0) {
            message.error('请填写学员得到学时规则')
            return false
        }
        if (alreadyTeacher.value.length > 0) {
            if (list3.length == 0) {
                message.error('请填写主讲教师得到学时规则')
                return false
            }
            let isList3Repeat = checkSignTimeDuplicates(list3)
            if (!isList3Repeat) {
                message.error('主讲人得到学时规则的签到次数不能重复')
                return false
            }
        }

        let islist1Null = list1.every((item: any) => {
            return item.signTime != null && item.classHour != null
        })
        let islist2Null = list2.every((item: any) => {
            return item.signTime != null && item.classHour != null
        })
        let islist3Null = list3.every((item: any) => {
            return item.signTime != null && item.classHour != null
        })
        let islist4Null = list4.every((item: any) => {
            return item.signTime != null && item.classHour != null
        })
        if (!islist1Null) {
            message.error('请填写完整学员得到学时规则')
            return false
        }
        if (deferRuleCheck1) {
            if (mainDecisionTime1 == null) {
                message.error('请填写学员迟到规则主判定时间')
                return false
            }
            if (deferList1.length == 0) {
                message.error('请填写学员迟到规则')
                return false
            }
            let isdeferlist1Null = deferList1.every((item: any) => {
                return item.signTime != null && item.classHour != null
            })
            if (!isdeferlist1Null) {
                message.error('请填写完整学员迟到规则')
                return false
            }
            let isdeferlist1Repeat = checkSignTimeDuplicates(deferList1)
            if (!isdeferlist1Repeat) {
                message.error('学员迟到规则不能重复')
                return false
            }
        }
        if (deferRuleCheck2) {
            if (mainDecisionTime2 == null) {
                message.error('请填写主讲人迟到规则主判定时间')
                return false
            }
            if (deferList2.length == 0) {
                message.error('请填写主讲人迟到规则')
                return false
            }
            let isdeferlist2Null = deferList2.every((item: any) => {
                return item.signTime != null && item.classHour != null
            })
            if (!isdeferlist2Null) {
                message.error('请填写完整主讲人迟到规则')
                return false
            }
            let isdeferlist2Repeat = checkSignTimeDuplicates(deferList2)
            if (!isdeferlist2Repeat) {
                message.error('主讲人迟到规则不能重复')
                return false
            }
        }
        let isList1Repeat = checkSignTimeDuplicates(list1)
        if (!isList1Repeat) {
            message.error('学员得到学时规则的签到次数不能重复')
            return false
        }
        if (!islist3Null) {
            message.error('请填写完整主讲教师得到学时规则')
            return false
        }

        if (studentCheck) {
            if (list2.length == 0) {
                message.error('请填写学员得到学分规则')
                return false
            }
            else {
                if (!islist2Null) {
                    message.error('请填写完整学员得到学分规则')
                    return false
                }
                let isList2Repeat = checkSignTimeDuplicates(list2)
                if (!isList2Repeat) {
                    message.error('学员得到学分规则的签到次数不能重复')
                    return false
                }
            }
        }

        if (teacherCheck) {
            if (list4.length == 0) {
                message.error('请填写学员得到学分规则')
                return false
            }
            else {
                if (!islist4Null) {
                    message.error('请填写完整学员得到学分规则')
                    return false
                }
                let isList4Repeat = checkSignTimeDuplicates(list4)
                if (!isList4Repeat) {
                    message.error('主讲人得到学分规则的签到次数不能重复')
                    return false
                }
            }
        }
    }
    else {
        if (type2List1.length == 0) {
            message.error('请正确填写学员得到学时规则')
            return false
        }
        if (type2deferRuleCheck1) {
            if (type2mainDecisionTime1 == null) {
                message.error('请填写学员迟到规则主判定时间')
                return false
            }
            if (type2deferList1.length == 0) {
                message.error('请填写学员迟到规则')
                return false
            }
            let istype2deferlist1Null = type2deferList1.every((item: any) => {
                return item.signTime != null && item.classHour != null
            })
            if (!istype2deferlist1Null) {
                message.error('请填写完整学员迟到规则')
                return false
            }
            let istype2deferlist1Repeat = checkSignTimeDuplicates(type2deferList1)
            if (!istype2deferlist1Repeat) {
                message.error('学员迟到规则不能重复')
                return false
            }
        }

        if (type2StudentCheck && type2List2.length == 0) {
            message.error('请正确填写学员得到学分规则')
            return false
        }
        if (alreadyTeacher.value.length > 0) {
            if (type2List3.length == 0) {
                message.error('请正确填写主讲人得到学时规则')
                return false
            }
            if (type2teacherCheck && type2List4.length == 0) {
                message.error('请正确填写主讲人得到学分规则')
                return false
            }
        }

        if (type2List1.length > 0) {
            for (let i of type2List1) {
                noChooseCycle = i.weeks.some((item: any) => {
                    return item.isChoose != false
                })
                if (!noChooseCycle) {
                    message.error('请正确填写学员得到学时规则')
                    return false
                }
                if (i.rules.length == 0) {
                    message.error('请正确填写学员得到学时规则')
                    return false
                }
                let arr = [...i.rules]
                let type2Islist1Null = arr.every((item: any) => {
                    return item.signTime != null && item.classHour != null
                })

                if (!type2Islist1Null) {
                    message.error('请正确填写学员得到学时规则')
                    return false
                }
                let istype2List1Repeat = checkSignTimeDuplicates(arr)
                if (!istype2List1Repeat) {
                    message.error('学员得到学时规则的签到次数不能重复')
                    return false
                }
            }
        }
        if (type2List2.length > 0 && type2StudentCheck) {
            for (let i of type2List2) {
                noChooseCycle = i.weeks.some((item: any) => {
                    return item.isChoose != false
                })
                if (!noChooseCycle) {
                    message.error('请正确填写学员得到学分规则')
                    return false
                }
                if (i.rules.length == 0) {
                    message.error('请正确填写学员得到学分规则')
                    return false
                }
                let arr = [...i.rules]
                let type2Islist2Null = arr.every((item: any) => {
                    return item.signTime != null && item.classHour != null
                })
                if (!type2Islist2Null) {
                    message.error('请正确填写学员得到学分规则')
                    return false
                }
                let istype2List2Repeat = checkSignTimeDuplicates(arr)
                if (!istype2List2Repeat) {
                    message.error('学员得到学分规则的签到次数不能重复')
                    return false
                }
            }
        }
        if (type2List3.length > 0) {
            for (let i of type2List3) {
                noChooseCycle = i.weeks.some((item: any) => {
                    return item.isChoose != false
                })
                if (!noChooseCycle) {
                    message.error('请正确填写主讲人得到学时规则')
                    return false
                }
                if (i.rules.length == 0) {
                    message.error('请正确填写主讲人得到学时规则')
                    return false
                }
                let arr = [...i.rules]
                let type2Islist1Null = arr.every((item: any) => {
                    return item.signTime != null && item.classHour != null
                })
                if (!type2Islist1Null) {
                    message.error('请正确填写主讲人得到学时规则')
                    return false
                }
                let istype2List3Repeat = checkSignTimeDuplicates(arr)
                if (!istype2List3Repeat) {
                    message.error('主讲人得到学时规则的签到次数不能重复')
                    return false
                }

            }
        }
        if (type2deferRuleCheck2) {
            if (type2mainDecisionTime2 == null) {
                message.error('请填写主讲人迟到规则主判定时间')
                return false
            }
            if (type2deferList2.length == 0) {
                message.error('请填写主讲人迟到规则')
                return false
            }
            let istype2deferlist2Null = type2deferList2.every((item: any) => {
                return item.signTime != null && item.classHour != null
            })
            if (!istype2deferlist2Null) {
                message.error('请填写完整主讲人迟到规则')
                return false
            }
            let istype2deferlist2Repeat = checkSignTimeDuplicates(type2deferList2)
            if (!istype2deferlist2Repeat) {
                message.error('主讲人迟到规则不能重复')
                return false
            }
        }
        if (type2List4.length > 0 && type2teacherCheck) {
            for (let i of type2List4) {
                noChooseCycle = i.weeks.some((item: any) => {
                    return item.isChoose != false
                })
                if (!noChooseCycle) {
                    message.error('请正确填写主讲人得到学分规则')
                    return false
                }
                if (i.rules.length == 0) {
                    message.error('请正确填写主讲人得到学分规则')
                    return false
                }
                let arr = [...i.rules]
                let type2Islist2Null = arr.every((item: any) => {
                    return item.signTime != null && item.classHour != null
                })
                if (!type2Islist2Null) {
                    message.error('请正确填写主讲人得到学分规则')
                    return false
                }
                let istype2List4Repeat = checkSignTimeDuplicates(arr)
                if (!istype2List4Repeat) {
                    message.error('主讲人得到学分规则的签到次数不能重复')
                    return false
                }
            }
        }

    }

    let arr = []
    if (type.value == 1) {
        for (let i of list1) {
            arr.push({
                signTime: i.signTime,
                classHour: i.classHour,
                userType: 1,
                signType: studyRuleType,
                ruleType: type.value,
                scoreType: 1
            })
        }
        if (studentCheck) {
            for (let i of list2) {
                arr.push({
                    signTime: i.signTime,
                    classHour: i.classHour,
                    userType: 1,
                    signType: gradeRuleType,
                    openScore: studentCheck ? 1 : 2,
                    ruleType: type.value,
                    scoreType: 2
                })
            }
        }
        if (deferRuleCheck1) {
            for (let i of deferList1) {
                arr.push({
                    signTime: i.signTime,
                    classHour: i.classHour,
                    userType: 1,
                    signType: studyRuleType,
                    ruleType: type.value,
                    scoreType: 3,
                    openLate: deferRuleCheck1 ? 1 : 2,
                    lateMinute: mainDecisionTime1
                })
            }
        }
        if (alreadyTeacher.value.length > 0) {
            for (let i of list3) {
                arr.push({
                    signTime: i.signTime,
                    classHour: i.classHour,
                    userType: 2,
                    signType: studyRuleType1,
                    ruleType: type.value,
                    scoreType: 1
                })
            }
            if (teacherCheck) {
                for (let i of list4) {
                    arr.push({
                        signTime: i.signTime,
                        classHour: i.classHour,
                        userType: 2,
                        signType: gradeRuleType1,
                        ruleType: type.value,
                        scoreType: 2,
                        openScore: teacherCheck ? 1 : 2,
                    })
                }
            }
            if (deferList2) {
                for (let i of deferList2) {
                    arr.push({
                        signTime: i.signTime,
                        classHour: i.classHour,
                        userType: 2,
                        signType: studyRuleType,
                        ruleType: type.value,
                        scoreType: 3,
                        openLate: deferRuleCheck2 ? 1 : 2,
                        lateMinute: mainDecisionTime2
                    })
                }
            }
        }

    }
    else {
        for (let i of type2List1) {
            for (let j of i.weeks) {
                if (j.isChoose) {
                    i.zhouqi.push(j.id)
                }
            }
        }

        for (let i of type2List1) {
            for (let j of i.rules) {
                arr.push({
                    signTime: j.signTime,
                    classHour: j.classHour,
                    userType: 1,
                    signType: type2studyRuleType,
                    ruleType: type.value,
                    scoreType: 1,
                    days: i.zhouqi.join(','),

                })
            }

        }
        if (type2StudentCheck) {
            for (let i of type2List2) {
                for (let j of i.weeks) {
                    if (j.isChoose) {
                        i.zhouqi.push(j.id)
                    }
                }
            }

            for (let i of type2List2) {
                for (let j of i.rules) {
                    arr.push({
                        signTime: j.signTime,
                        classHour: j.classHour,
                        userType: 1,
                        signType: type2gradeRuleType,
                        ruleType: type.value,
                        scoreType: 2,
                        openScore: type2StudentCheck ? 1 : 2,
                        days: i.zhouqi.join(','),

                    })
                }

            }
        }
        if (type2deferRuleCheck1) {
            for (let j of type2deferList1) {
                arr.push({
                    signTime: j.signTime,
                    classHour: j.classHour,
                    userType: 1,
                    signType: type2studyRuleType,
                    ruleType: type.value,
                    scoreType: 3,
                    openLate: type2deferRuleCheck1 ? 1 : 2,
                    lateMinute: type2mainDecisionTime1
                })
            }
        }
        if (alreadyTeacher.value.length > 0) {
            for (let i of type2List3) {
                for (let j of i.weeks) {
                    if (j.isChoose) {
                        i.zhouqi.push(j.id)
                    }
                }
            }
            for (let i of type2List3) {
                for (let j of i.rules) {
                    arr.push({
                        signTime: j.signTime,
                        classHour: j.classHour,
                        userType: 2,
                        signType: type2studyRuleType1,
                        ruleType: type.value,
                        scoreType: 1,
                        days: i.zhouqi.join(','),

                    })
                }
            }
            if (type2teacherCheck) {
                for (let i of type2List4) {
                    for (let j of i.weeks) {
                        if (j.isChoose) {
                            i.zhouqi.push(j.id)
                        }
                    }
                }
                for (let i of type2List4) {
                    for (let j of i.rules) {
                        arr.push({
                            signTime: j.signTime,
                            classHour: j.classHour,
                            userType: 2,
                            signType: type2gradeRuleType1,
                            ruleType: type.value,
                            scoreType: 2,
                            openScore: type2teacherCheck ? 1 : 2,
                            days: i.zhouqi.join(','),
                        })
                    }

                }
            }
            if (type2deferRuleCheck2) {
                for (let j of type2deferList2) {
                    arr.push({
                        signTime: j.signTime,
                        classHour: j.classHour,
                        userType: 2,
                        signType: type2studyRuleType,
                        ruleType: type.value,
                        scoreType: 3,
                        openLate: type2deferRuleCheck2 ? 1 : 2,
                        lateMinute: type2mainDecisionTime2
                    })
                }
            }
        }
    }
    let teachArr: any = []
    if (alreadyTeacher.value.length > 0) {
        alreadyTeacher.value.forEach((item: any) => {
            teachArr.push(item.coreUserInfoId)
        })
    }
    const statrTime = projectTime.value[0]
    const endTime = projectTime.value[1]

    let params = {
        endSignTime: isSignUp.value == '1' ? endSignTime.value : null,
        resourceName: projectName.value,
        singId: chooseTaskId.value,
        eduSignResourceId: props.editSignId ? props.editSignId : null,
        trainLevel: level.value,
        signRulesList: arr,
        hostTeacherId: teachArr && teachArr.length > 0 ? teachArr : null,
        activityType: projectType.value,
        isOpenSign: isSignUp.value,
        startTime: statrTime,
        endTime: endTime,
    }
    sureSave(params)

}
const sureSave = async (val: any) => {
    const res = await editSignProject(val) as any
    if (res.data.code == 0 && res.data.success) {
        message.success('操作完成')
        emit('close')

    }
}
const goSignWork = () => {
    bureauBlankOpen('/yskt/ys/sign/#/signIndex?objectType=2000')
}
const getSignList = async () => {
    const res = await eduSignList({ name: signTask.value }) as any
    if (res.data.code == 0 && res.data.success) {
        options.value = res.data.data.map((item: any) => {
            return {
                ...item,
                label: item.taskName,
                value: item.signTaskBaseId
            }
        })
    }
}
const getDetails = async () => {
    const res = await eduSignDetail({ eduSignResourceId: props.editSignId }) as any
    if (res.data.code == 0 && res.data.success) {
        projectName.value = res.data.data.resourceName
        chooseTaskId.value = res.data.data.singId
        signTask.value = res.data.data.taskName
        level.value = res.data.data.trainLevel
        type.value = res.data.data.signType
        endSignTime.value = res.data.data.endSignTime
        projectType.value = String(res.data.data.activityType)
        isSignUp.value = String(res.data.data.isOpenSign)
        projectTime.value = res.data.data.startTime ? [res.data.data.startTime, res.data.data.endTime] : []
        if (res.data.data.teachers && res.data.data.teachers.length > 0) {
            alreadyTeacher.value = res.data.data.teachers.map((item: any) => {
                return { ...item, coreUserInfoId: item.userId, userName: item.userName }
            })
        }
        if (res.data.data.studentRules && res.data.data.studentRules.length > 0) {
            for (let i of res.data.data.studentRules) {
                if (i.scoreType == 1) {
                    xueyuanxueshi.value.push(i)
                }
                else {
                    xueyuanxuefen.value.push(i)
                }
            }
        }
        if (res.data.data.teacherRules && res.data.data.teacherRules.length > 0) {
            for (let i of res.data.data.teacherRules) {
                if (i.scoreType == 1) {
                    zhujiangrenxueshi.value.push(i)
                }
                else {
                    zhujiangrenxuefen.value.push(i)
                }
            }
        }
        if (res.data.data.studentLateRules && res.data.data.studentLateRules.length > 0) {
            xueyuandefer.value = [...res.data.data.studentLateRules]
        }
        if (res.data.data.teacherLateRules && res.data.data.teacherLateRules.length > 0) {
            zhujiangrendefer.value = [...res.data.data.teacherLateRules]
        }

    }
}
const getTopLevel = () => {
    doUserOrgLevel().then((res: any) => {
        if (res.data.data == true) {
            topLevel.value = true
        }
        else {
            topLevel.value = false
        }
    })
}
onMounted(() => {
    console.log('20240805')
    getTopLevel()
    getSignList()
    nextTick(() => {
        if ((props.createType) as any == 2) {
            getDetails()
        }
    })

})
//#endregion


</script>

<style lang='scss' scoped>
.createDrawer {
    .header {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #F0F0F0;
        padding: 0 24px
    }

    .content {
        height: calc(100vh - 120px);
        // padding:  0 24px;

        .steps {
            height: 80px;
            background: #FAFAFA;
            display: flex;
            align-items: center;
            justify-content: center;

            .ant-steps {
                width: 90%;
            }
        }

        .step-one {
            padding: 24px 74px 24px 0;

            .form-item {
                display: flex;
                align-items: center;
                margin-bottom: 24px;
                position: relative;

                .labels {
                    width: 140px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;

                    .star {
                        color: red
                    }
                }

                .labels1 {
                    margin-left: -4px
                }

                .signTitle {
                    position: absolute;
                    bottom: -22px;
                    left: 136px;
                    color: #8C8C8C
                }

            }

            .form-item2 {
                margin-top: 36px;

                button {
                    border-color: #007aff;
                    color: #007aff
                }
            }

            .addPersonBox {
                padding: 12px 16px 4px 16px;
                background: rgba(0, 0, 0, 0.03);
                border-radius: 6px;
                margin-left: 140px;
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                .teacherItem {
                    // height: 28px;
                    background: #FFFFFF;
                    border-radius: 4px 4px 4px 4px;
                    border: 1px solid #E5E5E5;
                    padding: 6px 8px;
                    margin-right: 8px;
                    margin-bottom: 8px
                }
            }
        }

        .step-two {
            padding: 24px 74px 24px 0;

            .form-item {
                display: flex;
                align-items: center;
                margin-bottom: 24px;
                position: relative;

                .labels {
                    width: 140px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;

                    .star {
                        color: red
                    }
                }

                .labels1 {
                    margin-left: -4px
                }

                .signTitle {
                    position: absolute;
                    bottom: -22px;
                    left: 136px;
                    color: #8C8C8C
                }

            }

            .form-item2 {
                margin-top: 36px;

                button {
                    border-color: #007aff;
                    color: #007aff
                }
            }

            .addPersonBox {
                padding: 12px 16px 4px 16px;
                background: rgba(0, 0, 0, 0.03);
                border-radius: 6px;
                margin-left: 140px;
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                .teacherItem {
                    // height: 28px;
                    background: #FFFFFF;
                    border-radius: 4px 4px 4px 4px;
                    border: 1px solid #E5E5E5;
                    padding: 6px 8px;
                    margin-right: 8px;
                    margin-bottom: 8px
                }
            }
        }

        .step-three {
            padding: 24px
        }
    }

    .footer {
        border-top: 1px solid #F0F0F0;
        height: 60px;
        padding: 0 24px;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        button {
            margin-right: 8px;
        }
    }
}

.teacherDrawer {
    .header {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #F0F0F0;
        padding: 0 24px
    }

    .content {
        display: flex;


        .content_left {
            padding: 24px;
            width: 840px;
            border-right: 1px solid #E5E5E5;
            height: calc(100vh - 120px);
            overflow: auto;
        }

        .content_table {
            margin-top: 20px;
        }

        .content_right {
            flex: 1;
            height: calc(100vh - 120px);
            overflow: auto;

            .right_header {
                height: 64px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .choose {

                .teacherItem {
                    padding: 0 20px;
                    height: 44px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .name {
                        max-width: 70%
                    }

                    .delItem {
                        display: none;
                        cursor: pointer;
                    }

                    &:hover {
                        background: #F7F7F7;

                        .delItem {
                            display: block
                        }
                    }
                }
            }
        }
    }

    .teacherFooter {
        padding: 0 16px;
        border-top: 1px solid #e5e5e5;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>
<style lang=scss>
.createDrawer {
    .ant-drawer-header {
        display: none
    }


}

.teacherDrawer {
    .ant-drawer-header {
        display: none
    }
}
</style>
