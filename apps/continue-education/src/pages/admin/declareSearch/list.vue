<template>
  <section class="declare_container" ref="declare_container">
    <SearchHeader
      class="basic_header_search_"
      oneTitle="申报项目"
      oneText="请输入"
      @search="(obj: any) => newSiftSearch(obj)"
      @reSet="handleResourceReset"
    />
    <section class="table_box scrollbars p1624 bcf br4">
      <div class="flex_js mb16" style="height: 36px">
        <span> 申报列表 </span>
        <span v-if="containerType != '1'">
          <a-button
            class="ml8"
            :disabled="
              !rowSelection.selectedRowKeys ||
              !!!rowSelection.selectedRowKeys.length
            "
            @click="handleDel(0, '')"
            >删除</a-button
          >
          <a-button class="ml8" @click="handleExport(5)">导出</a-button>
          <a-button class="ml8" @click="handleRecord">申报记录</a-button>
          <a-button class="ml8" type="primary" @click="handleAddDeclare"
            >资料申报</a-button
          >
        </span>
      </div>
      <a-table
        v-if="reFresh"
        :dataSource="resourceList"
        :columns="columns"
        @change="handleTabSort2"
        :row-selection="rowSelection"
        row-key="eduResourceId"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'resourceName'">
            <span
              class="c07 cursor"
              @click="handleOpenExamineDetail(record.eduResourceId)"
            >
              {{ record.resourceName }}
            </span>
          </template>
          <template v-if="column.dataIndex == 'studyTime'">
            {{ !!record.studyTime ? record.studyTime : 0 }}
          </template>
          <template v-if="column.dataIndex == 'resourceType'">
            {{ record.resourceType == 1 ? "灵活性" : "规范性" }}
          </template>
          <template v-if="column.dataIndex == 'status'">
            <div class="flex_ac">
              <span
                class="spot mr8"
                :style="{
                  backgroundColor: handleStatusSift(record.status).color,
                }"
              ></span>
              {{ handleStatusSift(record.status).text }}
            </div>
          </template>
          <template
            v-if="column.dataIndex == 'operation' && record.status == '2'"
          >
            <span class="cursor c07" @click="handleAuditEdit(record)">编辑</span>
            <a-popconfirm
              title="是否确认删除？"
              ok-text="确定"
              cancel-text="取消"
              :getPopupContainer="() => $refs.declare_container"
              @confirm="handleDel(1, record.eduResourceId)"
            >
              <span class="cursor c07" style="margin-left:8px">删除</span>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
      <ysPagination
        :pageNo="resourceParams.pageNo"
        :pageSize="resourceParams.pageSize"
        :total="resourceTotal"
        @change="handlePageChange"
      />
    </section>
    <Loading :loading="loading" />
  </section>
  <ExamineDetails
    :record="true"
    :showAlert="examineDhowAlert"
    :visible="examineDetailsVisible"
    :id="examineDetailsId"
    :resourceList="resourceList"
    @close="handleCloseExamineDetail"
  />
</template>

<script lang='ts' setup>
import { ref, onMounted, watch, nextTick } from "vue";
import Loading from "@/components/loading.vue";
import SearchHeader from "@/components/searchHeader.vue";
import ExamineDetails from "@/components/examineDetails/index.vue";
import { ysPagination } from "@ys/ui";
import { tableColumns } from "@/assets/types";
import { useRouter, onBeforeRouteUpdate } from "vue-router";
import useResourceList from "@/hooks/useResourceList";
import useExamineDetails from "@/hooks/useExamineDetails";
const {
  loading,
  resourceParams,
  resourceTotal,
  resourceList,
  handleStatusSift,
  handleTabSort2,
  selectedRowKeys,
  onSelectRowChange,
  getResouceAuditList,
  handlePaginationChange,
  handleResourceReset,
  newSiftSearch,
  handleDeleteProject,
  handleExport,
  handleAuditEdit
} = useResourceList();
const {
  examineDetailsVisible,
  examineDetailsId,
  examineDhowAlert,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();
const router = useRouter();
onBeforeRouteUpdate((to: any, form: any, next: any) => {
  containerType.value = to.query.containerType;
  next();
});

onMounted(async () => {
  containerType.value = router.currentRoute.value.query.containerType;
  debounced.value.debouncedFc(getResouceAuditList);
});
/* --------------- data --------------- */
//#region
const containerType = ref<any>();
const reFresh = ref<boolean>(true);

const rowSelection = ref<any>({
  selectedRowKeys,
  onChange: onSelectRowChange,
});

const columns = ref<Array<tableColumns>>([
  { title: "申报项目", dataIndex: "resourceName", ellipsis: true },
  { title: "姓名", dataIndex: "userName", ellipsis: true, width: "191px" },
  { title: "认证时间", dataIndex: "createTime", sorter: true, width: "230px" },
  { title: "类型", dataIndex: "resourceType", width: "230px" },
  { title: "学时", dataIndex: "classHour", width: "230px" },
  { title: "状态", dataIndex: "status", width: "230px" },
  { title: "操作", dataIndex: "operation", width: "128px" },
]);

//#endregion
const debounced = ref<any>({
  timer: null,
  t: 200,
  debouncedFc: (fc: Function, wait?: string) => {
    if (!!wait) debounced.value.t = wait;
    clearInterval(debounced.value.timer);
    debounced.value.timer = setTimeout(() => {
      fc();
      clearTimeout(debounced.value.timer);
    }, debounced.value.t);
  },
});

watch(
  () => containerType.value,
  (num: any) => {
    if (num == "1") {
      resourceParams.dataSource = 1;
      rowSelection.value.selectedRowKeys = [];
      rowSelection.value = null;
    } else {
      resourceParams.dataSource = null;
      rowSelection.value = {
        selectedRowKeys,
        onChange: onSelectRowChange,
      };
    }
    if (num !== "2") {
      debounced.value.debouncedFc(getResouceAuditList);
    }
  }
);

/* --------------- methods --------------- */
//#region

/* 审核记录 */
function handleRecord() {
  resourceParams.pageNo=1
  reFresh.value = false;
  router.push("/admin/declareSearch/list?containerType=1");
  nextTick(() => {
    reFresh.value = true;
  });
}
/* 新建申报 */
function handleAddDeclare() {
  router.push("/admin/declareSearch/examineAdd?containerType=3");
}
/* 编辑申报 */
function handleEdit(item: any) {
  router.push(
    `/admin/declareSearch/examineEdit/${item.eduResourceId}?containerType=2`
  );
}
/* 分页切换 */
function handlePageChange(no: number, size: number) {
  handlePaginationChange(no, size);
}
/* 删除 */
function handleDel(num: number, id: string) {
  if (!!!num) {
    handleDeleteProject(selectedRowKeys.value);
  } else {
    examineDetailsId.value = id;
    handleDeleteProject([examineDetailsId.value]);
  }
}

//#endregion
</script>

<style lang='scss' scoped>
.table_box {
  margin: 0 auto;
  width: calc(100% - 48px);
  height: calc(100vh - 292px);
}

::v-deep(.ant-popover-inner-content) {
  .ant-btn {
    height: 24px;
    border-radius: 2px;
  }
}
</style>
