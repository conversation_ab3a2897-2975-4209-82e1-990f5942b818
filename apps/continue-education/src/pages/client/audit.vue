<template>
  <ys-loading :loading="loading" />
  <main class="audit-layout">
    <section class="container">
      <pageHeader :prveTitle="'首页'" :currentTitle="'认证审核'" :goHome="true" />
      <div class="filtrate">
        <div class="filtrate-top">
          <div class="filtrate-item">
            <h4 class="title">搜索:</h4>
            <a-input class="input" v-model:value="resourceParams.name" placeholder="姓名/身份证号/申报项目" />
          </div>
          <div class="filtrate-item">
            <h4 class="title">选择年度:</h4>
            <a-range-picker class="input" picker="year" :disabledDate="disabledDate" @calendarChange="onCalendarChange"
              v-model:value="resourceDate" valueFormat="YYYY" />
          </div>
          <div class="filtrate-item">
            <h4 class="title">申报类型:</h4>
            <a-select class="input" ref="select" v-model:value="resourceParams.type" placeholder="全部">
              <a-select-option :value="item.value" v-for="item in applyTypeOptions">{{ item.label }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="filtrate-bottom">
          <div class="filtrate-item">
            <h4 class="title">审批状态:</h4>
            <a-select class="input" ref="select" v-model:value="resourceParams.status" placeholder="全部">
              <a-select-option :value="item.value" v-for="item in applyStatusOptions3">{{ item.label }}</a-select-option>
            </a-select>
          </div>
          <div class="filtrate-handler">
            <a-button type="primary" @click="handleResourceQuery">查询</a-button>
            <a-button type="default" @click="handleResourceReset" style="margin-left: 8px">重置</a-button>
          </div>
        </div>
      </div>

      <div class="audit-list">
        <div class="header">
          <div class="header-left">
            <h3 class="title">审核列表</h3>
            <span>(待审核{{ unAuditCount }})</span>
          </div>
          <div class="header-right">
            <a-button type="primary" :disabled="!!!selectedRowKeys.length" @click="handleBatchAudit">批量审核</a-button>
          </div>
        </div>
        <div class="table">
          <a-table style="margin-top: 8px" :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectRowChange,
            getCheckboxProps: getCheckboxProps,
          }" @change="handleTabSort" row-key="eduResourceId" :columns="columns" :data-source="resourceList"
            :pagination="false">
            <template v-slot:headerCell="{ column, record, index }">
              <div class="table-title">{{ column.title }}</div>
            </template>
            <template v-slot:bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex == 'resourceName'">
                <span class="c07 cursor" @click="handleOpenExamineDetail(record.eduResourceId)">
                  {{ record.resourceName }}
                </span>
              </template>
              <template v-if="column.dataIndex === 'resourceType'">
                <div>
                  <span>{{
                    computedResourceTypeText(record.resourceType)
                  }}</span>
                </div>
              </template>

              <template v-if="column.dataIndex === 'status'">
                <div class="table-status-carpe">
                  <i class="dot" :class="computedStatusText(record.status).className"></i>
                  <span>{{ computedStatusText(record.status).text }}</span>
                </div>
              </template>

              <template v-if="column.dataIndex === 'handler'">
                <div class="table-handler">
                  <div v-if="record.status == 0">
                    <span @click="openCheckModel(record)">
                      通过
                      <!-- <onePass text="通过" @ok="openCheckModel(record)" /> -->
                    </span>
                    <span @click="handleAuditOneRejectOpen(record)">
                      拒绝
                    </span>
                  </div>
                  <!-- <div v-if="record.status == 2">
                    <span @click="handleAuditEdit(record)">编辑</span>
                  </div> -->
                  <div v-else></div>
                </div>
              </template>
            </template>
          </a-table>
        </div>
        <div class="pagination">
          <ysPagination :pageNo="resourceParams.pageNo" :pageSize="resourceParams.pageSize" :total="resourceTotal"
            @change="handlePaginationChange" />
        </div>
      </div>
    </section>
  </main>

  <ExamineDetails :footerBtnS="true" :showAlert="true" :visible="examineDetailsVisible" :id="examineDetailsId"
    :resourceList="resourceList" @close="handleCloseExamineDetail" />

  <rejectReason :visible="rejectReasonVisible" :needReason="true" :basicReason="[]" @close="handleAuditOneRejectCancel"
    @define="handleAuditOneRejectConfirm" />

  <batchDeclare :visible="batchDeclareVisible" :num="selectedRowKeys.length" @close="batchDeclareVisible = false"
    @define="handleBatchDefine" />
  <checkModel v-if="showChecked"  @close="closeCheckModel" @sure="sureChecked" />
</template>

<script setup lang="ts">
import checkModel from "@/components/checkModel.vue"
import { ysLoading, ysPagination } from "@ys/ui";
import ExamineDetails from "@/components/examineDetails/index.vue";
import pageHeader from "@/components/page-header.vue";
import onePass from "@/components/onePass.vue";
import rejectReason from "@/components/examineDetails/rejectReason.vue";
import batchDeclare from "@/components/batchDeclare.vue";
import useResourceList from "@/hooks/useResourceList";
import useExamineDetails from "@/hooks/useExamineDetails";
import { onMounted, ref, nextTick } from "vue";
const {
  loading,
  batchDeclareVisible,
  rejectReasonVisible,
  resourceParams,
  resourceDate,
  resourceTotal,
  unAuditCount,
  resourceList,
  applyTypeOptions,
  applyStatusOptions3,
  selectedRowKeys,
  disabledDate,
  onCalendarChange,
  getCheckboxProps,
  computedStatusText,
  computedResourceTypeText,
  onSelectRowChange,
  getResouceAuditList,
  handleTabSort,
  handleResourceQuery,
  handleResourceReset,
  handlePaginationChange,
  handleAuditOnePass,
  handleAuditOneRejectOpen,
  handleAuditOneRejectCancel,
  handleAuditOneRejectConfirm,
  handleBatchAudit,
  handleBatchDefine,
  handleAuditEdit,
} = useResourceList();

const {
  examineDetailsVisible,
  examineDetailsId,
  examineRecord,
  examineDhowAlert,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();
const showChecked = ref(false)
const columns = [
  {
    title: "申报项目",
    dataIndex: "resourceName",
    key: "resourceName",
    ellipsis: true,
  },
  {
    title: "姓名",
    dataIndex: "userName",
    key: "userName",
  },
  {
    title: "身份证号",
    dataIndex: "idCard",
    key: "idCard",
  },
  {
    title: "认证时间",
    dataIndex: "createTime",
    key: "createTime",
    sorter: true,
  },
  {
    title: "申报类型",
    dataIndex: "resourceType",
    key: "resourceType",
  },
  {
    title: "学时",
    dataIndex: "classHour",
    key: "classHour",
  },
  {
    title: "状态",
    key: "status",
    dataIndex: "status",
  },
  {
    title: "操作",
    key: "handler",
    dataIndex: "handler",
  },
];
const rowObj = ref<any>({})
const openCheckModel = (val: any) => {
  showChecked.value = true
  rowObj.value = val
}
const closeCheckModel = () => {
  showChecked.value = false
  // getResouceAuditList(true)
}
const sureChecked = () => {
  handleAuditOnePass(rowObj.value)
  nextTick(() => {
    showChecked.value = false
  })
}
onMounted(() => {
  getResouceAuditList(true);
});
</script>

<style lang="scss" scoped>
.audit-layout {
  width: 100%;
  background: #f0f2f5;
  min-height: calc(100vh - 60px);
  overflow: auto;

  .container {
    margin: 24px auto;
    width: 1280px;

    .filtrate {
      margin-top: 16px;
      width: 100%;
      padding: 24px;
      box-sizing: border-box;
      border-radius: 4px;
      background: #fff;

      .filtrate-top {
        @include flex-space-between;
      }

      .filtrate-bottom {
        @include flex-space-between;
        margin-top: 24px;
      }

      .filtrate-item {
        @include flex-start;

        .title {
          margin: 0;
          margin-right: 8px;
          width: 104px;
          font-size: 14px;
          color: #262626;
          text-align: right;
        }

        .input {
          width: 291px;
        }
      }
    }

    .audit-list {
      margin-top: 16px;
      width: 100%;
      padding: 16px 24px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 4px;

      .header {
        @include flex-space-between;
        width: 100%;

        .header-left {
          @include flex-start;

          .title {
            margin-bottom: 0;
            font-size: 14px;
            font-weight: bold;
            color: #262626;
          }

          span {
            margin-left: 5px;
            color: #595959;
          }
        }
      }

      .table {
        .table-title {
          color: #262626;
          font-weight: bold;
        }

        .table-handler {
          user-select: none;
          color: #007aff;

          span {
            margin-right: 16px;
            cursor: pointer;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .pagination {
        @include flex-end;
        margin-top: 16px;
      }
    }
  }
}
</style>
