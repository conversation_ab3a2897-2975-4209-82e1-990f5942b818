<template>
  <ys-loading :loading="loading" />
  <main class="audit-layout">
    <section class="container">
      <pageHeader
        :prveTitle="'认证查询'"
        :currentTitle="'申报记录'"
        :goHome="false"
        @on-back="routerReplace('/client/inquire')"
      />
      <div class="filtrate">
        <div class="filtrate-top">
          <div class="filtrate-item">
            <h4 class="title">搜索:</h4>
            <a-input
              class="input"
              v-model:value="resourceParams.name"
              placeholder="姓名/身份证号/申报项目"
            />
          </div>
          <div class="filtrate-item">
            <h4 class="title">选择年度:</h4>
            <a-range-picker
              class="input"
              picker="year"
              :disabledDate="disabledDate"
              @calendarChange="onCalendarChange"
              v-model:value="resourceDate"
              valueFormat="YYYY"
            />
          </div>
          <div class="filtrate-item">
            <h4 class="title">申报类型:</h4>
            <a-select
              class="input"
              ref="select"
              v-model:value="resourceParams.type"
              placeholder="全部"
            >
              <a-select-option
                :value="item.value"
                v-for="item in applyTypeOptions"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </div>
        </div>
        <div class="filtrate-bottom">
          <div class="filtrate-item">
            <h4 class="title">审批状态:</h4>
            <a-select
              class="input"
              ref="select"
              v-model:value="resourceParams.status"
              placeholder="全部"
            >
              <a-select-option
                :value="item.value"
                v-for="item in applyStatusOptions"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </div>
          <div class="filtrate-handler">
            <a-button type="primary" @click="handleResourceQuery"
              >查询</a-button
            >
            <a-button
              type="default"
              @click="handleResourceReset"
              style="margin-left: 8px"
              >重置</a-button
            >
          </div>
        </div>
      </div>

      <div class="audit-list">
        <div class="header">
          <div class="header-left">
            <h3 class="title">申报记录</h3>
          </div>
          <div class="header-right">
            <a-button @click="handleExport(5)">导出</a-button>
            <a-button type="primary" @click="routerPush('/client/apply-add')"
              >资料申报</a-button
            >
          </div>
        </div>
        <div class="table">
          <a-table
            style="margin-top: 8px"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectRowChange,
              getCheckboxProps: getCheckboxProps,
            }"
            @change="handleTabSort2"
            row-key="eduResourceId"
            :columns="columns"
            :data-source="resourceList"
            :pagination="false"
          >
            <template v-slot:headerCell="{ column, record, index }">
              <div class="table-title">{{ column.title }}</div>
            </template>
            <template v-slot:bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex == 'resourceName'">
                <span
                  class="c07 cursor"
                  @click="handleOpenExamineDetail(record.eduResourceId)"
                >
                  {{ record.resourceName }}
                </span>
              </template>
              <template v-if="column.dataIndex === 'resourceType'">
                <div>
                  <span>{{
                    computedResourceTypeText(record.resourceType)
                  }}</span>
                </div>
              </template>

              <template v-if="column.dataIndex === 'status'">
                <div class="table-status-carpe">
                  <i
                    class="dot"
                    :class="computedStatusText(record.status).className"
                  ></i>
                  <span>{{ computedStatusText(record.status).text }}</span>
                </div>
              </template>

              <template v-if="column.dataIndex === 'handler'">
                <div class="table-handler">
                  <div v-if="record.status == 2">
                    <span @click="handleAuditEdit(record)">编辑</span>
                  </div>
                </div>
              </template>
            </template>
          </a-table>
        </div>
        <div class="pagination">
          <ysPagination
            :pageNo="resourceParams.pageNo"
            :pageSize="resourceParams.pageSize"
            :total="resourceTotal"
            @change="handlePaginationChange"
          />
        </div>
      </div>
    </section>
  </main>

  <ExamineDetails
    :record="examineRecord"
    :showAlert="examineDhowAlert"
    :visible="examineDetailsVisible"
    :id="examineDetailsId"
    :resourceList="resourceList"
    @close="handleCloseExamineDetail"
  />
</template>

<script setup lang="ts">
import { ysLoading, ysPagination } from "@ys/ui";
import ExamineDetails from "@/components/examineDetails/index.vue";
import pageHeader from "@/components/page-header.vue";
import useResourceList from "@/hooks/useResourceList";
import useRouterUtils from "@/hooks/useRouterUtils";
import useExamineDetails from "@/hooks/useExamineDetails";
import { ref, onMounted } from "vue";
import { getSessionUser } from "@ys/tools/common/user";
const { routerPush, routerReplace } = useRouterUtils();
const userInfo = getSessionUser();
const {
  loading,
  resourceParams,
  resourceDate,
  resourceTotal,
  resourceList,
  applyTypeOptions,
  applyStatusOptions,
  selectedRowKeys,
  disabledDate,
  onCalendarChange,
  getCheckboxProps,
  computedStatusText,
  computedResourceTypeText,
  onSelectRowChange,
  getResouceAuditList,
  handleExport,
  handleTabSort2,
  handleResourceQuery,
  handleResourceReset,
  handlePaginationChange,
  handleAuditEdit,
} = useResourceList();

const {
  examineDetailsVisible,
  examineDetailsId,
  examineRecord,
  examineDhowAlert,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();

const columns = [
  {
    title: "申报项目",
    dataIndex: "resourceName",
    key: "resourceName",
    ellipsis: true,
  },
  {
    title: "姓名",
    dataIndex: "userName",
    key: "userName",
  },
  {
    title: "认证时间",
    dataIndex: "createTime",
    key: "createTime",
    sorter: true,
  },
  {
    title: "申报类型",
    dataIndex: "resourceType",
    key: "resourceType",
  },
  {
    title: "学时",
    dataIndex: "classHour",
    key: "classHour",
  },
  {
    title: "状态",
    key: "status",
    dataIndex: "status",
  },
  {
    title: "操作",
    key: "handler",
    dataIndex: "handler",
  },
];

const detailsVisible = ref<boolean>(false);
const detailsId = ref<string>("");

onMounted(() => {
  if (userInfo) {
    resourceParams.userId = userInfo.id;
  }
  resourceParams.queryType = "2";
  getResouceAuditList(false);
});
</script>

<style lang="scss" scoped>
.audit-layout {
  width: 100%;
  background: #f0f2f5;
  min-height: calc(100vh - 60px);
  overflow: auto;
  .container {
    margin: 24px auto;
    width: 1280px;
    .page-header {
      user-select: none;
      i {
        margin: 0 8px;
        color: #8c8c8c;
      }
      .back-title {
        color: #8c8c8c;
        cursor: pointer;
      }
      .current-title {
        color: #262626;
      }
    }
    .filtrate {
      margin-top: 16px;
      width: 100%;
      padding: 24px;
      box-sizing: border-box;
      border-radius: 4px;
      background: #fff;
      .filtrate-top {
        @include flex-space-between;
      }
      .filtrate-bottom {
        @include flex-space-between;
        margin-top: 24px;
      }
      .filtrate-item {
        @include flex-start;
        .title {
          margin-right: 8px;
          width: 104px;
          font-size: 14px;
          color: #262626;
          text-align: right;
        }
        .input {
          width: 291px;
        }
      }
    }
    .audit-list {
      margin-top: 16px;
      width: 100%;
      padding: 16px 24px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 4px;
      .header {
        @include flex-space-between;
        width: 100%;
        .header-left {
          @include flex-start;
          .title {
            margin-bottom: 0;
            font-size: 14px;
            font-weight: bold;
            color: #262626;
          }
          span {
            margin-left: 5px;
            color: #595959;
          }
        }
        .header-right {
          button {
            margin-left: 8px;
          }
        }
      }
      .table {
        .table-title {
          color: #262626;
          font-weight: bold;
        }
        .table-status {
          @include flex-start;
          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #ffaa00;
          }
          span {
            margin-left: 12px;
            color: #262626;
          }
        }

        .table-handler {
          user-select: none;
          color: #007aff;
          span {
            margin-right: 16px;
            cursor: pointer;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      .pagination {
        @include flex-end;
        margin-top: 16px;
      }
    }
  }
}
</style>
