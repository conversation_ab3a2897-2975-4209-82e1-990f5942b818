<template>
  <ys-loading :loading="loading" />
  <main class="audit-layout">
    <section class="container">
      <pageHeader :prveTitle="'首页'" :currentTitle="'认证查询'" :goHome="true" />
      <div class="filtrate">
        <div class="nav">
          <div class="header-title" @click="changeNav(item)" :class="[headerActive == item ? 'blueBottom' : '']"
            v-for="item in 2" :key="item">
            {{ item == 1 ? '学时认证' : '学分认证' }}
          </div>
        </div>
        <div class="filtrate-top">
          <div class="filtrate-item">
            <h4 class="title">{{ headerActive == 1 ? '申报项目' : '认证名称' }}:</h4>
            <a-input class="input" v-model:value="resourceParams.name" placeholder="请输入" />
          </div>
          <div class="filtrate-item">
            <h4 class="title">选择年度:</h4>
            <a-range-picker class="input" picker="year" :placeholder="['开始日期', '结束日期']" :disabledDate="disabledDate"
              @calendarChange="onCalendarChange" v-model:value="resourceDate" valueFormat="YYYY" />
          </div>
          <div class="filtrate-item">
            <h4 class="title">申报类型:</h4>
            <a-select class="input" ref="select" v-model:value="resourceParams.type" placeholder="全部">
              <a-select-option :value="item.value" v-for="item in applyTypeOptions">{{ item.label }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="filtrate-bottom">
          <div class="filtrate-item">
            <h4 class="title">审批状态:</h4>
            <a-select class="input" ref="select" v-model:value="resourceParams.status" placeholder="全部">
              <a-select-option :value="item.value" v-for="item in applyStatusOptions">{{ item.label }}</a-select-option>
            </a-select>
          </div>
          <div class="filtrate-handler">
            <a-button type="primary" @click="handleResourceQuery">查询</a-button>
            <a-button type="default" @click="handleResourceReset" style="margin-left: 8px">重置</a-button>
          </div>
        </div>
      </div>

      <div class="audit-list">
        <div class="header">
          <div class="header-left">
            <h3 class="title">认证列表</h3>
            <!-- <div class="header-title" @click="changeNav(item)" :class="[headerActive == item ? 'blueBottom' : '']"
              v-for="item in 2" :key="item">
              {{ item == 1 ? '学时列表' : '学分列表' }}
            </div> -->
          </div>
          <div class="header-right" v-if="headerActive == 1">
            <a-button @click="handleExport(5, headerActive)">导出</a-button>
            <a-button @click="routerPush('/client/apply-record')">申报记录</a-button>
            <a-button type="primary" @click="routerPush('/client/apply-add')">资料申报</a-button>
          </div>
          <div class="header-right" v-else>
            <a-button type="primary" @click="handleExport(5, headerActive)">
              <ysIcon type='icondaochu' />
              导出
            </a-button>
          </div>
        </div>
        <div class="table">
          <a-table style="margin-top: 8px" :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectRowChange,
            getCheckboxProps: getCheckboxProps,
          }" @change="handleTabSort2" row-key="eduResourceId" :columns="columns" :data-source="resourceList"
            :pagination="false">
            <template v-slot:headerCell="{ column, record, index }">
              <div class="table-title"
                v-if="column.dataIndex != 'classHour' && column.dataIndex != 'resourceType' && column.dataIndex != 'resourceName'">
                {{ column.title }}</div>
              <template v-if="column.dataIndex == 'resourceName'">
                <span v-if="headerActive == 1" style="font-weight: bold">申报项目</span>
                <span v-if="headerActive == 2" style="font-weight: bold">认证名称</span>
              </template>
              <template v-if="column.dataIndex == 'resourceType'">
                <span v-if="headerActive == 1" style="font-weight: bold">申报类型</span>
                <span v-if="headerActive == 2" style="font-weight: bold">认证类型</span>
              </template>
              <template v-if="column.dataIndex == 'classHour'">
                <span v-if="headerActive == 1" style="font-weight: bold">学时</span>
                <span v-if="headerActive == 2" style="font-weight: bold">学分</span>
              </template>
            </template>
            <template v-slot:bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex == 'resourceName'">
                <span class="c07 cursor" @click="handleOpenExamineDetail(record.eduResourceId)">
                  {{ record.resourceName }}
                </span>
              </template>
              <template v-if="column.dataIndex === 'resourceType'">
                <div>
                  <span>{{
                    computedResourceTypeText(record.resourceType)
                  }}</span>
                </div>
              </template>
              <template v-if="column.dataIndex === 'classHour'">
                {{ headerActive == 1 ? record.classHour : record.classScore }}
              </template>
              <template v-if="column.dataIndex === 'status'">
                <div class="table-status-carpe">
                  <i class="dot" :class="computedStatusText(record.status).className"></i>
                  <span>{{ computedStatusText(record.status).text }}</span>
                </div>
              </template>

              <template v-if="column.dataIndex === 'handler'">
                <div class="table-handler">
                  <div v-if="record.status == 2 || record.status == 3">
                    <span @click="handleAuditEdit(record)">编辑</span>
                    <span @click="delAduitItem(record)">删除</span>
                  </div>
                  <div v-if="record.status == 1 && showCertificateBtn">
                    <span @click="lookCercificate(record)">查看证书</span>
                  </div>
                  <div v-if="record.status == 0">
                    <span @click="revocation(record)">撤回</span>
                    <!-- <span @click="delAduitItem(record)">删除</span> -->
                  </div>
                </div>
              </template>
            </template>
          </a-table>
        </div>
        <div class="pagination">
          <ysPagination :pageNo="resourceParams.pageNo" :pageSize="resourceParams.pageSize" :total="resourceTotal"
            @change="handlePaginationChange" />
        </div>
      </div>
    </section>
  </main>
  <a-modal v-model:visible="open" title="证书预览" :footer="null" v-if="open">
    <div style="
        width: 100%;
        min-height: 590px;
        display: flex;
        align-items: center;
        justify-content: center;
      ">
      <iframe v-if="open" class="iframeBox" type="application/x-google-chrome-pdf" frameborder="0"
        style="width: 375px; height: 545px" :src="iframeUrl"></iframe>
    </div>
  </a-modal>
  <ExamineDetails :detailsType="headerActive" :record="true" :visible="examineDetailsVisible" :id="examineDetailsId"
    :resourceList="resourceList" @close="handleCloseExamineDetail" />
</template>

<script setup lang="ts">
import { ysLoading, ysPagination, ysIcon } from "@ys/ui";
import ExamineDetails from "@/components/examineDetails/index.vue";
import pageHeader from "@/components/page-header.vue";
import useResourceList from "@/hooks/useResourceList";
import useRouterUtils from "@/hooks/useRouterUtils";
import { ref, onMounted } from "vue";
import { getSessionUser } from "@ys/tools/common/user";
import useExamineDetails from "@/hooks/useExamineDetails";
const { routerPush, routerNewPage } = useRouterUtils();
const userInfo = getSessionUser();
import { personalCertify, getPersonalCertifyRule, revokeProject } from "@/request"
import { message } from "ant-design-vue";
const {
  loading,
  resourceParams,
  resourceDate,
  resourceTotal,
  resourceList,
  applyTypeOptions,
  applyStatusOptions,
  selectedRowKeys,
  disabledDate,
  onCalendarChange,
  getCheckboxProps,
  computedStatusText,
  computedResourceTypeText,
  onSelectRowChange,
  getResouceAuditList,
  handleExport,
  handleTabSort2,
  handleResourceQuery,
  handleResourceReset,
  handlePaginationChange,
  handleAuditEdit,
  handleDeleteProject,
} = useResourceList();

const {
  examineDetailsVisible,
  examineDetailsId,
  examineRecord,
  examineDhowAlert,
  handleOpenExamineDetail,
  handleCloseExamineDetail,
} = useExamineDetails();

const columns = [
  {
    title: "申报项目",
    dataIndex: "resourceName",
    key: "resourceName",
    ellipsis: true,
  },
  {
    title: "姓名",
    dataIndex: "userName",
    key: "userName",
  },
  {
    title: "认证时间",
    dataIndex: "createTime",
    key: "createTime",
    sorter: true,
  },
  {
    title: "申报类型",
    dataIndex: "resourceType",
    key: "resourceType",
  },
  {
    title: "学时",
    dataIndex: "classHour",
    key: "classHour",
  },
  {
    title: "状态",
    key: "status",
    dataIndex: "status",
  },
  {
    title: "操作",
    key: "handler",
    dataIndex: "handler",
  },
];
const iframeUrl = ref("");
const open = ref(false);
const changeNav = (val: any) => {
  resourceDate.value = []
  resourceParams.startTime = null
  resourceParams.endTime = null
  resourceParams.name = null
  resourceParams.type = null
  resourceParams.status = null
  resourceParams.pageNo = 1
  resourceParams.pageSize = 10
  headerActive.value = val
  if (val == 1) {
    resourceParams.sourceType = null
  }
  else {
    resourceParams.sourceType = 1
  }
  getResouceAuditList(false);
  // 改变resorceParams状态
  // resourceParams.userId = userInfo.id;
}
const detailsVisible = ref<boolean>(false);
const detailsId = ref<string>("");
const headerActive = ref(1)
const showCertificateBtn = ref(false)
onMounted(() => {
  if (userInfo) {
    resourceParams.userId = userInfo.id;
  }
  resourceParams.queryType = "1";
  getResouceAuditList(false);
  getOneCertificateRule()
});
const getOneCertificateRule = async () => {
  const res = await getPersonalCertifyRule() as any
  if (res.data.code == 0) {
    console.log(res.data)
    if (res.data.data && res.data.data.personalCertificateId) {
      showCertificateBtn.value = true
    }
  }
}
const lookCercificate = async (val: any) => {
  loading.value = true
  let params = {
    eduResourceId: val.eduResourceId,
    type: headerActive.value
  }
  const res = await personalCertify(params) as any
  console.log(res.data)
  if (res.data.code == 0 && res.data.success) {
    loading.value = false
    const result = res.data.data;
    const { origin } = location;
    open.value = true;
    console.log("result", result);
    const url = `${origin}/yskt/certify/#${result}`;
    console.log("url", url);
    // setTimeout(() => {
      iframeUrl.value = url;
    // }, 1000);
  }
  else {
    loading.value = false
  }
}
// 撤回
const revocation = (val: any) => {
  loading.value = true
  revokeProject({ eduResourceId: val.eduResourceId }).then((res: any) => {
    if (res.data.code == 0) {
      message.success('已撤回')
      getResouceAuditList(false);
      loading.value = false
    }
    else {
      message.error(res.data.msg)
      loading.value = false
    }
  })
}
const delAduitItem = (val: any) => {
  handleDeleteProject([val.eduResourceId])

};
</script>

<style lang="scss" scoped>
.audit-layout {
  width: 100%;
  background: #f0f2f5;
  min-height: calc(100vh - 60px);
  overflow: auto;

  .container {
    margin: 24px auto;
    width: 1280px;

    .page-header {
      user-select: none;

      i {
        margin: 0 8px;
        color: #8c8c8c;
      }

      .back-title {
        color: #8c8c8c;
        cursor: pointer;
      }

      .current-title {
        color: #262626;
      }
    }

    .filtrate {
      margin-top: 16px;
      width: 100%;
      padding: 0 24px 24px 24px;
      box-sizing: border-box;
      border-radius: 4px;
      background: #fff;

      .nav {
        display: flex;
        align-items: center;
        height: 56px;
        margin-bottom: 24px;
        border-bottom: 1px solid #e5e5e5;

        .header-title {
          height: 56px;
          line-height: 56px;
          cursor: pointer;

          &:last-child {
            margin-left: 16px
          }
        }


        .blueBottom {
          border-bottom: 2px solid #007aff
        }
      }

      .filtrate-top {
        @include flex-space-between;
      }

      .filtrate-bottom {
        @include flex-space-between;
        margin-top: 24px;
      }

      .filtrate-item {
        @include flex-start;

        .title {
          margin-right: 8px;
          width: 104px;
          font-size: 14px;
          color: #262626;
          text-align: right;
        }

        .input {
          width: 291px;
        }
      }
    }

    .audit-list {
      margin-top: 16px;
      width: 100%;
      padding: 0 24px 16px 24px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 4px;

      .header {
        // @include flex-space-between;
        width: 100%;
        height: 68px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-left {
          @include flex-start;

          .title {
            font-size: 14px;
            font-weight: bold;
            height: 68px;
            cursor: pointer;
            line-height: 68px;
          }



          span {
            margin-left: 5px;
            color: #595959;
          }
        }

        .header-right {
          button {
            margin-left: 8px;
          }
        }
      }

      .table {
        .table-title {
          color: #262626;
          font-weight: bold;
        }

        .table-status {
          @include flex-start;

          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #ffaa00;
          }

          span {
            margin-left: 12px;
            color: #262626;
          }
        }

        .table-handler {
          user-select: none;
          color: #007aff;

          span {
            margin-right: 16px;
            cursor: pointer;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }

      .pagination {
        @include flex-end;
        margin-top: 16px;
      }
    }
  }
}
</style>
