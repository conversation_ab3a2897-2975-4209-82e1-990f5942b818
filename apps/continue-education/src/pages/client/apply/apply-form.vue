<template>
  <section class="content">
    <h3 class="title">{{ form.eduResourceId ? "编辑" : "资料申报" }}</h3>
    <main class="apply-form" v-if="show">
      <div class="title2">
        <i class="line"></i>
        <span>资料填写</span>
      </div>
      <div class="form">
        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>姓名:</span>
          </div>
          <div class="form-input">
            <a-input class="input" disabled v-model:value="username" />
          </div>
        </div>

        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>身份证号:</span>
          </div>
          <div class="form-input">
            <a-input
              v-if="idCard"
              disabled
              class="input"
              v-model:value="form.idCard"
              placeholder="请输入"
              :maxlength="18"
            />
            <a-input
              v-else
              class="input"
              v-model:value="form.idCard"
              placeholder="请输入"
              :maxlength="18"
            />
          </div>
        </div>

        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>所属机构:</span>
          </div>
          <div class="form-input">
            <a-input class="input" disabled v-model:value="orgname" />
          </div>
        </div>

        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>选择年度:</span>
          </div>
          <div class="form-input">
            <a-select
              class="input"
              ref="select"
              placeholder="请选择"
              v-model:value="dateValue"
            >
              <a-select-option
                :value="item.eduSystemSetId"
                v-for="item in dateList"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </div>
        </div>
        <!-- 学时来源 -->
        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>学时来源:</span>
          </div>
          <div class="form-input">
            <a-radio-group
              :options="sourceOptions"
              v-model:value="form.sourceType"
              @change="handleChangeSourceType"
            />
          </div>
        </div>
        <!-- 申报分类 -->
        <div class="form-item" v-if="classification">
          <div class="form-title">
            <i>*</i>
            <span>申报分类:</span>
          </div>
          <div class="form-input">
            <a-cascader
              class="input"
              v-model:value="form.classifyId"
              :options="classificationOptions"
              placeholder="请选择分类"
              change-on-select
              @change="handleChangeCascader"
            />
          </div>
        </div>
        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>培训级别:</span>
          </div>
          <div class="form-input">
            <a-select
              :disabled="disabledInputLevel"
              class="input"
              ref="select"
              placeholder="请选择"
              v-model:value="form.trainLevel"
            >
              <a-select-option
                :value="item.id"
                v-for="item in tringLevelList"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </div>
        </div>
        <!-- 培训方式 -->
        <div class="form-item" v-if="form.sourceType == '1'">
          <div class="form-title">
            <i>*</i>
            <span>培训方式:</span>
          </div>
          <div class="form-input">
            <a-select
              class="input"
              ref="select"
              placeholder="请选择"
              v-model:value="form.trainWay"
            >
              <a-select-option :value="item.id" v-for="item in tringTypeList">{{
                item.name
              }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="form-item" v-if="form.sourceType == '1'">
          <div class="form-title">
            <i>*</i>
            <span>角色参与:</span>
          </div>
          <div class="form-input">
            <a-select
              class="input"
              ref="select"
              placeholder="请选择"
              v-model:value="form.joinRole"
            >
              <a-select-option :value="item.id" v-for="item in partList">{{
                item.name
              }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="form-item" v-if="form.sourceType == '1'">
          <div class="form-title">
            <i>*</i>
            <span>学段:</span>
          </div>
          <div class="form-input">
            <a-select
              class="input"
              ref="select"
              placeholder="请选择"
              v-model:value="form.stageIds"
              @change="handleChangeStage"
            >
              <a-select-option :value="item.code" v-for="item in stageList">{{
                item.text
              }}</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="form-item" v-if="form.sourceType == '1'">
          <div class="form-title">
            <i>*</i>
            <span>任教学科:</span>
          </div>
          <div class="form-input">
            <a-select
              class="input"
              ref="select"
              placeholder="请选择"
              v-model:value="form.subjectIds"
            >
              <a-select-option :value="item.id" v-for="item in subjectList">{{
                item.name
              }}</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>手机号:</span>
          </div>
          <div class="form-input">
            <a-input
              :disabled="disabledPhone"
              class="input current"
              type="number"
              v-model:value="form.phone"
              :maxlength="11"
              @keyup="onlyNumber('phone')"
              placeholder="请输入"
            />
          </div>
        </div>

        <div class="form-item" v-if="form.sourceType == '1'">
          <div class="form-title">
            <i>*</i>
            <span>学习时间:</span>
          </div>
          <div class="form-input">
            <a-range-picker
              :placeholder="['开始日期', '结束日期']"
              v-model:value="time"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              :showNow="false"
              style="width: 328px"
            />
          </div>
        </div>
        <div class="form-item" v-if="form.sourceType == '1'">
          <div class="sutdyAddress">
            <i>*</i>
            <span>学习地点:</span>
          </div>
          <div class="form-input">
            <a-textarea
              :rows="2"
              class="textarea"
              v-model:value="form.studyAddress"
              placeholder="请输入"
              :maxlength="50"
            />
          </div>
        </div>
        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>申报项目:</span>
          </div>
          <div class="form-input">
            <a-input
              class="input"
              v-model:value="form.resourceName"
              placeholder="请输入"
              show-count
              :maxlength="50"
            />
          </div>
        </div>

        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>申报类型:</span>
          </div>
          <div class="form-input">
            <a-radio-group
              :options="plainOptions"
              v-model:value="form.resourceType"
            />
          </div>
        </div>

        <div class="form-item">
          <div class="form-title">
            <i>*</i>
            <span>认证学时:</span>
          </div>
          <div class="form-input">
            <a-input
              :disabled="disabledInputHour"
              class="time current"
              v-model:value="form.classHour"
              placeholder="请输入"
              :maxlength="5"
              @keyup="onlyNumber('classHour')"
            />
            <span class="text">学时</span>
          </div>
        </div>
      </div>

      <div class="title2">
        <i class="line"></i>
        <span>上传证书或其他扫描件</span>
      </div>

      <div class="upload-content">
        <span class="small-title">
          <i>*</i>
          <span style="margin-left: 4px">资料</span>
          :</span
        >
        <div class="upload-info">
          <div class="upload-button">
            <a-button>
              <template #icon>
                <ysIcon type="iconshangchuan1" />
              </template>
              上传
            </a-button>
            <input type="file" class="upload-file" @change="handleUpload" />
          </div>
          <ul class="file-list">
            <li class="file-item" v-for="(item, index) in form.attachments">
              <div class="file-item-left">
                <ysIcon type="iconfujian1" class="icon" />
                <span class="name">{{
                  getFileNameEllipsis(item.attachmentName)
                }}</span>
              </div>
              <div class="file-item-right" @click="handleDeleteFile(index)">
                <ysIcon type="iconshanchudefuben" class="icon" />
              </div>
            </li>
          </ul>
          <div class="tips">最大50M，最多可支持10个文件</div>
        </div>
      </div>
    </main>

    <main class="apply-success" v-else>
      <div class="circle">
        <ysIcon type="icon-yes" class="icon" />
      </div>
      <p class="title">资料申报成功</p>
      <div class="handler">
        <a-button
          v-if="!form.eduResourceId"
          type="primary"
          class="current"
          @click="handleContinue"
          >继续申报</a-button
        >
        <a-button @click="handleBack">返回列表</a-button>
      </div>
    </main>
  </section>

  <footer v-if="show" class="footer" :style="{ width: footerWidth }">
    <!-- <a-button @click="handleCancle">取消</a-button> -->
    <a-button type="primary" @click="handleSubmit">提交</a-button>
  </footer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import useRouterUtils from "@/hooks/useRouterUtils";
import { message } from "ant-design-vue";
import { ysIcon } from "@ys/ui";
import { common } from "@ys/tools";
import {
  dictGetByType,
  getResourceDetail,
  getSubjectByGrade,
  getSystemSet,
  getUserInfo,
  getConfig,
  hanlleGetTree,
} from "@/request";
import dayjs, { Dayjs } from "dayjs";
import { getFileNameEllipsis } from "@/utils/index";
import { checkID } from "@/utils/verifyIdCard";

interface Props {
  footerWidth: string;
  show: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  footerWidth: "100%",
  show: true,
});

const emits = defineEmits<{
  (e: "on-continue"): void;
  (e: "on-back"): void;
  (e: "on-cancle"): void;
  (e: "on-submit", payload: formProps): void;
}>();

const { upload, getSessionUser } = common;
const { route } = useRouterUtils();
const userInfo = getSessionUser();

export interface formProps {
  eduResourceId: string;
  userId: string;
  orgid: string;
  idCard: string;
  startTime: string;
  endTime: string;
  stageIds: string | null;
  subjectIds: string | null;
  phone: string;
  studyTime: any;
  studyAddress: string;
  resourceName: string;
  resourceType: string;
  classHour: string;
  attachments: attachmentItemProp[];
  trainLevel: null;
  joinRole: null;
  sourceType: string;
  classifyId: string | null;
  trainWay: null;
}
const tringLevelList = ref([
  { id: 1, name: "国家级" },
  { id: 2, name: "省级" },
  { id: 3, name: "市级" },
  { id: 4, name: "县级" },
  { id: 5, name: "校级" },
  { id: 6, name: "其他" },
]);
const tringTypeList = ref([
  { id: 5, name: "其他" },
  { id: 1, name: "长期脱产研修（一个月以上）" },
  { id: 2, name: "短期面授培训" },
  { id: 3, name: "网络研修" },
  { id: 4, name: "面授培训和网络研修结合" },
]);
const partList = ref([
  { id: 1, name: "主讲人" },
  { id: 2, name: "学员" },
]);
interface stageItemProp {
  code: string;
  id: string;
  text: string;
  type: string;
}

interface subjectItemProp {
  id: string;
  name: string;
}

interface attachmentItemProp {
  attachmentName: string;
  attachmentPath: string;
}

const dateList = ref<any>([]);
const dateValue = ref("");
const time = ref<any>("");
const username = ref("");
const orgname = ref("");
const idCard = ref("");
const classification = ref(false);
const disabledInputHour = ref(false);
const disabledInputLevel = ref(false);
const disabledPhone = ref(false);
const form: formProps = reactive({
  eduResourceId: "",
  userId: "",
  orgid: "",
  idCard: "",
  startTime: "",
  endTime: "",
  stageIds: null,
  subjectIds: null,
  phone: "",
  studyTime: [],
  studyAddress: "",
  resourceName: "",
  resourceType: "1",
  sourceType: "1",
  classHour: "",
  attachments: [],
  trainLevel: null,
  joinRole: null,
  trainWay: null,
  classifyId: null,
});

const plainOptions = [
  { label: "灵活性", value: "1" },
  { label: "规范性", value: "2" },
];
const sourceOptions = ref<any>([
  { label: "培训", value: "1" },
  { label: "其他", value: "2" },
]);
const classificationOptions = ref<any>([]);
const stageList = ref<Array<stageItemProp>>([]);
const subjectList = ref<Array<subjectItemProp>>([]);
const allowOtherApply = ref(true);
const handleChangeCascader = (e: any, selectedOptions: any) => {
  console.log("e", e);
  console.log("selectedOptions", selectedOptions);
  let obj: any = {};
  if (selectedOptions) {
    obj = selectedOptions[selectedOptions.length - 1];
    if (obj.hour) {
      form.classHour = obj.hour;
      disabledInputHour.value = true;
    } else {
      form.classHour = null as any;
      disabledInputHour.value = false;
    }
    if (obj.level) {
      form.trainLevel = obj.level;
      disabledInputLevel.value = true;
    } else {
      form.trainLevel = null as any;
      disabledInputLevel.value = false;
    }
  } else {
    form.classHour = null as any;
    disabledInputHour.value = false;
    form.trainLevel = null as any;
    disabledInputLevel.value = false;
  }
};
const getUserInfoData = async () => {
  const response = (await getUserInfo()) as any;
  const res = response.data;
  const data = res.data;
  username.value = data.name;
  orgname.value = data.orgName;
  idCard.value = data.IDCard || "";
  form.idCard = data.IDCard || "";
  form.userId = data.id;
  form.orgid = data.orgId;
  form.phone = data.phone || null;
  if (data.phone) {
    disabledPhone.value = true;
  }
};

const getTime = async () => {
  let responce = (await getSystemSet()) as any;
  const res = responce.data.data;
  dateList.value = res.map((item: any) => {
    if (item.name == null) {
      item.name = `${item.startTime.split("-")[0]}-${item.endTime.split("-")[0]}`;
    }
    return item;
  });
  if (!route.params.id) {
    dateValue.value = dateList.value[0].eduSystemSetId;
  }

  // dateValue.value = [(res.startTime), (res.endTime)];
  // dateHackValue.value = [(res.startTime), (res.endTime)];
  // date.value = [dayjs(res.startTime), dayjs(res.endTime)];
};
const showWatch = watch(
  () => props.show,
  (newV) => {
    if (newV) {
      handleResetFormData();
    }
  }
);
const dateWatch = watch(
  () => dateValue.value,
  (newV) => {
    if (newV) {
      const item = dateList.value.find(
        (item: any) => item.eduSystemSetId === newV
      );
      form.startTime = item.startTime;
      form.endTime = item.endTime;
    } else {
      form.startTime = "";
      form.endTime = "";
    }
  }
);

const timeWatch = watch(
  () => time.value,
  (newV) => {
    if (newV) {
      form.studyTime = newV;
    } else {
      form.studyTime = "";
    }
  }
);
watch(
  () => props.show,
  (val) => {
    // if (val && !route.params.id) {
    // }
    getTime();
  },
  { immediate: true }
);
const stageWatch = watch(
  () => form.stageIds,
  () => {
    form.subjectIds = null;
  }
);

const handleResetFormData = () => {
  // date.value = [];
  // dateValue.value = [];
  // dateHackValue.value = [];
  dateList.value = [];
  dateValue.value = "";
  time.value = "";
  // form.userId = "";
  // form.orgid = "";
  // form.idCard = "";
  form.startTime = "";
  form.endTime = "";
  form.stageIds = null;

  form.phone = "";
  form.studyTime = "";
  form.resourceName = "";
  form.resourceType = "1";
  form.classHour = "";
  form.attachments = [];
  form.studyAddress = "";
  form.trainLevel = null;
  form.joinRole = null;
  form.trainWay = null;
  form.classifyId = null;
  stageList.value = [];
  form.subjectIds = null;
  form.sourceType = "1";
  disabledInputLevel.value = false;
  getUserInfoData();
};
const handleChangeSourceType = () => {
  if (form.sourceType == "2") {
    form.studyAddress = "";
    form.studyAddress = "";
    form.stageIds = null;
    form.joinRole = null;
    time.value = "";
  }
};
const handleGetStage = async () => {
  try {
    const params = { type: "stage" };
    const response = await dictGetByType(params);
    const res = response.data as any;
    const data = res.data;
    stageList.value = data;
  } catch (error) {
    message.error("获取学段失败");
  }
};

const handleChangeStage = (value: string) => {
  handleGetSubject(value);
};

const handleGetSubject = async (stageId: string, subjectIds?: string) => {
  try {
    const params = {
      subjectType: "378953758199259136",
      periodId: stageId,
    };
    const response = await getSubjectByGrade(params);
    const res = response.data as any;
    const data = res.data;
    subjectList.value = data;
    /**
     * 编辑回显显示学科
     */
    if (subjectIds) {
      form.subjectIds = subjectIds;
    } else {
      form.subjectIds = null;
    }
  } catch (error) {
    message.error("获取学科失败");
  }
};

const onlyNumber = (key: any) => {
  if ((form as any)[key] != null) {
    (form as any)[key] = ((form as any)[key] as string).replace(/[^0-9]/g, "");
  }
};

const handleUpload = async (e: any) => {
  if (form.attachments.length >= 10) {
    message.error("最多可支持10个文件");
    return;
  }
  const suffixList = ["doc", "docx", "ppt", "pptx", "pdf", "png", "jpg"];
  let file = e.target.files[0];
  const suffix = file.name.slice(file.name.lastIndexOf(".") + 1);
  if (!suffixList.includes(suffix)) {
    message.error("不支持该文件格式");
    return;
  }
  let size = file.size / 1048576;
  if (size > 50) {
    message.error("文件最大支持50M");
    return;
  }
  try {
    const response = await upload(e);
    if (response) {
      console.log(response);
      const item: attachmentItemProp = {
        attachmentName: file.name || response.filename,
        attachmentPath: response.filepath || response.file.pdfUrl,
      };
      form.attachments.push(item);
    }
  } catch (error) {
    message.error("文件上传失败");
  }
};

const handleDeleteFile = (index: number) => {
  form.attachments.splice(index, 1);
};

const handleContinue = () => {
  emits("on-continue");
  handleResetFormData();
};
const handleBack = () => {
  emits("on-back");
};
const handleCancle = () => {
  emits("on-cancle");
};
const handleSubmit = () => {
  let starTime: any = "";
  let endTime: any = "";
  for (let i of dateList.value) {
    if (i.eduSystemSetId == dateValue.value) {
      starTime = new Date(i.startTime).getTime();
      endTime = new Date(i.endTime).getTime();
    }
  }
  if (form.idCard.trim() == "") {
    message.error("身份证号不能为空");
    return false;
  }
  if (!checkID(form.idCard)) {
    message.error("身份证号格式不正确");
    return false;
  }
  if (!form.classifyId && classification.value) {
    message.error("请选择申报分类");
    return false;
  }
  if (!form.trainLevel && form.sourceType == "1") {
    message.error("培训级别不能为空");
    return false;
  }
  if (!form.trainWay && form.sourceType == "1") {
    message.error("请选择培训方式");
    return false;
  }
  if (form.joinRole == "" && form.sourceType == "1") {
    message.error("参与角色不能为空");
    return false;
  }
  if (!form.stageIds && form.sourceType == "1") {
    message.error("学段不能为空");
    return false;
  }
  if (!form.subjectIds && form.sourceType == "1") {
    message.error("任教学科不能为空");
    return false;
  }
  if (!form.phone) {
    message.error("手机号不能为空");
    return false;
  }
  if (!time.value && form.sourceType == "1") {
    message.error("请选择学习时间");
    return false;
  }
  if (!form.studyAddress && form.sourceType == "1") {
    message.error("学习地点不能为空");
    return false;
  }
  if (form.resourceName.trim() == "") {
    message.error("申报项目不能为空");
    return false;
  }
  if (!form.classHour || String(form.classHour).trim() === "") {
    message.error("请输入认证学时");
    return false;
  }
  if (Number(form.classHour) <= 0) {
    message.error("认证学时不能为0");
    return false;
  }
  if (form.attachments.length == 0) {
    message.error("请上传附件");
    return false;
  }
  if (!allowOtherApply.value && form.sourceType == "2") {
    message.error("当前设置不允许教师申报非培训学时");
    return false;
  }
  const params = JSON.parse(JSON.stringify(form));
  params.eduSystemSetId = dateValue.value;
  params.studyTime = time.value[0];
  params.studyEndTime = time.value[1];
  if (params.classifyId) {
    let id = null;
    let idArr = params.classifyId;
    id = idArr[idArr.length - 1];
    params.classifyId = id;
  }
  emits("on-submit", params);
};

// 获取详情
const handleGetResourceDetail = async () => {
  try {
    const response = await getResourceDetail({ id: form.eduResourceId });
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      const response2 = (await getUserInfo()) as any;
      const userInfoData = response2.data.data as any;
      const data = res.data;
      // console.log("userInfoData", userInfoData);
      // console.log("data", data);
      dateValue.value = data.eduSystemSetId;
      idCard.value = data.idCard || userInfoData.IDCard || "";
      username.value = data.userName;
      orgname.value = data.orgName;
      form.userId = data.userId;
      form.orgid = data.orgid;
      form.idCard = data.idCard;
      form.startTime = dayjs(new Date(data.startTime)).format("YYYY-MM");
      form.endTime = dayjs(new Date(data.endTime)).format("YYYY-MM");
      // date.value = [dayjs(data.startTime), dayjs(data.endTime)];
      // dateValue.value = [data.startTime, data.endTime];
      // dateHackValue.value = [dayjs(data.startTime), dayjs(data.endTime)];
      form.stageIds = data.stageIds;
      form.subjectIds = data.subjectIds;
      if (form.stageIds) {
        handleGetSubject(form.stageIds, form.subjectIds as string);
      }

      form.phone = data.phone || "";
      form.studyTime = [data.studyTime, data.studyEndTime] as any;
      time.value = [data.studyTime, data.studyEndTime];
      form.resourceName = data.resourceName;
      form.resourceType = String(data.resourceType);
      form.classHour = String(data.classHour);
      form.trainLevel = data.trainLevel;
      form.joinRole = data.joinRole;
      form.studyAddress = data.studyAddress;
      form.trainWay = data.trainWay;
      form.sourceType = String(data.sourceType);
      let arr = JSON.parse(JSON.stringify(data.cascaderPath));
      arr.splice(0, 1);
      form.classifyId = arr;
      if (data.attachments) {
        data.attachments.forEach((item: attachmentItemProp) => {
          let obj: attachmentItemProp = {
            attachmentName: "",
            attachmentPath: "",
          };
          obj.attachmentName = item.attachmentName;
          obj.attachmentPath = item.attachmentPath;
          form.attachments.push(obj);
        });
      }
    }
  } catch (error) {}
};
const init = () => {
  getConfig().then((res: any) => {
    if (res.data.code == 0) {
      classification.value = res.data.data.classifyApply;
      allowOtherApply.value = res.data.data.allowNotTraining;
    }
  });
};
const handleGetTypeTree = async () => {
  let params = {
    status: 0,
  };
  const responce = await hanlleGetTree(params);
  const res: any = responce.data;
  classificationOptions.value = res.data[0].chirldrenList || [];
  changeChildren(classificationOptions.value);
  console.log("classificationOptions", classificationOptions.value);
};
const changeChildren = (tree: any) => {
  tree.forEach((node: any) => {
    node.children = node.chirldrenList;
    node.value = node.id;
    node.label = node.name;
    if (node.chirldrenList && node.chirldrenList.length > 0) {
      changeChildren(node.chirldrenList);
    }
  });
};
onMounted(() => {
  init();
  handleGetTypeTree();
  if (route.params.id) {
    // 说明是编辑
    form.eduResourceId = route.params.id as string;
    handleGetResourceDetail();
  } else if (userInfo) {
    // getTime();
    getUserInfoData();
  }
  handleGetStage();
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  padding: 0 24px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 4px;

  .title {
    padding: 18px 0;
    margin: 0;
    font-size: 16px;
    color: #262626;
    border-bottom: 1px solid #e5e5e5;
  }
}

.apply-form {
  @include flex-start-column;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 100px;
  width: 100%;
  padding-bottom: 32px;
  box-sizing: border-box;
  background: #fff;

  .title2 {
    width: 640px;
    font-size: 0;

    .line {
      display: inline-block;
      width: 3px;
      height: 16px;
      background: #007aff;
      vertical-align: middle;
    }

    span {
      margin-left: 4px;
      font-size: 14px;
      font-weight: bold;
      color: #262626;
      vertical-align: middle;
    }
  }

  .form {
    margin-top: 24px;

    .form-item {
      @include flex-start;
      margin-bottom: 24px;

      .form-title {
        @include flex-end;
        width: 72px;

        i {
          color: #f53f3f;
        }

        span {
          margin-left: 4px;
        }
      }

      .sutdyAddress {
        height: 58px;
        display: flex;
        margin-left: 12px;

        i {
          color: #f53f3f;
        }

        // border: 1px solid red;
      }

      .form-input {
        margin-left: 8px;
        position: relative;

        .input,
        .textarea {
          width: 328px;

          &.current {
            @include appearance-none;
          }
        }

        .time {
          width: 216px;

          &.current {
            @include appearance-none;
          }
        }

        .text {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  .upload-content {
    @include flex-start;
    align-items: flex-start;
    margin-left: 69px;
    margin-top: 24px;

    .small-title {
      margin-top: 8px;
      display: inline-block;
      width: 72px;
      text-align: right;

      i {
        color: red;
      }
    }

    .upload-info {
      margin-left: 8px;
      width: 408px;

      .upload-button {
        position: relative;
        width: 82px;
        height: 36px;

        .upload-file {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          cursor: pointer;
        }
      }

      .file-list {
        width: 100%;
        margin: 0;
        margin-top: 8px;
        list-style: none;

        li {
          @include flex-space-between;
          width: 100%;
          padding: 6px;
          box-sizing: border-box;
          margin-bottom: 8px;
          cursor: pointer;

          &:hover {
            background: #fafafa;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .icon {
            color: #8c8c8c;
            font-size: 16px;
          }

          .name {
            margin-left: 8px;
            color: #007aff;
          }
        }
      }

      .tips {
        margin-top: 8px;
        color: #8c8c8c;
      }
    }
  }
}

.apply-success {
  @include flex-center-column;
  width: 100%;
  height: 356px;

  .circle {
    @include flex-center;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: #17be6b;

    .icon {
      font-size: 36px;
      color: #fff;
    }
  }

  .title {
    margin: 0;
    margin-top: 24px;
    font-size: 24px;
    border-bottom: none;
  }

  .handler {
    margin-top: 24px;

    .current {
      margin-right: 16px;
    }
  }
}

.footer {
  @include flex-center;
  position: fixed;
  right: 0;
  bottom: 0;
  /* width: calc(100% - 200px); */
  height: 64px;
  background: #ffffff;
  box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;

  button {
    /* margin: 0 8px; */
    width: 104px;
  }
}
</style>
