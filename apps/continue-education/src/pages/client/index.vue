<template>
  <ys-loading :loading="loading" />
  <main class="clinet-layout">
    <section class="container">
      <div class="statistical-panel">
        <div class="statistical-info">
          <span class="name">欢迎您，{{ userInfo?.name || "" }}老师</span>
          <!-- <div class="statistical-date">
            <span>选择年度:</span>
            <div class="form-input">
              <a-range-picker class="input" picker="month" v-model:value="dateValue"
                @openChange="handleOpenChangePicker" />
            </div>
          </div> -->
          <div class="info_right">
            <div class="classHour" @click="goRegistrationCard(1)">
              <ysIcon
                type="iconshijian-xianxing"
                class="classHourIcon"
              ></ysIcon>
              <span>学时登记卡</span>
            </div>
            <div
              class="CreditHour"
              v-if="statData.classScore > 0"
              @click="goRegistrationCard(2)"
            >
              <ysIcon type="iconxing" class="CreditHourIcon"></ysIcon>
              <span>学分登记卡</span>
            </div>
            <a-dropdown v-if="yearList.length > 0">
              <a class="ant-dropdown-link" @click.prevent>
                {{ yearName }}年度
                <DownOutlined />
              </a>
              <template #overlay v-show="yearList.length > 0">
                <a-menu style="max-height: 170px; overflow: auto">
                  <a-menu-item
                    v-for="(item, index) in yearList"
                    :key="index"
                    @click="handleChangeYear(item, index)"
                  >
                    <!-- <a href="javascript:;"
                      >{{ item.startTime }}~{{ item.endTime }}</a
                    > -->
                    <a href="javascript:;">{{ item.name }}</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <!-- <a-dropdown v-else>
              <a class="ant-dropdown-link" @click.prevent>
                年度选择
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">暂无数据</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown> -->
          </div>
          <!-- <span v-else class="span1">暂无数据</span> -->
        </div>
        <ul class="statistical-list">
          <li @click="goRegistrationCard(1)" class="cursor">
            <img class="icon" :src="statIcon1" />
            <div class="title">
              <div class="number">{{ statData.classHour }}</div>
              <p class="name">学时总数</p>
            </div>
          </li>
          <li @click="goRegistrationCard(1)" class="cursor">
            <img class="icon" :src="statIcon2" />
            <div class="title">
              <div class="number">{{ statData.lhclassHour }}</div>
              <p class="name">灵活性学时申报总数</p>
            </div>
          </li>
          <li @click="goRegistrationCard(1)" class="cursor">
            <img class="icon" :src="statIcon6" />
            <div class="title">
              <div class="number">{{ statData.gfclassHour }}</div>
              <p class="name">规范性学时申报总数</p>
            </div>
          </li>
          <template v-if="statData.classScore > 0">
            <li @click="goRegistrationCard(2)">
              <img class="icon" :src="statIcon3" />
              <div class="title">
                <div class="number">{{ statData.classScore }}</div>
                <p class="name">学分总数</p>
              </div>
            </li>
            <li @click="goRegistrationCard(2)">
              <img class="icon" :src="statIcon4" />
              <div class="title">
                <div class="number">{{ statData.lhclassScore }}</div>
                <p class="name">灵活性学分申报总数</p>
              </div>
            </li>
            <li @click="goRegistrationCard(2)">
              <img class="icon" :src="statIcon5" />
              <div class="title">
                <div class="number">{{ statData.gfclassScore }}</div>
                <p class="name">规范性学分申报总数</p>
              </div>
            </li>
          </template>
        </ul>
      </div>

      <div class="bottom-panel">
        <div class="bottom-panel-left">
          <div class="jump-panel">
            <img :src="activitySignIcon" />
            <div class="title" @click="routerPush('/client/activityList')">
              <span>活动报名</span>
              <ysIcon class="icon" type="icon-arrow-down" />
            </div>
          </div>
          <div class="jump-panel current">
            <img :src="dataApplyIcon" />
            <div class="title" @click="routerPush('/client/apply-add')">
              <span>资料申报</span>
              <ysIcon class="icon" type="icon-arrow-down" />
            </div>
          </div>
          <div class="jump-panel current">
            <img :src="authQueryIcon" />
            <div class="title" @click="routerPush('/client/inquire')">
              <span>认证查询</span>
              <ysIcon class="icon" type="icon-arrow-down" />
            </div>
          </div>
          <div class="jump-panel current">
            <img :src="certificateIcon" />
            <div
              class="title"
              @click="
                routerPush(`/client/myCertificate?nowYearId=${isNowYearId}`)
              "
            >
              <span>学年证书</span>
              <ysIcon class="icon" type="icon-arrow-down" />
            </div>
          </div>
        </div>
        <div class="bottom-panel-right">
          <div class="handline">
            <div class="handline-left">
              <h3 class="title">申报列表</h3>
              <ul class="date-list">
                <li
                  v-for="item in dateList"
                  :key="item.index"
                  :class="{ active: item.index == currentDateIndex }"
                  @click="handleChangeDataItem(item.index)"
                >
                  {{ item.text }}
                </li>
              </ul>
            </div>
            <div class="handline-right" v-if="auditPerson.length > 0">
              <a-button type="primary" @click="routerPush('/client/audit')"
                >待审核</a-button
              >
              <div class="dot">{{ unAuditCount }}</div>
            </div>
          </div>

          <a-table
            style="margin-top: 8px"
            :columns="columns"
            :data-source="mylist"
            @change="handleTabSort2"
            :pagination="false"
          >
            <template v-slot:headerCell="{ column, record, index }">
              <div class="table-title">{{ column.title }}</div>
            </template>
            <template v-slot:bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'status'">
                <div class="table-status-carpe">
                  <i
                    class="dot"
                    :class="computedStatusText(record.status).className"
                  ></i>
                  <span>{{ computedStatusText(record.status).text }}</span>
                </div>
              </template>
            </template>
          </a-table>
          <div class="pagination">
            <a-pagination
              :simple="true"
              v-model:current="pageNo"
              :pageSize="pageSize"
              :total="resourceTotal"
              @change="handlePaginationChange"
            />
          </div>
        </div>

        <div
          class="fixButton"
          v-if="
            setPermise == 1 && !!navData.length && current == '/client/index'
          "
          @click="handlejump"
        >
          <div>
            <ysIcon
              type="iconshezhi3"
              style="color: #007aff; font-size: 20px"
            />
          </div>
          <div>管理</div>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup lang="ts">
import { ysIcon, ysLoading } from "@ys/ui";
import { common } from "@ys/tools";
import dayjs, { Dayjs } from "dayjs";
import { DownOutlined } from "@ant-design/icons-vue";
import statIcon1 from "@/assets/image/stat-icon1.png";
import statIcon2 from "@/assets/image/stat-icon2.png";
import statIcon3 from "@/assets/image/stat-icon3.png";
import statIcon4 from "@/assets/image/stat-icon4.png";
import statIcon5 from "@/assets/image/stat-icon5.png";
import statIcon6 from "@/assets/image/stat-icon6.png";
import activitySignIcon from "@/assets/image/activity-sign-icon.png";
import authQueryIcon from "@/assets/image/auth-query-icon.png";
import dataApplyIcon from "@/assets/image/data-apply-icon.png";
import certificateIcon from "@/assets/image/certificateIcon.png";
import { ref, reactive, computed, watch, onMounted } from "vue";
import useRouterUtils from "@/hooks/useRouterUtils";
import useResourceList from "@/hooks/useResourceList";
import {
  getIndexData,
  getIndexYear,
  getSystemSet,
  getResourceList,
  getUnAuditCount,
} from "@/request";
import useAuditPerson from "@/hooks/useAuditPerson";
import useAdminPermise from "@/hooks/usePermiseData";
import { doUserSetting } from "@/request";
const { getSessionUser } = common;
const userInfo = getSessionUser() as any;
const { navData, currentOneAdmin, getPermise } = useAdminPermise();
const { routerPush, route } = useRouterUtils();
const {
  loading,
  // resourceParams,
  // resourceTotal,
  // unAuditCount,
  computedStatusText,
  getResouceAuditList,
  // handleTabSort2,
  // handlePaginationChange,
} = useResourceList();

const { auditPerson, handleGetAuditPerson } = useAuditPerson();

interface yearValueProp {
  name: string;
  value: string;
}
const resourceTotal = ref(1);
const pageNo = ref(1);
const pageSize = ref(6);
const currentDateIndex = ref(1);
const dateValue = ref<any>();
const yearList = ref<any>([]);
const yearName = ref("");
const yearMonthName = ref("");
const dateList = [
  {
    index: 1,
    text: "近3个月",
    month: "3",
  },
  {
    index: 2,
    text: "近半年",
    month: "6",
  },
  {
    index: 3,
    text: "近一年",
    month: "12",
  },
];
const unAuditCount = ref(0);

const mylist = ref<any>([]);
const setPermise = ref<number>(1);
const current = ref<string>("/client/index");
const currentDateItem = computed(() => {
  const value = dateList.find((item) => {
    return item.index == currentDateIndex.value;
  });
  return value;
});
const isNowYearId = ref("");
const sortType = ref(2);
const columns = [
  {
    title: "申报项目",
    dataIndex: "resourceName",
    key: "resourceName",
    ellipsis: true,
  },
  {
    title: "认证时间",
    dataIndex: "createTime",
    key: "createTime",
    sorter: true,
  },
  {
    title: "状态",
    key: "status",
    dataIndex: "status",
  },
];

const statData = reactive({
  classHour: 0,
  gfclassHour: 0,
  lhclassHour: 0,
  classScore: 0,
  gfclassScore: 0,
  lhclassScore: 0,
});
const handlejump = () => {
  routerPush(currentOneAdmin.value);
};
const handlePaginationChange = (page: number, pageS: number) => {
  pageNo.value = page;
  pageSize.value = pageS;
  getMyAccecptList();
};
const handleTabSort2 = (page: any, data: any, order: any) => {
  sortType.value = order.order == "ascend" ? 1 : 2;
  pageNo.value = 1;
  getMyAccecptList();
};
const getResouceUnAuditCount = async () => {
  const response = await getUnAuditCount();
  const res = response.data as any;
  if (res.code == 0 && res.success) {
    unAuditCount.value = res.data;
  }
};
const getMyAccecptList = async () => {
  let params = {
    name: null,
    startTime: null,
    endTime: null,
    month: currentDateItem.value?.month,
    type: null,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    status: null,
    sortType: sortType.value,
    queryType: 3,
    userId: userInfo.id,
    dataSource: null,
  };
  const res: any = await getResourceList(params);
  mylist.value = res.data.data;
  resourceTotal.value = Number(res.data.totalDatas);
  getResouceUnAuditCount();
  console.log(res.data);
};
const getTime = async () => {
  try {
    const response = await getSystemSet();
    const res = response.data as any;
    yearList.value = res.data.map((item: any, key: number) => {
      item.index = key + 1;
      if (item.name == null) {
        item.name = `${item.startTime.split("-")[0]}-${
          item.endTime.split("-")[0]
        }`;
      }
      return item;
    });
    if (res.data && res.data.length > 0) {
      res.data.forEach((item: any) => {
        if (item.isNow == 1) {
          isNowYearId.value = item.eduSystemSetId;
        }
      });
    }
    console.log("yearList", yearList.value);

    let one = yearList.value.find((item: any) => {
      return item.isNow == 1;
    });
    // let obj1 = { startTime: res.data.data.startTime, endTime: res.data.data.endTime } as any
    // let start = `${res.data.data.startTime.split('-')[0] - 1}-${res.data.data.startTime.split('-')[1]}`
    // let endTime = `${res.data.data.endTime.split('-')[0] - 1}-${res.data.data.endTime.split('-')[1]}`
    // let obj2 = { startTime: start, endTime: endTime } as any
    // yearList.value.push(obj1)
    // yearList.value.push(obj2)
    // console.log(yearList.value)
    // const res2 = (await getIndexYear()) as any;
    // console.log("res2", res2);
    // yearList.value = res.data.data;
    let start1 = one.startTime;
    let end1 = one.endTime;
    yearName.value = `${one.name}`;
    yearMonthName.value = `${start1}~${end1}`;
    handleGetEduIndexData();
  } catch (error) {}
};
// const handleGetEduYearList = async () => {
//   try {
//     const response = await getIndexYear();
//     const res = response.data as any;
//     if (res.code == 0 && res.success) {
//       let data = res.data as string[];
//       if (data.length > 0) {
//         data.forEach((item) => {
//           let option = {
//             name: "",
//             value: "",
//           };
//           option.value = item;
//           option.name = `${Number(item)}~${Number(item) + 1}年度`;
//           yearList.value.push(option);
//         });

//         yearName.value = yearList.value[0].name;
//         handleGetEduIndexData(yearList.value[0].value);
//       }
//     }
//   } catch (error) {}
// };

const handleChangeYear = (year: any, index: number) => {
  yearName.value = year.name;
  yearMonthName.value = `${year.startTime}~${year.endTime}`;
  handleGetEduIndexData();
};

const dateValueWatch = watch(
  () => dateValue.value,
  (newV) => {
    if (newV == null) {
      handleGetEduIndexData();
    }
  }
);
const goRegistrationCard = (val: any) => {
  routerPush(
    `/client/registration-card?type=${Number(val)}&time=${yearMonthName.value}&yearName=${yearName.value}`
  );
};
// const handleOpenChangePicker = (status: any) => {
//   if (
//     status == false &&
//     dateValue.value &&
//     dateValue.value[0] &&
//     dateValue.value[1]
//   ) {
//     handleGetEduIndexData();
//   }
// };

const handleGetEduIndexData = async () => {
  try {
    let startTime = yearMonthName.value
      ? `${yearMonthName.value.split("~")[0]}`
      : "";
    let endTime = yearMonthName.value
      ? `${yearMonthName.value.split("~")[1]}`
      : "";
    loading.value = true;
    const response = await getIndexData({
      startTime,
      endTime,
    });
    loading.value = false;
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      const data = res.data;
      if (data) {
        statData.classHour = data.classHour || 0;
        statData.lhclassHour = data.lhclassHour || 0;
        statData.gfclassHour = data.gfclassHour || 0;
        statData.classScore = data.classScore || 0;
        statData.gfclassScore = data.gfclassScore || 0;
        statData.lhclassScore = data.lhclassScore || 0;
      } else {
        statData.classHour = 0;
        statData.lhclassHour = 0;
        statData.gfclassHour = 0;
        statData.classScore = 0;
        statData.gfclassScore = 0;
        statData.lhclassScore = 0;
      }
    }
  } catch (error) {}
};

const handleChangeDataItem = (index: number) => {
  currentDateIndex.value = index;
  getMyAccecptList();
  // handleInitData(false);
};

// const handleInitData = (immediate: boolean = true) => {
//   if (currentDateItem.value) {
//     resourceParams.month = currentDateItem.value.month;
//     if (!immediate) {
//       resourceParams.pageNo = 1;
//     }
//     resourceParams.pageSize = 6;
//     getResouceAuditList(true);
//   }
// };

onMounted(() => {
  getPermise();
  if (route.matched.some((v: any) => v.path == "/admin"))
    current.value = "/admin";
  doUserSetting().then((res: any) => {
    if (res.data.code == 0) {
      setPermise.value = res.data.data.studioSetting == 1 ? 1 : 2;
    }
  });
  if (userInfo) {
    // resourceParams.userId = userInfo.id || null;
    handleGetAuditPerson(userInfo.id);
    getMyAccecptList();
  }
  // handleGetEduYearList();

  // handleInitData();
  getTime();
});
</script>

<style lang="scss" scoped>
.clinet-layout {
  width: 100%;
  background: #f0f2f5;
  min-height: calc(100vh - 60px);
  overflow: auto;

  .container {
    margin: 24px auto;
    width: 1280px;

    .statistical-panel {
      width: 100%;
      padding: 0 24px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 4px;

      .statistical-info {
        @include flex-space-between;
        width: 100%;
        height: 64px;
        border-bottom: 1px solid #e5e5e5;

        .name {
          font-size: 18px;
          color: #262626;
        }

        .info_right {
          display: flex;
          cursor: pointer;

          .classHour,
          .CreditHour {
            span {
              margin-left: 3px;
            }

            margin-right: 26px;
          }

          .classHour {
            // &:hover.classHourIcon {
            //   color: #007aff
            // }

            .classHourIcon {
              &:hover {
                color: #007aff;
              }
            }

            &:hover {
              color: #007aff;
            }
          }

          .CreditHour {
            .CreditHourIcon {
              &:hover {
                color: #007aff;
              }
            }

            &:hover {
              color: #007aff;
            }
          }
        }

        .statistical-date {
          @include flex-start;

          span {
            margin-right: 4px;
          }
        }
      }

      .statistical-list {
        @include flex-start;
        flex-wrap: wrap;
        width: 100%;
        padding: 32px 0;
        box-sizing: border-box;

        li {
          @include flex-start;
          margin-bottom: 32px;
          width: 33%;
          height: 56px;
          border-right: 1px solid #e5e5e5;
          box-sizing: border-box;

          &:nth-child(3n) {
            border-right-color: transparent;
          }

          &:nth-last-child(-n + 3) {
            margin-bottom: 0;
          }

          .icon {
            margin-left: 36px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
          }

          .title {
            margin-left: 16px;

            .number {
              font-size: 24px;
              font-weight: bold;
              color: #262626;
              line-height: 28px;
            }

            .name {
              margin-top: 4px;
              color: #595959;
              margin: 0;
            }
          }
        }
      }
    }

    .bottom-panel {
      @include flex-start;
      margin-top: 24px;
      width: 100%;
      position: relative;

      .fixButton {
        position: absolute;
        top: 0;
        right: -82px;
        width: 56px;
        height: 56px;
        // line-height: 56px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #e5e5e5;
        text-align: center;
        padding-top: 9px;
        cursor: pointer;
      }

      .bottom-panel-left {
        width: 410px;

        .jump-panel {
          @include flex-center-column;
          width: 100%;
          height: 161px;
          border-radius: 4px;
          background: #fff;
          &:hover {
            color: #007aff;
            // .title {

            // }
          }
          &.current {
            margin-top: 15px;

            // .title {
            //   color: #262626;
            // }
          }

          img {
            width: 64px;
            height: 64px;
          }

          .title {
            margin-top: 16px;
            cursor: pointer;

            span {
              font-weight: bold;
              font-size: 18px;
            }

            .icon {
              font-size: 16px;
            }
          }
        }
      }

      .bottom-panel-right {
        margin-left: 24px;
        width: 846px;
        height: 690px;
        padding: 24px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 4px;

        .handline {
          @include flex-space-between;
          width: 100%;

          .handline-left {
            @include flex-start;

            .title {
              margin: 0;
              font-size: 16px;
              font-weight: bold;
              color: #262626;
            }

            .date-list {
              @include flex-start;
              margin: 0;
              margin-left: 24px;
              list-style: none;

              li {
                margin-right: 16px;
                color: #595959;
                cursor: pointer;

                &.active {
                  color: #007aff;
                }
              }
            }
          }

          .handline-right {
            position: relative;

            .dot {
              position: absolute;
              right: -15px;
              top: -8px;
              padding: 2px 8px;
              box-sizing: border-box;
              font-size: 12px;
              color: #fff;
              background: #ff4d4f;
              border-radius: 100px;
              border: 1px solid #ffffff;
            }
          }
        }

        .pagination {
          @include flex-end;
          margin-top: 16px;
        }
      }
    }
  }
}

.table-title {
  color: #262626;
  font-weight: bold;
}

.table-status {
  @include flex-start;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ffaa00;
  }

  span {
    margin-left: 12px;
    color: #262626;
  }
}
</style>
