<template>
  <ys-loading :loading="loading" />
  <div class="certificateBox scrollbar">
    <div class="myCertificate">
      <div class="head">
        <div v-if="!showCountdown">
          <span class="cursor" @click="router.back()" style="color: #007aff"
            >首页&nbsp;/&nbsp;</span
          >
          <span>我的证书</span>
        </div>
        <div v-else>
          <span
            class="cursor"
            @click="showCountdown = false"
            style="color: #007aff"
            >我的证书&nbsp;/&nbsp;</span
          >
          <span>跨年证书</span>
        </div>
      </div>
      <div class="certificateTable" v-if="!showCountdown">
        <div class="table_head">
          <div class="left">
            <div
              class="cursor leftNav"
              @click="changeNav(item.id)"
              v-for="(item, index) in nav"
              :key="index"
            >
              <div
                class="item"
                :style="{
                  borderBottom: active == item.id ? '3px solid #007AFF' : '',
                  color: active == item.id ? '#007AFF' : '',
                }"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
          <div class="rightBtn">
            <a-button @click="handleChangePage"
              >跨年证书<span style="margin-left: 4px">{{
                countdownTotal && countdownTotal > 0 ? countdownTotal : null
              }}</span></a-button
            >
            <a-button
              :disabled="goingCollect"
              style="margin-left: 8px"
              @click="handleCollectYear"
              type="primary"
              >汇总学年</a-button
            >
          </div>
        </div>
        <div class="tableContent">
          <a-table
            :dataSource="list"
            row-key="eduSystemSetId"
            :columns="columns"
            :pagination="false"
            :rowSelection="{
              selectedRowKeys,
              onSelect: onChooseChange,
              onSelectAll: onChooseAllChange,
            }"
          >
            <template #headerCell="{ text, column, record, index }">
              <template v-if="column.key === 'totalData'">
                {{ active == 1 ? "总学时" : "总学分" }}
              </template>
            </template>
            <template #bodyCell="{ text, column, record, index }">
              <template v-if="column.key === 'index'">
                {{ index + 1 }}
              </template>
              <template v-if="column.key === 'startTime'">
                {{ record.name }}
              </template>
              <template v-if="column.key === 'examine'">
                <div
                  :class="[record.examine ? 'qualified' : 'disqualification']"
                >
                  {{ record.examine ? "合格" : "不合格" }}
                </div>
              </template>
              <template
                v-if="
                  column.key === 'action' &&
                  isrange(record.startTime, record.endTime) &&
                  record.examine
                "
              >
                <span
                  class="cursor"
                  @click="CertificatePreview(1, record)"
                  style="color: #007aff"
                  >证书查看</span
                >
                <!-- <span
                  class="cursor"
                  @click="downCertificate(record)"
                  style="margin-left: 16px; color: #007aff"
                  >证书下载</span
                > -->
              </template>
            </template>
          </a-table>
          <ysPagination
            :pageNo="pagination.pageNo"
            :pageSize="pagination.pageSize"
            :total="total"
            @change="pageChange"
          />
        </div>
      </div>
      <div class="countdown" v-else>
        <div style="margin-bottom: 16px">跨年证书</div>
        <a-table
          :dataSource="countdownList"
          row-key="id"
          :columns="countdownColumns"
          :pagination="false"
        >
          <template #bodyCell="{ text, column, record, index }">
            <template v-if="column.key === 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.key === 'startTime'">
              {{ record.startTime }}-{{ record.endTime }}
            </template>
            <template v-if="column.key === 'action'">
              <span
                class="cursor"
                @click="CertificatePreview(2, record)"
                style="color: #007aff"
                >查看</span
              >
              <span
                class="cursor"
                @click="delCertificate(record)"
                style="margin-left: 16px; color: #007aff"
                >删除</span
              >
            </template>
          </template>
        </a-table>
        <ysPagination
          :pageNo="countdownPagination.pageNo"
          :pageSize="countdownPagination.pageSize"
          :total="countdownTotal"
          @change="pageChange1"
        />
      </div>
    </div>
    <div class="previewBox" v-if="showCertificate">
      <div class="closeBox" @click="showCertificate = false">
        <close-outlined />
      </div>
      <img :src="isCdn(certificateUrl)" alt="" />
    </div>
  </div>

  <a-modal v-model:visible="open" :title="titleName" :footer="null">
    <div
      style="
        width: 100%;
        min-height: 590px;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <iframe
        class="iframeBox"
        type="application/x-google-chrome-pdf"
        frameborder="0"
        style="width: 375px; height: 545px"
        :src="iframeUrl"
      ></iframe>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { CloseOutlined } from "@ant-design/icons-vue";
import { ysLoading, ysPagination } from "@ys/ui";
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import { useRouter } from "vue-router";
import {
  lookCertify,
  getHourCertify,
  getScoreCertify,
  yearsCertificate,
  yearsCertificateList,
  lookYearsCertificate,
  delYearsCertificate,
  getYearsCertificateSystemSet,
} from "@/request";
import { common } from "@ys/tools";
import { message, Modal } from "ant-design-vue";
const router = useRouter();
const { down, isCdn, bureauBlankOpen } = common;

/* --------------- data --------------- */
//#region
const pagination = ref<any>({
  pageNo: 1,
  pageSize: 10,
});
const countdownPagination = ref<any>({
  pageNo: 1,
  pageSize: 10,
});
const goingCollect = ref(false);
const loading = ref(false);
const nav = ref<any>([
  { id: 1, name: "学时证书" },
  { id: 2, name: "学分证书" },
]);
const titleName = ref<any>("证书预览");
const selectedRowKeys = ref<any>([]);
const showCertificate = ref(false);
const active = ref<any>(1);
const total = ref(0);
const countdownTotal = ref(0);
const list = ref<any>([]);
const countdownList = ref<any>([]);
const columns: any = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "left",
    width: 60,
  },
  {
    title: "学年度",
    dataIndex: "name",
    key: "name",
    align: "left",
  },
  {
    align: "left",
    title: "规范性",
    dataIndex: "gfData",
    key: "gfData",
  },
  {
    align: "left",
    title: "灵活性",
    dataIndex: "lhData",
    key: "lhData",
  },
  {
    align: "left",
    title: "总学时",
    dataIndex: "totalData",
    key: "totalData",
  },
  {
    align: "left",
    title: "考核要求",
    dataIndex: "examine",
    key: "examine",
  },
  {
    align: "left",
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 168,
  },
];
const countdownColumns = ref<any>([
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "left",
    width: 60,
  },
  {
    title: "学年度",
    dataIndex: "name",
    key: "name",
    align: "left",
  },
  {
    align: "left",
    title: "规范性",
    dataIndex: "gfHour",
    key: "gfHour",
  },
  {
    align: "left",
    title: "灵活性",
    dataIndex: "lhHour",
    key: "lhHour",
  },
  {
    align: "left",
    title: "总学时",
    dataIndex: "totalData",
    key: "totalData",
  },
  {
    align: "left",
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 168,
  },
]);
const certificateUrl = ref("");
const iframeUrl = ref<any>("");
const open = ref(false);
const nowYearId = ref<any>("");
const yearList = ref<any>([]);
const showCountdown = ref(false);
// 手动选中/取消所有列
const onChooseAllChange = (
  selected: any,
  selectedRows: any,
  changeRows: any
) => {
  if (!selected) {
    for (let i = 0; i < selectedRowKeys.value.length; i++) {
      for (let j = 0; j < changeRows.length; j++) {
        if (selectedRowKeys.value[i] == changeRows[j].eduSystemSetId) {
          selectedRowKeys.value.splice(i, 1);
          i--;
        }
      }
    }
  } else {
    for (let i = 0; i < selectedRows.length; i++) {
      for (let j = 0; j < changeRows.length; j++) {
        if (selectedRows[i] != undefined) {
          if (selectedRows[i].eduSystemSetId == changeRows[j].eduSystemSetId) {
            selectedRowKeys.value.push(selectedRows[i].eduSystemSetId);
          }
        }
      }
    }
  }
};

// 手动选中/取消单列
const onChooseChange = (rows: any, boo: any) => {
  if (!boo) {
    for (let i = 0; i < selectedRowKeys.value.length; i++) {
      if (selectedRowKeys.value[i] == rows.eduSystemSetId) {
        selectedRowKeys.value.splice(i, 1);
      }
    }
  } else {
    selectedRowKeys.value.push(rows.eduSystemSetId);
  }
};
//#endregion
/* --------------- methods --------------- */
//#region
onMounted(() => {
  if (localStorage.getItem("certificateActive") == null) {
    localStorage.setItem("certificateActive", active.value);
  }
  if (localStorage.getItem("showCountdown") == "1") {
    showCountdown.value = true;
  }
  active.value = localStorage.getItem("certificateActive");
  nowYearId.value = router.currentRoute.value.query.nowYearId;
  getinfo(active.value);
  getYearCertificate(active.value);
  getSystemSetData();
});
onBeforeUnmount(() => {
  localStorage.removeItem("certificateActive");
  localStorage.removeItem("showCountdown");
});
// 学年证书删除
const delCertificate = (item: any) => {
  console.log("学年证书删除");
  delYearsCertificate({ id: item.eduUserCertifyId }).then((res: any) => {
    if (res.data.code == 0 && res.data.success) {
      message.success("已删除该证书");
      getYearCertificate(active.value);
    }
  });
};
const handleChangePage = () => {
  showCountdown.value = true;
  localStorage.setItem("showCountdown", "1");
};
const getSystemSetData = async () => {
  const response = (await getYearsCertificateSystemSet()) as any;
  const res = response.data;
  if (res.code === 0) {
    yearList.value = res.data;
  }
};
// 汇总学年
const handleCollectYear = () => {
  if (selectedRowKeys.value.length == 0) {
    message.warning("请先勾选要汇总的年度");
  } else if (
    selectedRowKeys.value.length > 0 &&
    selectedRowKeys.value.length < 2
  ) {
    message.warning("至少勾选2项形成新的证书");
  } else {
    goingCollect.value = true;
    let start = "";
    let end = "";
    let zancun1 = null;
    let zancun2 = null;
    for (let i of yearList.value) {
      if (i.eduSystemSetId == selectedRowKeys.value[0]) {
        zancun1 = i;
      }
      if (
        i.eduSystemSetId ==
        selectedRowKeys.value[selectedRowKeys.value.length - 1]
      ) {
        zancun2 = i;
      }
    }
    if (zancun1.endTime.split("-")[0] >= zancun2.startTime.split("-")[0]) {
      start = `${zancun2.startTime.split("-")[0]}学年${zancun2.startTime.split("-")[1]}月`;
      end = `${zancun1.endTime.split("-")[0]}学年${zancun1.endTime.split("-")[1]}月`;
      if (zancun1.endTime.split("-")[0] == zancun2.startTime.split("-")[0]) {
        if (zancun1.endTime.split("-")[1] >= zancun2.startTime.split("-")[1]) {
          start = `${zancun2.startTime.split("-")[0]}学年${zancun2.startTime.split("-")[1]}月`;
          end = `${zancun1.endTime.split("-")[0]}学年${zancun1.endTime.split("-")[1]}月`;
        } else {
          start = `${zancun1.startTime.split("-")[0]}学年${zancun1.startTime.split("-")[1]}月`;
          end = `${zancun2.endTime.split("-")[0]}学年${zancun2.endTime.split("-")[1]}月`;
        }
      }
    } else {
      start = `${zancun1.startTime.split("-")[0]}学年${zancun1.startTime.split("-")[1]}月`;
      end = `${zancun2.endTime.split("-")[0]}学年${zancun2.endTime.split("-")[1]}月`;
    }
    nextTick(() => {
      Modal.confirm({
        title: "确定汇总",
        content: `你确定汇总${start}-${end}的学时，生成证书吗？`,
        onOk: () => {
          let params = {
            eduSystemSetIds: selectedRowKeys.value,
            type: active.value,
          };
          yearsCertificate(params).then((res: any) => {
            if (res.data.code == 0 && res.data.success) {
              message.success("生成跨年证书成功");
              goingCollect.value = false;
              selectedRowKeys.value = [];
              getYearCertificate(active.value);
            } else {
              goingCollect.value = false;
            }
          });
        },
        onCancel() {
          goingCollect.value = false;
          selectedRowKeys.value = [];
        },
      });
    });
  }
};
// 获取跨年证书
const getYearCertificate = (num: any) => {
  let params = {
    pageNo: countdownPagination.value.pageNo,
    pageSize: countdownPagination.value.pageSize,
    type: num,
  };
  yearsCertificateList(params).then((res: any) => {
    if (res.data.code == 0 && res.data.success) {
      countdownList.value = res.data.data.map((item: any) => {
        const start = item.startTime.split("-")[0];
        const end = item.endTime.split("-")[0];
        item.name = `${start}-${end}`;
        item.totalData = Number(item.lhHour) + Number(item.gfHour);
        return item;
      });
      countdownTotal.value = res.data.data ? Number(res.data.totalDatas) : 0;
    }
  });
};
function isrange(s: any, e: any) {
  const nowYear = new Date().getFullYear();
  if (s <= nowYear && e >= nowYear) return false;
  else return true;
}
const changeNav = (val: any) => {
  active.value = val;
  localStorage.setItem("certificateActive", active.value);

  pagination.value.pageNo = 1;
  pagination.value.pageSize = 10;
  list.value = [];
  total.value = 0;
  selectedRowKeys.value = [];
  getinfo(val);
  getYearCertificate(val);
};
function pageChange(pageNo: number, pageSize: number) {
  pagination.value.pageNo = pageNo;
  pagination.value.pageSize = pageSize;
  getinfo(1);
}
function pageChange1(pageNo: number, pageSize: number) {
  countdownPagination.value.pageNo = pageNo;
  countdownPagination.value.pageSize = pageSize;
  getYearCertificate(active.value);
}
async function getinfo(val: any) {
  let params = {
    pageNo: pagination.value.pageNo,
    pageSize: pagination.value.pageSize,
    systemSetId: nowYearId.value,
  };
  if (val == 1) {
    const res1 = (await getHourCertify(params)) as any;
    list.value = res1.data.data.map((item: any) => {
      // const start = item.startTime.split("-")[0];
      // const end = item.endTime.split("-")[0];
      // item.name = `${start}-${end}`;
      return item;
    });
    total.value = res1.data.data ? Number(res1.data.totalDatas) : 0;
  } else {
    const res2 = (await getScoreCertify(params)) as any;
    list.value = res2.data.data.map((item: any) => {
      // const start = item.startTime.split("-")[0];
      // const end = item.endTime.split("-")[0];
      // item.name = `${start}-${end}`;
      return item;
    });
    total.value = res2.data.data ? Number(res2.data.totalDatas) : 0;
  }
}
function downCertificate(val: any) {
  console.log("downCertificate", val);
  // if (val.certificateUrl) {
  //   certificateUrl.value = val.certificateUrl;
  //   down(
  //     isCdn(certificateUrl.value),
  //     active.value == 1 ? "学时证书" : "学分证书"
  //   );
  // } else {
  //   let params = {
  //     gfHour: val.gfData,
  //     lhHour: val.lhData,
  //     startTime: val.startTime,
  //     endTime: val.endTime,
  //     classHour: val.totalData,
  //     type: active.value == 1 ? 1 : 2,
  //   };
  //   lookCertify(params).then((res: any) => {
  //     if (res.data.code == 0 && res.data.success) {
  //       certificateUrl.value = res.data.msg;
  //       down(
  //         isCdn(certificateUrl.value),
  //         active.value == 1 ? "学时证书" : "学分证书"
  //       );
  //     }
  //   });
  // }
  let params = {
    gfHour: val.gfData,
    lhHour: val.lhData,
    startTime: val.startTime,
    endTime: val.endTime,
    classHour: val.totalData,
    type: active.value == 1 ? 1 : 2,
  };
  lookCertify(params).then((res: any) => {
    if (res.data.code == 0 && res.data.success) {
      certificateUrl.value = res.data.msg;
      down(
        isCdn(certificateUrl.value),
        active.value == 1 ? "学时证书" : "学分证书"
      );
    }
  });
}
// 证书查看
const CertificatePreview = (num: any, val: any) => {
  if (num == 1) {
    open.value = false;
    titleName.value = "证书预览";
    loading.value = true;
    iframeUrl.value = null;
    let params = {
      gfHour: showCountdown.value == false ? val.gfData : val.gfHour,
      lhHour: showCountdown.value == false ? val.lhData : val.lhHour,
      startTime: val.startTime,
      endTime: val.endTime,
      classHour: val.totalData,
      type: active.value == 1 ? 1 : 2,
      systemSetId: nowYearId.value,
    };
    lookCertify(params).then((res: any) => {
      if (res.data.code == 0 && res.data.success) {
        loading.value = false;
        const result = res.data.data;
        const { origin } = location;
        open.value = true;
        const url = `${origin}/yskt/certify/#${result}`;
        // const url = `https://ysotjxyt.ysclass.net/yskt/certify/#${result}`;
        iframeUrl.value = url;
      } else {
        loading.value = false;
      }
    });
  } else {
    open.value = false;
    titleName.value = "合并证书";
    loading.value = true;
    iframeUrl.value = null;
    let params = {
      gfHour: showCountdown.value == false ? val.gfData : val.gfHour,
      lhHour: showCountdown.value == false ? val.lhData : val.lhHour,
      startTime: val.startTime,
      endTime: val.endTime,
      classHour: val.totalData,
      type: active.value == 1 ? 1 : 2,
      systemSetId: nowYearId.value,
      certifyType: 2,
    };
    lookYearsCertificate(params).then((res: any) => {
      if (res.data.code == 0 && res.data.success) {
        loading.value = false;
        const result = res.data.data;
        const { origin } = location;
        open.value = true;
        const url = `${origin}/yskt/certify/#${result}`;
        iframeUrl.value = url;
      } else {
        loading.value = false;
      }
    });
  }
};

//#endregion
</script>

<style lang="scss" scoped>
.certificateBox {
  width: 100vw;
  height: calc(100vh - 60px);
  background: #f0f2f5;
  overflow: auto;

  .myCertificate {
    max-width: 1280px;
    margin: 0 auto;
    padding: 24px;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    background: #f0f2f5;
  }

  .certificateTable {
    margin-top: 16px;
    background: #ffffff;
    border-radius: 4px;
    padding: 17px 24px 24px 24px;

    .table_head {
      display: flex;
      height: 39px;
      border-bottom: 1px solid #f0f0f0;
      justify-content: space-between;
      align-items: center;

      .left {
        display: flex;
        align-items: center;

        .leftNav {
          display: flex;
          align-items: center;
          margin-right: 32px;

          .item {
            height: 39px;
          }
        }
      }

      .rightBtn {
        margin-top: -12px;
      }
    }

    .tableContent {
      margin-top: 20px;

      .qualified {
        display: inline-block;
        padding: 6px;
        background: rgba(23, 190, 107, 0.06);
        border-radius: 4px 4px 4px 4px;
        color: #17be6b;
        border: 1px solid #17be6b;
      }

      .disqualification {
        display: inline-block;
        padding: 6px;
        background: rgba(245, 63, 63, 0.08);
        border-radius: 4px 4px 4px 4px;
        color: #f53f3f;
        border: 1px solid #f53f3f;
      }
    }
  }

  .countdown {
    margin-top: 16px;
    background: #ffffff;
    border-radius: 4px;
    padding: 17px 24px 24px 24px;
  }

  .previewBox {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    display: flex;
    justify-content: center;
    align-items: center;

    .closeBox {
      width: 36px;
      height: 36px;
      background: #000000;
      border-radius: 2px 2px 2px 2px;
      // opacity: 0.4;
      color: #ffffff;
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .iframeBox {
    img {
      height: 400px;
    }
  }
}
</style>
