<template>
  <ys-loading :loading="loading" />
  <main class="activity-layout">
    <section class="container">
      <pageHeader :prveTitle="'首页'" :currentTitle="'项目报名'" :goHome="true" />
      <div class=search_header>
        <div class="searchItem">
          <span>项目名称：</span>
          <a-input-search style="width:328px" v-model:value="activityName" placeholder="请输入" @search="onSearch" />
        </div>
        <div class="searchItem ml-27">
          <span>报名类型：</span>
          <a-select ref="select" placeholder="请选择" allowClear style="width:328px" v-model:value="signType"
            :options="signOption" @change="handleChange"></a-select>
        </div>
      </div>
      <div class="activity-content" ref="signProject" v-if="list && list.length > 0">
        <div class='activityItem' v-for="(item, index) in list" :key="index">
          <div class="item_top">
            <img :src="getImg(item.activityType)" alt="">
            <div class="status" :class="[item.isEnd == false ? 'status1' : 'status2']">{{ item.isEnd == false
              ? '进行中' :
              '已结束' }}</div>
            <div class="item_top_right">
              <div class="projectName text_overflow" :title="item.signAddress">
                {{ item.resourceName }}
              </div>
              <div class="activityPeriod text_overflow">
                <span>项目时段：</span>
                <span>{{ getStartime(item.startTime) }}~{{ item.endTime }}</span>
              </div>
              <div class="signPeriod activityPeriod">
                <span>签到时段：</span>
                <div class="fiexTime" v-if="item.signTaskDates && item.signTaskDates.length > 0">
                  <div v-if="item.signType == 1 && item.signTaskDates[0].signTaskDateTimes">
                    <span>{{ item.signTaskDates[0].signTaskDateTimes[0].startTime }}-{{
                      item.signTaskDates[0].signTaskDateTimes[0].endTime }}</span>
                    <a-popover :getPopupContainer="() => $refs.signProject" trigger="click" placement="bottom"
                      overlayClassName="popoverRule">
                      <template #content>
                        <div class="allrule scrollbar">
                          <div class="ruleItem" v-for="(item2, index) in item.signTaskDates" :key="index">
                            <!-- <span>{{ item2.startTime }}~{{ item2.endTime }}</span> -->
                            <div class="week">
                              {{ item2.signDate }}
                            </div>
                            <div class="timeDates">
                              <div class="timeDatesItem" v-for="(item3, index) in item2.signTaskDateTimes">
                                {{ item3.startTime }}-{{ item3.endTime }}</div>
                            </div>
                          </div>
                        </div>

                      </template>
                      <span style="margin-left:8px"
                        v-if="item.signTaskDates[0].signTaskDateTimes && item.signTaskDates[0].signTaskDateTimes.length > 0">
                        <ysIcon type="iconshangjiantou" />
                      </span>
                    </a-popover>

                  </div>
                  <div v-else>
                    <span>{{ getOneWeek(item.signTaskDates[0].signWeek) }}</span>
                    <span style="margin-left:8px">{{ item.signTaskDates[0].signTaskDateTimes[0].startTime }}-{{
                      item.signTaskDates[0].signTaskDateTimes[0].endTime }}</span>
                    <a-popover :getPopupContainer="() => $refs.signProject" trigger="click" placement="bottom"
                      overlayClassName="popoverRule ">
                      <template #content>
                        <div class="allrule scrollbar">
                          <div class="ruleItem" v-for="(item2, index) in item.signTaskDates" :key="index">
                            <!-- <span>{{ item2.startTime }}~{{ item2.endTime }}</span> -->
                            <div class="week">
                              {{ getSignDate(item2.signWeek) }}
                            </div>
                            <div class="timeDates">
                              <div class="timeDatesItem" v-for="(item3, index) in item2.signTaskDateTimes">
                                {{ item3.startTime }}-{{ item3.endTime }}</div>
                            </div>
                          </div>
                        </div>

                      </template>
                      <span style="margin-left:8px" v-if="item.signTaskDates && item.signTaskDates.length > 0">
                        <ysIcon type="iconshangjiantou" />
                      </span>
                    </a-popover>

                  </div>
                </div>
              </div>
              <div class="signAddress activityPeriod">
                <span>活动地点：</span>
                <span>{{ item.signAddress }}</span>
              </div>
            </div>
          </div>
          <div class="item_bottom">
            <div class="bottom_left">
              <span>截止报名</span>
              <span>&nbsp;{{ item.endSignTime }}</span>
              <span v-if="item.isExpired">（已过期）</span>
            </div>
            <div class="bottom_right">
              <span>已报名{{ item.signNum }}人</span>
              <a-button type="primary" v-if="item.isSign == 0 && !item.isExpired" class="abutton"
                @click="openModel(item)">立即报名</a-button>
              <a-button v-if="item.isSign != 0" :disabled="item.isSign != 0" class="abutton">已报名</a-button>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="noempty">暂无报名项目</div>
      <div style="text-align: right; margin-top: 32px">
        <pagJump :pagData="pagData" @change="handlePageChange"></pagJump>
      </div>
    </section>
    <a-modal v-model:visible="showSignModal" style="width:448px" centered title="报名" :footer="null"
      wrapClassName="signModal">
      <div class=modalContent>
        <div class="resourceName text-overflow">
          {{ chooseSignName }}
        </div>
        <div style="margin-top: 24px;">
          未识别到您的身份信息，请完善身份信息
        </div>
        <div class="footer">
          <a-button @click="handlePersonCenter" type="primary">去完善</a-button>
        </div>
      </div>
    </a-modal>
  </main>
</template>

<script lang='ts' setup>
import { checkID, isPhoneNumber } from "@/utils/verifyIdCard";
import pagJump from "@/components/pagJump.vue";
import { ysLoading, ysPagination, ysIcon } from "@ys/ui";
import pageHeader from "@/components/page-header.vue";
import { ref, reactive, onMounted, createVNode, handleError } from 'vue'
import trianImg from "@/assets/image/trian.png"
import meetImg from "@/assets/image/meeting.png"
import activeImg from "@/assets/image/activity.png"
import { message, Modal } from "ant-design-vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { signActiveList, activitySign, checkSign } from "@/request"
import dayjs, { Dayjs } from "dayjs";
/* --------------- data --------------- */
//#region
const loading = ref(false)
const activityName = ref<any>(null)
const signType = ref<any>(null)
const signOption = ref<any>([
  { label: '活动', value: 1 },
  { label: '培训', value: 2 },
  { label: '会议', value: 3 }
])
const pagData = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 1,
});
const list = ref<any>([])
const showSignModal = ref(false)
const signBaseId = ref<any>(null)
const signResourceId = ref<any>(null)
const otherSignResourceId = ref<any>(null)
const chooseSignName = ref<any>(null)
const clashName = ref<any>(null)
const weekdays = ref<any>([
  { id: 1, name: '周一' },
  { id: 2, name: '周二' },
  { id: 3, name: '周三' },
  { id: 4, name: '周四' },
  { id: 5, name: '周五' },
  { id: 6, name: '周六' },
  { id: 7, name: '周天' }
])
//#endregion

/* --------------- methods --------------- */
//#region
const getStartime = (val: any) => {
  let time = null as any
  if (val) {
    time = dayjs(val).format("YYYY-MM-DD HH:mm");
  }
  else {
    time = null
  }

  return time
}
const getOneWeek = (val: any) => {
  let str = ''
  let date = [val.split(',')[0]]
  if (!val) {
    str = ''
  }
  else {
    for (let i of weekdays.value) {
      for (let j of date) {
        if (i.id == j) {
          if (date.indexOf(j) < date.length - 1) {
            str += i.name + "、"
          } else {
            str += i.name;
          }
        }
      }
    }
  }
  return str
}
const getSignDate = (val: any) => {
  let str = ''
  let date = val.split(',')
  if (!val) {
    str = ''
  }
  else {
    for (let i of weekdays.value) {
      for (let j of date) {
        if (i.id == j) {
          if (date.indexOf(j) < date.length - 1) {
            str += i.name + "、"
          } else {
            str += i.name;
          }
        }
      }
    }
  }
  return str
}
const handlePersonCenter = () => {
  showSignModal.value = false
  const href = `${window.location.origin}/zhxy/#/oneselfCenter/personalData`
  // const href = `http://192.168.38.218/zhxy/#/oneselfCenter/personalData`
  window.open(href, "_blank");

}

const openModel = async (item: any) => {
  chooseSignName.value = item.resourceName
  signBaseId.value = item.signTaskBaseId
  signResourceId.value = item.eduSignResourceId
  // checkSign
  // activitySign
  let params = {
    signBaseId: item.signTaskBaseId,
    signResourceId: item.eduSignResourceId
  }
  checkSign(params).then((res: any) => {
    console.log(res.data)
    if (res.data.code == 0 && res.data.success) {

      if (!res.data.data.isSign) {
        if (res.data.data.phone && res.data.data.idCard) {
          console.log('发接口')
          let params1 = {

            signBaseId: item.signTaskBaseId,
            signResourceId: item.eduSignResourceId
          }
          activitySign(params1).then((res: any) => {
            if (res.data.code == 0 && res.data.success) {
              message.success('报名成功')
              activityName.value = null
              signType.value = null
              getList()
            }
          })
        }
        else {
          showSignModal.value = true
        }
      }
      else {
        otherSignResourceId.value = res.data.data.otherSignResourceId
        clashName.value = res.data.data.otherSignResourceName
        Modal.confirm({
          centered: true,
          title: createVNode(
            "div",
            { style: { "font-weight": "bold" } },
            "提示"
          ),
          content: `同一时间已报名【${clashName.value}】，继续报名【${item.resourceName}】，将会取消之前的报名`,
          icon: createVNode(ExclamationCircleOutlined),
          okText: "确认",
          cancelText: "取消",
          onCancel() {
            console.log('close')
          },
          onOk: () => {
            let params2 = {
              otherSignResourceId: otherSignResourceId.value,
              signBaseId: item.signTaskBaseId,
              signResourceId: item.eduSignResourceId
            }
            activitySign(params2).then((res: any) => {
              if (res.data.code == 0 && res.data.success) {
                message.success('报名成功')
                activityName.value = null
                signType.value = null
                getList()
              }
              else {
                message.error(res.data.msg)
              }
            })

          },
        });
      }
    }
  })
}

const getList = async () => {
  const res: any = await signActiveList({
    name: activityName.value,
    activityType: signType.value,
    pageNo: pagData.pageNo,
    pageSize: pagData.pageSize
  })
  if (res.data.success) {
    list.value = res.data.data || []
    pagData.total = Number(res.data.totalDatas)
  }
}
const handlePageChange = (pageNo: any,) => {
  pagData.pageNo = pageNo;
  getList();
};

const getImg = (type: any) => {
  let url = ''
  if (type == 1) {
    url = activeImg
  } else if (type == 2) {
    url = trianImg
  } else {
    url = meetImg
  }
  return url
}

const onSearch = () => {
  pagData.pageNo = 1
  getList();
}
const handleChange = () => {
  pagData.pageNo = 1
  getList();
}
onMounted(() => {
  getList()
})
//#endregion


</script>

<style lang='scss' scoped>
.modalContent {
  .resourceName {
    height: 40px;
    line-height: 40px;
    background: #EBF4FF;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid rgba(0, 122, 255, 0.45);
    color: #262626;
    padding: 0 16px;
  }

  .footer {
    margin-top: 24px;
    text-align: center
  }
}

.activity-layout {
  width: 100%;
  background: #f0f2f5;
  min-height: calc(100vh - 60px);
  overflow: auto;

  .container {
    margin: 24px auto;
    width: 1280px;

    .search_header {
      margin-top: 16px;
      padding: 24px;
      height: 84px;
      background: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      align-items: center;

      .searchItem {
        display: flex;
        align-items: center
      }

      .ml-27 {
        margin-left: 27px;
      }
    }

    .activity-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .popoverRule {
        border: 1px solid red;
        width: 315px;

        .allrule {
          max-height: 300px;
          overflow: auto;

          .ruleItem {
            .week {
              font-weight: bold;
            }

            .timeDates {
              margin-top: 12px;
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              width: 295px;

              .timeDatesItem {
                width: 89px;
                height: 28px;
                background: #F7F7F7;
                border-radius: 2px 2px 2px 2px;
                border: 1px solid #D9D9D9;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 8px;
                margin-bottom: 12px
              }
            }
          }
        }

      }

      .activityItem {
        width: calc(50% - 12px);
        margin-top: 24px;
        // height: 208px;
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;

        // padding: 16px;
        .item_top {
          padding: 16px;
          display: flex;
          position: relative;
          width: 100%;

          // justify-content: space-between;
          // align-items: center;
          img {
            width: 120px;
          }

          .status {
            position: absolute;
            left: 16px;
            top: 16px;
            width: 55px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff
          }

          .status1 {
            background: #F53F3F;
          }

          .status2 {
            background: #86909C;
          }

          .item_top_right {
            width: calc(100% - 136px);
            // flex: 1;
            margin-left: 16px;

            .projectName {
              font-weight: bold;
              font-size: 16px;
              width: 100%;
            }

            .activityPeriod {
              margin-top: 12px;

              span {
                &:first-child {
                  color: #8C8C8C
                }
              }
            }

            .signPeriod {
              display: flex;
              align-items: center;
            }
          }
        }

        .item_bottom {
          border-top: 1px solid #F5F5F5;
          height: 56px;
          display: flex;
          align-items: center;
          padding: 0 16px;
          justify-content: space-between;

          .bottom_left {
            background: rgba(0, 0, 0, 0.03);
            border-radius: 2px;
            color: #595959;
            padding: 0 8px
          }

          .bottom_right {
            color: #8C8C8C;

            .abutton {
              margin-left: 16px;
            }
          }
        }
      }
    }

    .noempty {
      margin-top: 24px;
      height: 500px;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: bold
    }
  }
}
</style>
<style lang="scss">
.signModal {
  .ant-modal-body {
    padding: 0;

    .modalContent {
      padding: 24px 40px;
    }
  }

}
</style>