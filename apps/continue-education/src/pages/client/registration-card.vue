<template>
  <div class="opa" :class="{ scroll: !showPrint }">
    <div class="registration-card" v-show="!showPrint">
      <div class="card_head">
        <div class="left">
          <span class="cursor" @click="router.back()" style="color: #8c8c8c"
            >首页&nbsp;/&nbsp;</span
          >
          <span
            >{{
              (router.currentRoute.value.query.type as any) == 1
                ? "学时"
                : "学分"
            }}登记卡</span
          >
        </div>
        <!-- <div class="right" @click="printHtml" v-if="list.length > 1">
          <ysIcon type="icondayinji" />
          <span>打印登记卡</span>
        </div> -->
      </div>
      <div class="card_table">
        <div class="content-search">
          <span class="left"
            >{{
              (router.currentRoute.value.query.type as any) == 1
                ? "学时"
                : "学分"
            }}登记卡</span
          >
          <div class="right">
            <a-select
              v-model:value="searchType"
              style="width: 240px"
              @change="changeList"
            >
              <a-select-option :value="0">全部</a-select-option>
              <a-select-option :value="1">只查灵活性学时</a-select-option>
              <a-select-option :value="2">只查规范性学时</a-select-option>
            </a-select>
            <a-button style="margin-left: 16px" @click="printHtml"
              >打印</a-button
            >
            <a-button
              style="margin-left: 16px"
              type="primary"
              @click="downloadExcel"
              >导出</a-button
            >
          </div>
        </div>
        <div class="content_head">
          <div>
            <span>学校：{{ info.schoolName }}</span>
          </div>
          <div>
            <span>姓名：{{ info.userName }}</span>
          </div>
          <div>
            <span>性别：{{ info.sex }}</span>
          </div>
          <div>
            <span>身份证号：{{ infoHidden(2, info.cardNumber) }}</span>
          </div>
        </div>
        <div class="listCard" v-if="list.length > 1">
          <a-table
            :columns="columns"
            :pagination="false"
            :dataSource="list"
            bordered
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex == 'trainingCategory'">
                {{ record.trainingCategory }}
              </template>
              <template v-if="column.dataIndex == 'resourceName'">
                {{ record.resourceName }}
              </template>
              <template v-if="column.dataIndex == 'studyAddress'">
                {{ record.studyAddress }}
              </template>
              <template v-if="column.dataIndex == 'trainLevel'">
                {{ getTrian(record.trainLevel) }}
              </template>
              <template v-if="column.dataIndex == 'joinRole'">
                {{
                  record.joinRole == 1
                    ? "主讲人"
                    : record.joinRole == 2
                      ? "学员"
                      : ""
                }}
              </template>
              <template v-if="column.dataIndex == 'studyTime'">
                <span v-if="record.studyTime && record.studyEndTime"
                  >{{ record.studyTime }}~{{ record.studyEndTime }}</span
                >
              </template>
              <template v-if="column.dataIndex == 'classHour'">
                {{ record.classHour }}
              </template>
              <template v-if="column.dataIndex == 'classScore'">
                {{ record.classScore }}
              </template>
              <template v-if="column.dataIndex == 'totalNum'">
                {{ record.totalNum }}
              </template>
            </template>
          </a-table>
        </div>
        <div v-else class="nolist">
          <img src="@/assets/image/noempty.png" alt="" />
          <span>暂无内容</span>
        </div>
      </div>
    </div>
    <div class="mask" v-if="showPrint">
      <printCard id="printdiv1" :searchType="searchType" />
    </div>
  </div>
</template>
<script setup lang="ts">
import printCard from "@/components/printCard.vue";
import { infoHidden } from "@/utils";
import { ysIcon } from "@ys/ui";
import { useRouter } from "vue-router";
import { ref, onMounted, nextTick, onUnmounted } from "vue";
import { registrationForm, exportRegistrationForm } from "@/request";
const router = useRouter();
const list = ref<any>([]);
const searchType = ref<any>(0);
onMounted(() => {
  type.value = router.currentRoute.value.query.type;
  if (type.value == 2) {
    for (let i of columns.value) {
      if (i.key == 7) {
        i.title = "折算学分";
        i.dataIndex = "classScore";
      }
    }
  }
  getinfo();
  window.addEventListener("afterprint", setPrintHide);
});
const type = ref<any>("");

onUnmounted(() => {
  window.removeEventListener("afterprint", setPrintHide);
});

function setPrintHide() {
  showPrint.value = false;
  const headDom = document.querySelector(".head") as any;
  headDom.style.opacity = "1";
}

const showPrint = ref(false);

const print1 = () => {
  window.print();
};
const changeList = (val: any) => {
  console.log(val);
  getinfo();
};
const downloadExcel = async () => {
  const time = router.currentRoute.value.query.time as any;
  const start = time.split("~")[0];
  const end = time.split("~")[1];
  const start2 = start.slice(0, 7);
  const end2 = end.slice(0, 7);
  let params = {
    startTime: start2,
    endTime: end2,
    exportType: type.value,
    dataType: searchType.value == 0 ? null : searchType.value,
  };
  const res = (await exportRegistrationForm(params)) as any;
  let name = type.value == 1 ? "学时登记卡.xlsx" : "学分登记卡.xlsx";
  let blob = new Blob([res.data], { type: "application/vnd.ms-execl" });
  let link = window.URL.createObjectURL(blob);
  let a = document.createElement("a");
  document.body.appendChild(a);
  a.download = name;
  a.href = link;
  a.click();
  window.URL.revokeObjectURL(link);
  document.body.removeChild(a);
};

const printHtml = () => {
  showPrint.value = true;
  const headDom = document.querySelector(".head") as any;
  headDom.style.opacity = "0";
  setTimeout(() => {
    print1();
  }, 800);
};
function getKindsNum(val: any) {
  let num = 0;
  for (let i of val) {
    for (let j of val) {
      if (i.trainingCategory == j.trainingCategory) {
        num += Number(type.value == 1 ? j.classHour : j.classScore);
        i.totalNum = num.toFixed(1);
      } else {
        num = 0;
      }
    }
  }
}
function getAllNum(val: any) {
  let num = 0;
  for (let i of val) {
    num += Number(type.value == 1 ? i.classHour : i.classScore);
    if (i.trainingCategory == "合计") {
      i.totalNum = num.toFixed(1);
    }
  }
}
function getTrian(val: any) {
  let str = "";
  str =
    val == 1
      ? "国家级"
      : val == 2
        ? "省级"
        : val == 3
          ? "市级"
          : val == 4
            ? "县级"
            : val == 5
              ? "校级"
              : val == 6
                ? "其他"
                : "";
  return str;
}
// 基础信息
const info = ref<any>({
  schoolName: "",
  userName: "",
  sex: "",
  cardNumber: "",
});
async function getinfo() {
  list.value = [];
  const time = router.currentRoute.value.query.time as any;
  const start = time.split("~")[0];
  const end = time.split("~")[1];
  // const start1 = start.slice(0, 4);
  // const end1 = end.slice(0, 4);
  const start2 = start.slice(0, 7);
  const end2 = end.slice(0, 7);

  let params = {
    startTime: start2,
    endTime: end2,
    dataType: searchType.value == 0 ? null : searchType.value,
    sourceType: type.value == 1 ? null : 1,
  };
  const res = (await registrationForm(params)) as any;
  if (res.data.data) {
    let lh = [];
    let gf = [];
    info.value.schoolName = res.data.data.displayName;
    info.value.userName = res.data.data.userName;
    info.value.sex = res.data.data.sex == 0 ? "男" : "女";
    info.value.cardNumber = res.data.data.idCard;
    if (res.data.data.lhList && res.data.data.lhList.length > 0) {
      lh = res.data.data.lhList.map((item: any) => {
        return { trainingCategory: "灵活性培训", totalNum: "", ...item };
      });
    }
    if (res.data.data.gfList && res.data.data.gfList.length > 0) {
      gf = res.data.data.gfList.map((item: any) => {
        return { trainingCategory: "规范性培训", totalNum: "", ...item };
      });
    }
    list.value = [...lh, ...gf];
    let a: any = "";
    for (let i of list.value) {
      // a = i.studyTime.split(" ")[1].split(":").splice(0, 2);
      // i.studyTime = i.studyTime.split(" ")[0] + " " + a.join(":");
      // i.studyTime = i.studyTime.split(" ")[0]
      i.studyEndTime = i.studyEndTime.split(" ")[0];
    }
    let obj = {
      trainingCategory: "合计",
      resourceName: "",
      studyAddress: "",
      studyTime: "",
      classHour: "",
      classScore: "",
      totalNum: "",
    };
    list.value.push(obj);
    getKindsNum(list.value);
    getAllNum(list.value);
  }
}

const columns = ref<any>([
  {
    key: 1,
    title: "培训类别",
    dataIndex: "trainingCategory",
    width: "168px",
    customCell: (record: any, rowIndex: any, column: any) => {
      return mergeCell(record, rowIndex, column);
    },
  },
  {
    key: 2,
    title: "培训项目",
    dataIndex: "resourceName",
    width: "200px",
    ellipsis: true,
  },
  {
    key: 3,
    title: "学习地点",
    dataIndex: "studyAddress",
    width: "200px",
    ellipsis: true,
  },
  { key: 4, title: "培训级别", dataIndex: "trainLevel" },
  { key: 5, title: "角色参与", dataIndex: "joinRole" },
  { key: 6, title: "学习时间", dataIndex: "studyTime", width: "232px" },
  { key: 7, title: "折算学时", dataIndex: "classHour", width: "104px" },
  {
    key: 8,
    title: "合计",
    dataIndex: "totalNum",
    width: "104px",
    customCell: (record: any, rowIndex: any, column: any) => {
      return mergeCell(record, rowIndex, column);
    },
  },
]);

function mergeCell(record: any, rowIndex: any, column: any) {
  const temp_Index = list.value.findIndex(
    (item: any) => item.trainingCategory === record.trainingCategory
  );
  let rowSpan = list.value.filter(
    (item2: any) => item2.trainingCategory === record.trainingCategory
  ).length;
  if (rowSpan > 1) {
    if (temp_Index === rowIndex) {
      return {
        rowSpan: rowSpan,
      };
    } else {
      return {
        rowSpan: 0,
      };
    }
  }
  return column;
}
</script>
<style lang="scss" scoped>
@page {
  size: auto;
  margin: 0mm;
}
@media print {
  thead {
    display: table-header-group;
  }
  tfoot {
    display: table-footer-group;
  }
}
.mask {
  display: flex;
  align-items: center;
  justify-content: center;
  // position: fixed;
  // left: 0;
  // top: 0;
  // bottom: 0;
  // right: 0;
  width: 100%;
  // height: 100vh;
  // overflow: auto;
  background: #fff;
  z-index: 999;
}

.opa {
  width: 100vw;
  background: #f0f2f5;
  overflow: auto;

  &.scroll {
    height: calc(100vh - 60px);

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: rgba(161, 168, 184, 0.3);
    }
  }

  .registration-card {
    max-width: 1280px;
    margin: 0 auto;
    padding: 24px;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    background: #f0f2f5;

    .card_head {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .right {
        span {
          margin-left: 4px;
        }

        &:hover {
          color: #007aff;
          cursor: pointer;
        }
      }
    }

    .card_table {
      margin-top: 16px;
      max-width: 1280px;
      background: #ffffff;
      padding: 20px 24px;

      .content-search {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
          font-weight: bold;
        }
      }

      .content_head {
        margin-top: 20px;
        max-width: 1280px;
        height: 64px;
        background: #f7f7f7;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 0 24px;

        div {
          width: 25%;
        }
      }

      .listCard {
        margin-top: 16px;
      }

      .nolist {
        height: 500px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        span {
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
