<template>
    <div class="detauls_container scrollbars flex1" >
        <p class="head_p">详细统计</p>
        <div>
            <SearchHeader class="mb24" @searchCondition="handleSearchCondition" />
        </div>
        <div class="content bcf">
            <DetailTable :list="tableList" @changeSorter="changeSorter" />
            <div class="pagation">
                <ys-pagination @change="onPaginationChange" :pageNo="pageNo" :pageSize="pageSize" :total="total" />
            </div>
        </div>
        <loading v-if="searchLoading" />
    </div>
</template>
<script lang="ts" setup>
import SearchHeader from "./common/SearchHeader.vue"
import DetailTable from "./common/DetailTable.vue"
import ysPagination from "./../pagations/index.vue"
import { ref, onMounted } from "vue"
import {statSimpleDataByType} from "@/request/setting"
import loading from "@/components/loading.vue";
function handleSearchCondition(val: any) {
    searchName.value = val.searchName
    startTime.value = val.rangeDate ? val.rangeDate[0] : null
    endTime.value = val.rangeDate ? val.rangeDate[1] : null
    getTablList()
}
const searchLoading = ref<boolean>(true)
const tableList = ref<any>([])
const searchName = ref('')
const startTime = ref('')
const endTime = ref('')
const pageNo = ref<any>(1)
const pageSize = ref<any>(10)
const total = ref<any>(1)


function onPaginationChange(page: any, pagesize: any) {
    pageNo.value = page
    pageSize.value = pagesize
    getTablList()
}
function getTablList() {   
     searchLoading.value = true
    let params = {
        endTime: endTime.value,
        eventName: searchName.value,
        orderType: sorterType.value,
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        startTime: startTime.value
    }
    statSimpleDataByType(params)
        .then((res: any) => {
            if (res.data.code == 0 && res.data.success) {
                tableList.value = res.data.data
                total.value = Number(res.data.totalDatas)
            }
            searchLoading.value = false
        })
   
}
const sorterType = ref(0)
function changeSorter(val: any) {
    sorterType.value = val
    getTablList()
}
onMounted(() => {
    getTablList()
})
</script>
<style lang="scss">
.detauls_container {
    width: 100%;
    min-height: 100%;
    padding: 16px 24px 36px;
    background: #f0f2f5;
    box-sizing: border-box;

    .content {
        //    height: 200px; 
        padding: 24px;
    }
}
</style>